{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule, NbCardModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/space.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction SpacePickerComponent_nb_icon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-icon\", 27);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_nb_icon_9_Template_nb_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.searchKeyword = \"\";\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_nb_icon_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-icon\", 27);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_nb_icon_15_Template_nb_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.searchLocation = \"\";\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_nb_icon_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 28);\n  }\n}\nfunction SpacePickerComponent_nb_icon_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 29);\n  }\n}\nfunction SpacePickerComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"input\", 31);\n    i0.ɵɵlistener(\"change\", function SpacePickerComponent_div_27_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 32);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.allSelected);\n  }\n}\nfunction SpacePickerComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_32_Template_div_click_0_listener() {\n      const space_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSpaceSelection(space_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r6.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r6.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r6.CLocation || \"-\");\n  }\n}\nfunction SpacePickerComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"nb-icon\", 39);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"ngx-pagination\", 41);\n    i0.ɵɵtwoWayListener(\"PageChange\", function SpacePickerComponent_div_34_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pageIndex, $event) || (ctx_r1.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function SpacePickerComponent_div_34_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r1.pageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r1.pageSize)(\"CollectionSize\", ctx_r1.totalRecords);\n  }\n}\nfunction SpacePickerComponent_div_35_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_35_span_5_Template_button_click_2_listener() {\n      const space_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeSelectedSpace(space_r9));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r9.CPart, \" \");\n  }\n}\nfunction SpacePickerComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"h6\", 43);\n    i0.ɵɵelement(2, \"nb-icon\", 44);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtemplate(5, SpacePickerComponent_div_35_span_5_Template, 3, 1, \"span\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u7A7A\\u9593 (\", ctx_r1.selectedItems.length, \" \\u9805) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedItems);\n  }\n}\nexport class SpacePickerComponent {\n  constructor(spaceService) {\n    this.spaceService = spaceService;\n    this.selectedItems = [];\n    this.multiple = true;\n    this.placeholder = '請選擇空間';\n    this.selectionChange = new EventEmitter();\n    // 搜尋相關屬性\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.isLoading = false;\n    // 分頁相關屬性\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.totalRecords = 0;\n    // 資料相關屬性\n    this.availableSpaces = [];\n    this.allSelected = false;\n  }\n  ngOnInit() {\n    this.loadAvailableSpaces();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    this.isLoading = true;\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this.spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n        this.updateAllSelectedState();\n      }\n      this.isLoading = false;\n    })).subscribe({\n      next: () => {},\n      error: () => {\n        this.isLoading = false;\n      }\n    });\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 切換空間選擇\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({\n            ...space\n          });\n        } else {\n          this.selectedItems = [{\n            ...space\n          }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 全選/取消全選\n  toggleAllSpaces() {\n    this.allSelected = !this.allSelected;\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({\n            ...space\n          });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 移除已選空間\n  removeSelectedSpace(space) {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 更新全選狀態\n  updateAllSelectedState() {\n    this.allSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page) {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 計算總頁數\n  get totalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  static {\n    this.ɵfac = function SpacePickerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpacePickerComponent)(i0.ɵɵdirectiveInject(i1.SpaceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpacePickerComponent,\n      selectors: [[\"app-space-picker\"]],\n      inputs: {\n        selectedItems: \"selectedItems\",\n        multiple: \"multiple\",\n        placeholder: \"placeholder\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 20,\n      consts: [[1, \"space-picker-container\"], [1, \"search-section\"], [1, \"search-form\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"row\", \"g-2\", \"align-items-end\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"form-label\", \"mb-1\", \"text-muted\", \"small\"], [\"type\", \"text\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [\"nbSuffix\", \"\", \"icon\", \"close-outline\", \"class\", \"cursor-pointer text-muted\", \"title\", \"\\u6E05\\u9664\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"text\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [1, \"col-lg-4\", \"col-md-2\"], [1, \"search-actions\", \"d-flex\", \"gap-1\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", \"title\", \"\\u91CD\\u7F6E\", 3, \"click\", \"disabled\"], [\"icon\", \"refresh-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", \"size\", \"small\", \"title\", \"\\u641C\\u5C0B\", 3, \"click\", \"disabled\"], [\"icon\", \"search-outline\", 4, \"ngIf\"], [\"icon\", \"loader-outline\", \"class\", \"spinning\", 4, \"ngIf\"], [1, \"d-none\", \"d-lg-inline\", \"ms-1\"], [1, \"space-list-section\", \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"font-weight-bold\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"selected-summary mt-3 p-3 bg-light border rounded\", 4, \"ngIf\"], [\"nbSuffix\", \"\", \"icon\", \"close-outline\", \"title\", \"\\u6E05\\u9664\", 1, \"cursor-pointer\", \"text-muted\", 3, \"click\"], [\"icon\", \"search-outline\"], [\"icon\", \"loader-outline\", 1, \"spinning\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", \"id\", \"selectAllSpaces\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllSpaces\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"font-weight-bold\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"selected-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"mb-2\"], [\"icon\", \"checkmark-circle-outline\", 1, \"text-success\", \"me-2\"], [1, \"selected-spaces-list\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", \"aria-label\", \"\\u79FB\\u9664\", 1, \"btn-close\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"]],\n      template: function SpacePickerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n          i0.ɵɵtext(6, \"\\u9805\\u76EE\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"nb-form-field\")(8, \"input\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_8_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, SpacePickerComponent_nb_icon_9_Template, 1, 0, \"nb-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 4)(11, \"label\", 5);\n          i0.ɵɵtext(12, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"nb-form-field\")(14, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_14_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, SpacePickerComponent_nb_icon_15_Template, 1, 0, \"nb-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 9)(17, \"div\", 10)(18, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_18_listener() {\n            return ctx.onReset();\n          });\n          i0.ɵɵelement(19, \"nb-icon\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_20_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵtemplate(21, SpacePickerComponent_nb_icon_21_Template, 1, 0, \"nb-icon\", 14)(22, SpacePickerComponent_nb_icon_22_Template, 1, 0, \"nb-icon\", 15);\n          i0.ɵɵelementStart(23, \"span\", 16);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(25, \"div\", 17)(26, \"div\", 18);\n          i0.ɵɵtemplate(27, SpacePickerComponent_div_27_Template, 4, 1, \"div\", 19)(28, SpacePickerComponent_div_28_Template, 2, 0, \"div\", 20);\n          i0.ɵɵelementStart(29, \"small\", 21);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 22);\n          i0.ɵɵtemplate(32, SpacePickerComponent_div_32_Template, 6, 4, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, SpacePickerComponent_div_33_Template, 3, 0, \"div\", 24)(34, SpacePickerComponent_div_34_Template, 2, 3, \"div\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, SpacePickerComponent_div_35_Template, 6, 2, \"div\", 26);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchLocation);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isLoading ? \"\\u641C\\u5C0B...\" : \"\\u641C\\u5C0B\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx.totalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx.pageIndex, \" / \", ctx.totalPages, \" \\u9801 \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableSpaces);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableSpaces.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbFormFieldModule, i4.NbFormFieldComponent, i4.NbSuffixDirective, NbInputModule, i4.NbInputDirective, NbButtonModule, i4.NbButtonComponent, NbIconModule, i4.NbIconComponent, NbCardModule, PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  border: 1px solid #e4e7ea;\\n  border-radius: 6px;\\n  background-color: #f8f9fa;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: #6c757d;\\n  margin-bottom: 0.25rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  height: 32px;\\n  border-radius: 4px;\\n  border: 1px solid #dee2e6;\\n  background-color: #ffffff;\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.1);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled {\\n  background-color: #f8f9fa;\\n  border-color: #e9ecef;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #adb5bd;\\n  font-size: 0.8rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbSuffix][_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.6;\\n  transition: opacity 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbSuffix][_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 32px;\\n  border-radius: 4px;\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  transition: all 0.15s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[ghost][_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  background-color: #ffffff;\\n  color: #6c757d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[ghost][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  border-color: #007bff;\\n  color: #007bff;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=primary][_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=primary][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   nb-icon.spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 12px;\\n  margin-bottom: 1rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: 2px solid #e4e7ea;\\n  border-radius: 8px;\\n  background-color: #ffffff;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #2c3e50;\\n  margin-bottom: 4px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #7f8c8d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #4a90e2;\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);\\n  transform: translateY(-1px);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #28a745;\\n  background-color: #f8fff9;\\n  box-shadow: 0 2px 12px rgba(40, 167, 69, 0.2);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.8rem;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 20px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: white;\\n  opacity: 0.8;\\n  cursor: pointer;\\n  padding: 0;\\n  margin: 0;\\n  width: 12px;\\n  height: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u00D7\\\";\\n  font-size: 12px;\\n  line-height: 1;\\n}\\n@media (max-width: 1200px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%], \\n   .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-5[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem !important;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: inline !important;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbFormFieldModule", "NbInputModule", "NbButtonModule", "NbIconModule", "NbCardModule", "PaginationComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpacePickerComponent_nb_icon_9_Template_nb_icon_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "searchKeyword", "ɵɵresetView", "onSearch", "ɵɵelementEnd", "SpacePickerComponent_nb_icon_15_Template_nb_icon_click_0_listener", "_r3", "searchLocation", "ɵɵelement", "SpacePickerComponent_div_27_Template_input_change_1_listener", "_r4", "toggleAllSpaces", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "allSelected", "SpacePickerComponent_div_32_Template_div_click_0_listener", "space_r6", "_r5", "$implicit", "toggleSpaceSelection", "ɵɵclassProp", "selected", "ɵɵtextInterpolate", "<PERSON>art", "CLocation", "ɵɵtwoWayListener", "SpacePickerComponent_div_34_Template_ngx_pagination_PageChange_1_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "pageIndex", "onPageChange", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "SpacePickerComponent_div_35_span_5_Template_button_click_2_listener", "space_r9", "_r8", "removeSelectedSpace", "ɵɵtextInterpolate1", "ɵɵtemplate", "SpacePickerComponent_div_35_span_5_Template", "selectedItems", "length", "SpacePickerComponent", "constructor", "spaceService", "multiple", "placeholder", "selectionChange", "isLoading", "availableSpaces", "ngOnInit", "loadAvailableSpaces", "request", "CStatus", "PageIndex", "PageSize", "apiSpaceGetSpaceListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CSpaceID", "some", "s", "TotalItems", "updateAllSelectedState", "subscribe", "next", "error", "onReset", "space", "push", "for<PERSON>ach", "filter", "emit", "availableSpace", "find", "every", "page", "totalPages", "Math", "ceil", "ɵɵdirectiveInject", "i1", "SpaceService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpacePickerComponent_Template", "rf", "ctx", "SpacePickerComponent_Template_input_ngModelChange_8_listener", "SpacePickerComponent_Template_input_keyup_enter_8_listener", "SpacePickerComponent_nb_icon_9_Template", "SpacePickerComponent_Template_input_ngModelChange_14_listener", "SpacePickerComponent_Template_input_keyup_enter_14_listener", "SpacePickerComponent_nb_icon_15_Template", "SpacePickerComponent_Template_button_click_18_listener", "SpacePickerComponent_Template_button_click_20_listener", "SpacePickerComponent_nb_icon_21_Template", "SpacePickerComponent_nb_icon_22_Template", "SpacePickerComponent_div_27_Template", "SpacePickerComponent_div_28_Template", "SpacePickerComponent_div_32_Template", "SpacePickerComponent_div_33_Template", "SpacePickerComponent_div_34_Template", "SpacePickerComponent_div_35_Template", "ɵɵtextInterpolate3", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbFormFieldComponent", "NbSuffixDirective", "NbInputDirective", "NbButtonComponent", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule, NbCardModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { SpaceService } from 'src/services/api/services/space.service';\nimport { GetSpaceListResponse } from 'src/services/api/models';\nimport { tap } from 'rxjs/operators';\n\nexport interface SpacePickerItem {\n  CSpaceID: number;\n  CPart: string;\n  CLocation?: string | null;\n  selected?: boolean;\n}\n\n@Component({\n  selector: 'app-space-picker',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbFormFieldModule,\n    NbInputModule,\n    NbButtonModule,\n    NbIconModule,\n    NbCardModule,\n    PaginationComponent\n  ],\n  templateUrl: './space-picker.component.html',\n  styleUrls: ['./space-picker.component.scss']\n})\nexport class SpacePickerComponent implements OnInit {\n  @Input() selectedItems: SpacePickerItem[] = [];\n  @Input() multiple: boolean = true;\n  @Input() placeholder: string = '請選擇空間';\n  @Output() selectionChange = new EventEmitter<SpacePickerItem[]>();\n\n  // 搜尋相關屬性\n  searchKeyword: string = '';\n  searchLocation: string = '';\n  isLoading: boolean = false;\n\n  // 分頁相關屬性\n  pageIndex = 1;\n  pageSize = 10;\n  totalRecords = 0;\n\n  // 資料相關屬性\n  availableSpaces: SpacePickerItem[] = [];\n  allSelected = false;\n\n  constructor(private spaceService: SpaceService) { }\n\n  ngOnInit(): void {\n    this.loadAvailableSpaces();\n  }\n\n  // 載入可用空間列表\n  loadAvailableSpaces(): void {\n    this.isLoading = true;\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1, // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n\n    this.spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\n      tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID!,\n            CPart: item.CPart!,\n            CLocation: item.CLocation,\n            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n          this.updateAllSelectedState();\n        }\n        this.isLoading = false;\n      })\n    ).subscribe({\n      next: () => {},\n      error: () => {\n        this.isLoading = false;\n      }\n    });\n  }\n\n  // 搜尋功能\n  onSearch(): void {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 重置搜尋\n  onReset(): void {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 切換空間選擇\n  toggleSpaceSelection(space: SpacePickerItem): void {\n    space.selected = !space.selected;\n\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({ ...space });\n        } else {\n          this.selectedItems = [{ ...space }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 全選/取消全選\n  toggleAllSpaces(): void {\n    this.allSelected = !this.allSelected;\n\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({ ...space });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 移除已選空間\n  removeSelectedSpace(space: SpacePickerItem): void {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 更新全選狀態\n  updateAllSelectedState(): void {\n    this.allSelected = this.availableSpaces.length > 0 &&\n      this.availableSpaces.every(space => space.selected);\n  }\n\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page: number): void {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n\n  // 計算總頁數\n  get totalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n}\n", "<!-- 空間選擇器共用組件 -->\n<div class=\"space-picker-container\">\n  <!-- 搜尋區域 -->\n  <div class=\"search-section\">\n    <div class=\"search-form p-3 bg-light border rounded\">\n      <div class=\"row g-2 align-items-end\">\n        <div class=\"col-lg-4 col-md-5\">\n          <label class=\"form-label mb-1 text-muted small\">項目名稱</label>\n          <nb-form-field>\n            <input type=\"text\" nbInput size=\"small\" placeholder=\"輸入項目名稱\" [(ngModel)]=\"searchKeyword\"\n              (keyup.enter)=\"onSearch()\" [disabled]=\"isLoading\" />\n            <nb-icon *ngIf=\"searchKeyword\" nbSuffix icon=\"close-outline\" class=\"cursor-pointer text-muted\"\n              (click)=\"searchKeyword = ''; onSearch()\" title=\"清除\"></nb-icon>\n          </nb-form-field>\n        </div>\n        <div class=\"col-lg-4 col-md-5\">\n          <label class=\"form-label mb-1 text-muted small\">所屬區域</label>\n          <nb-form-field>\n            <input type=\"text\" nbInput size=\"small\" placeholder=\"輸入所屬區域\" [(ngModel)]=\"searchLocation\"\n              (keyup.enter)=\"onSearch()\" [disabled]=\"isLoading\" />\n            <nb-icon *ngIf=\"searchLocation\" nbSuffix icon=\"close-outline\" class=\"cursor-pointer text-muted\"\n              (click)=\"searchLocation = ''; onSearch()\" title=\"清除\"></nb-icon>\n          </nb-form-field>\n        </div>\n        <div class=\"col-lg-4 col-md-2\">\n          <div class=\"search-actions d-flex gap-1\">\n            <button nbButton ghost size=\"small\" (click)=\"onReset()\" [disabled]=\"isLoading\" title=\"重置\">\n              <nb-icon icon=\"refresh-outline\"></nb-icon>\n            </button>\n            <button nbButton status=\"primary\" size=\"small\" (click)=\"onSearch()\" [disabled]=\"isLoading\" title=\"搜尋\">\n              <nb-icon *ngIf=\"!isLoading\" icon=\"search-outline\"></nb-icon>\n              <nb-icon *ngIf=\"isLoading\" icon=\"loader-outline\" class=\"spinning\"></nb-icon>\n              <span class=\"d-none d-lg-inline ms-1\">{{ isLoading ? '搜尋...' : '搜尋' }}</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 空間列表區域 -->\n  <div class=\"space-list-section border rounded p-3\" style=\"background-color: #f8f9fa;\">\n    <!-- 列表標題和統計 -->\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\n      <div class=\"d-flex align-items-center\" *ngIf=\"multiple\">\n        <input type=\"checkbox\" id=\"selectAllSpaces\" [checked]=\"allSelected\" (change)=\"toggleAllSpaces()\" class=\"me-2\">\n        <label for=\"selectAllSpaces\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\n      </div>\n      <div *ngIf=\"!multiple\" class=\"font-weight-bold\">\n        選擇空間\n      </div>\n      <small class=\"text-muted\">\n        共 {{ totalRecords }} 筆，第 {{ pageIndex }} / {{ totalPages }} 頁\n      </small>\n    </div>\n\n    <!-- 空間項目網格 -->\n    <div class=\"space-grid\">\n      <div class=\"space-item\" *ngFor=\"let space of availableSpaces\" [class.selected]=\"space.selected\"\n        (click)=\"toggleSpaceSelection(space)\">\n        <div class=\"space-card\">\n          <div class=\"space-name\">{{ space.CPart }}</div>\n          <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 空間列表為空時的提示 -->\n    <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\n      <nb-icon icon=\"info-outline\" class=\"me-2\"></nb-icon>\n      沒有符合條件的空間\n    </div>\n\n    <!-- 分頁控制 -->\n    <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"totalRecords > pageSize\">\n      <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\n        (PageChange)=\"onPageChange($event)\">\n      </ngx-pagination>\n    </div>\n  </div>\n\n  <!-- 已選空間摘要 -->\n  <div *ngIf=\"selectedItems.length > 0\" class=\"selected-summary mt-3 p-3 bg-light border rounded\">\n    <h6 class=\"mb-2\">\n      <nb-icon icon=\"checkmark-circle-outline\" class=\"text-success me-2\"></nb-icon>\n      已選空間 ({{ selectedItems.length }} 項)\n    </h6>\n    <div class=\"selected-spaces-list\">\n      <span *ngFor=\"let space of selectedItems\" class=\"badge badge-primary me-1 mb-1\">\n        {{ space.CPart }}\n        <button type=\"button\" class=\"btn-close ms-1\" (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"\n          aria-label=\"移除\">\n        </button>\n      </span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,QAAQ,gBAAgB;AAC7G,SAASC,mBAAmB,QAAQ,0DAA0D;AAG9F,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;ICIxBC,EAAA,CAAAC,cAAA,kBACsD;IAApDD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAD,MAAA,CAAAE,aAAA,GAAyB,EAAE;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAEH,MAAA,CAAAI,QAAA,EAAU;IAAA,EAAC;IAAYV,EAAA,CAAAW,YAAA,EAAU;;;;;;IAQhEX,EAAA,CAAAC,cAAA,kBACuD;IAArDD,EAAA,CAAAE,UAAA,mBAAAU,kEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAD,MAAA,CAAAQ,cAAA,GAA0B,EAAE;MAAA,OAAAd,EAAA,CAAAS,WAAA,CAAEH,MAAA,CAAAI,QAAA,EAAU;IAAA,EAAC;IAAYV,EAAA,CAAAW,YAAA,EAAU;;;;;IAS/DX,EAAA,CAAAe,SAAA,kBAA4D;;;;;IAC5Df,EAAA,CAAAe,SAAA,kBAA4E;;;;;;IAclFf,EADF,CAAAC,cAAA,cAAwD,gBACwD;IAA1CD,EAAA,CAAAE,UAAA,oBAAAc,6DAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAAUH,MAAA,CAAAY,eAAA,EAAiB;IAAA,EAAC;IAAhGlB,EAAA,CAAAW,YAAA,EAA8G;IAC9GX,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAmB,MAAA,2CAAM;IACnEnB,EADmE,CAAAW,YAAA,EAAQ,EACrE;;;;IAFwCX,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAgB,WAAA,CAAuB;;;;;IAGrEtB,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAmB,MAAA,iCACF;IAAAnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IAQNX,EAAA,CAAAC,cAAA,cACwC;IAAtCD,EAAA,CAAAE,UAAA,mBAAAqB,0DAAA;MAAA,MAAAC,QAAA,GAAAxB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAASH,MAAA,CAAAqB,oBAAA,CAAAH,QAAA,CAA2B;IAAA,EAAC;IAEnCxB,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAmB,MAAA,GAAiB;IAAAnB,EAAA,CAAAW,YAAA,EAAM;IAC/CX,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAmB,MAAA,GAA4B;IAE5DnB,EAF4D,CAAAW,YAAA,EAAM,EAC1D,EACF;;;;IANwDX,EAAA,CAAA4B,WAAA,aAAAJ,QAAA,CAAAK,QAAA,CAAiC;IAGnE7B,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAA8B,iBAAA,CAAAN,QAAA,CAAAO,KAAA,CAAiB;IACb/B,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA8B,iBAAA,CAAAN,QAAA,CAAAQ,SAAA,QAA4B;;;;;IAM9DhC,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAe,SAAA,kBAAoD;IACpDf,EAAA,CAAAmB,MAAA,+DACF;IAAAnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IAIJX,EADF,CAAAC,cAAA,cAAgF,yBAExC;IADtBD,EAAA,CAAAiC,gBAAA,wBAAAC,0EAAAC,MAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAqC,kBAAA,CAAA/B,MAAA,CAAAgC,SAAA,EAAAH,MAAA,MAAA7B,MAAA,CAAAgC,SAAA,GAAAH,MAAA;MAAA,OAAAnC,EAAA,CAAAS,WAAA,CAAA0B,MAAA;IAAA,EAAoB;IAClCnC,EAAA,CAAAE,UAAA,wBAAAgC,0EAAAC,MAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAAcH,MAAA,CAAAiC,YAAA,CAAAJ,MAAA,CAAoB;IAAA,EAAC;IAEvCnC,EADE,CAAAW,YAAA,EAAiB,EACb;;;;IAHYX,EAAA,CAAAoB,SAAA,EAAoB;IAApBpB,EAAA,CAAAwC,gBAAA,SAAAlC,MAAA,CAAAgC,SAAA,CAAoB;IAAuBtC,EAAtB,CAAAqB,UAAA,aAAAf,MAAA,CAAAmC,QAAA,CAAqB,mBAAAnC,MAAA,CAAAoC,YAAA,CAAgC;;;;;;IAa1F1C,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAmB,MAAA,GACA;IAAAnB,EAAA,CAAAC,cAAA,iBACkB;IAD2BD,EAAA,CAAAE,UAAA,mBAAAyC,oEAAA;MAAA,MAAAC,QAAA,GAAA5C,EAAA,CAAAI,aAAA,CAAAyC,GAAA,EAAAnB,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAASH,MAAA,CAAAwC,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAGnF5C,EADE,CAAAW,YAAA,EAAS,EACJ;;;;IAJLX,EAAA,CAAAoB,SAAA,EACA;IADApB,EAAA,CAAA+C,kBAAA,MAAAH,QAAA,CAAAb,KAAA,MACA;;;;;IAPJ/B,EADF,CAAAC,cAAA,cAAgG,aAC7E;IACfD,EAAA,CAAAe,SAAA,kBAA6E;IAC7Ef,EAAA,CAAAmB,MAAA,GACF;IAAAnB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAgD,UAAA,IAAAC,2CAAA,mBAAgF;IAOpFjD,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAVFX,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA+C,kBAAA,gCAAAzC,MAAA,CAAA4C,aAAA,CAAAC,MAAA,cACF;IAE0BnD,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAA4C,aAAA,CAAgB;;;ADxD9C,OAAM,MAAOE,oBAAoB;EAoB/BC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAnBvB,KAAAJ,aAAa,GAAsB,EAAE;IACrC,KAAAK,QAAQ,GAAY,IAAI;IACxB,KAAAC,WAAW,GAAW,OAAO;IAC5B,KAAAC,eAAe,GAAG,IAAInE,YAAY,EAAqB;IAEjE;IACA,KAAAkB,aAAa,GAAW,EAAE;IAC1B,KAAAM,cAAc,GAAW,EAAE;IAC3B,KAAA4C,SAAS,GAAY,KAAK;IAE1B;IACA,KAAApB,SAAS,GAAG,CAAC;IACb,KAAAG,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,CAAC;IAEhB;IACA,KAAAiB,eAAe,GAAsB,EAAE;IACvC,KAAArC,WAAW,GAAG,KAAK;EAE+B;EAElDsC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,IAAI,CAACH,SAAS,GAAG,IAAI;IACrB,MAAMI,OAAO,GAAG;MACd/B,KAAK,EAAE,IAAI,CAACvB,aAAa,IAAI,IAAI;MACjCwB,SAAS,EAAE,IAAI,CAAClB,cAAc,IAAI,IAAI;MACtCiD,OAAO,EAAE,CAAC;MAAE;MACZC,SAAS,EAAE,IAAI,CAAC1B,SAAS;MACzB2B,QAAQ,EAAE,IAAI,CAACxB;KAChB;IAED,IAAI,CAACa,YAAY,CAACY,6BAA6B,CAAC;MAAEC,IAAI,EAAEL;IAAO,CAAE,CAAC,CAACM,IAAI,CACrErE,GAAG,CAACsE,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACX,eAAe,GAAGU,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDC,QAAQ,EAAED,IAAI,CAACC,QAAS;UACxB3C,KAAK,EAAE0C,IAAI,CAAC1C,KAAM;UAClBC,SAAS,EAAEyC,IAAI,CAACzC,SAAS;UACzBH,QAAQ,EAAE,IAAI,CAACqB,aAAa,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKD,IAAI,CAACC,QAAQ;SACpE,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAChC,YAAY,GAAG2B,QAAQ,CAACQ,UAAU,IAAI,CAAC;QAC5C,IAAI,CAACC,sBAAsB,EAAE;MAC/B;MACA,IAAI,CAACpB,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH,CAACqB,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACAhD,QAAQA,CAAA;IACN,IAAI,CAAC4B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACuB,mBAAmB,EAAE;EAC5B;EAEA;EACAqB,OAAOA,CAAA;IACL,IAAI,CAAC1E,aAAa,GAAG,EAAE;IACvB,IAAI,CAACM,cAAc,GAAG,EAAE;IACxB,IAAI,CAACwB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACuB,mBAAmB,EAAE;EAC5B;EAEA;EACAlC,oBAAoBA,CAACwD,KAAsB;IACzCA,KAAK,CAACtD,QAAQ,GAAG,CAACsD,KAAK,CAACtD,QAAQ;IAEhC,IAAIsD,KAAK,CAACtD,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACqB,aAAa,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,CAAC,EAAE;QAChE,IAAI,IAAI,CAACnB,QAAQ,EAAE;UACjB,IAAI,CAACL,aAAa,CAACkC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAACjC,aAAa,GAAG,CAAC;YAAE,GAAGiC;UAAK,CAAE,CAAC;UACnC;UACA,IAAI,CAACxB,eAAe,CAAC0B,OAAO,CAACT,CAAC,IAAG;YAC/B,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,EAAE;cACjCE,CAAC,CAAC/C,QAAQ,GAAG,KAAK;YACpB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoC,MAAM,CAACV,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,CAAC;IACpF;IAEA,IAAI,CAACI,sBAAsB,EAAE;IAC7B,IAAI,CAACrB,eAAe,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAhC,eAAeA,CAAA;IACb,IAAI,CAACI,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACqC,eAAe,CAAC0B,OAAO,CAACF,KAAK,IAAG;MACnC,IAAI,IAAI,CAAC7D,WAAW,IAAI,CAAC6D,KAAK,CAACtD,QAAQ,EAAE;QACvCsD,KAAK,CAACtD,QAAQ,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,CAACqB,aAAa,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,CAAC,EAAE;UAChE,IAAI,CAACxB,aAAa,CAACkC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC7D,WAAW,IAAI6D,KAAK,CAACtD,QAAQ,EAAE;QAC9CsD,KAAK,CAACtD,QAAQ,GAAG,KAAK;QACtB,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoC,MAAM,CAACV,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,CAAC;MACpF;IACF,CAAC,CAAC;IAEF,IAAI,CAACjB,eAAe,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAJ,mBAAmBA,CAACqC,KAAsB;IACxC,IAAI,CAACjC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoC,MAAM,CAACV,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,CAAC;IAElF;IACA,MAAMc,cAAc,GAAG,IAAI,CAAC7B,eAAe,CAAC8B,IAAI,CAACb,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKS,KAAK,CAACT,QAAQ,CAAC;IACpF,IAAIc,cAAc,EAAE;MAClBA,cAAc,CAAC3D,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACiD,sBAAsB,EAAE;IAC7B,IAAI,CAACrB,eAAe,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;EACpD;EAEA;EACA4B,sBAAsBA,CAAA;IACpB,IAAI,CAACxD,WAAW,GAAG,IAAI,CAACqC,eAAe,CAACR,MAAM,GAAG,CAAC,IAChD,IAAI,CAACQ,eAAe,CAAC+B,KAAK,CAACP,KAAK,IAAIA,KAAK,CAACtD,QAAQ,CAAC;EACvD;EAEA;EACAU,YAAYA,CAACoD,IAAY;IACvB,IAAI,CAACrD,SAAS,GAAGqD,IAAI;IACrB,IAAI,CAAC9B,mBAAmB,EAAE;EAC5B;EAEA;EACA,IAAI+B,UAAUA,CAAA;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACpD,YAAY,GAAG,IAAI,CAACD,QAAQ,CAAC;EACrD;;;uCAnJWW,oBAAoB,EAAApD,EAAA,CAAA+F,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApB7C,oBAAoB;MAAA8C,SAAA;MAAAC,MAAA;QAAAjD,aAAA;QAAAK,QAAA;QAAAC,WAAA;MAAA;MAAA4C,OAAA;QAAA3C,eAAA;MAAA;MAAA4C,UAAA;MAAAC,QAAA,GAAAtG,EAAA,CAAAuG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBvB7G,EANV,CAAAC,cAAA,aAAoC,aAEN,aAC2B,aACd,aACJ,eACmB;UAAAD,EAAA,CAAAmB,MAAA,+BAAI;UAAAnB,EAAA,CAAAW,YAAA,EAAQ;UAE1DX,EADF,CAAAC,cAAA,oBAAe,eAEyC;UADOD,EAAA,CAAAiC,gBAAA,2BAAA8E,6DAAA5E,MAAA;YAAAnC,EAAA,CAAAqC,kBAAA,CAAAyE,GAAA,CAAAtG,aAAA,EAAA2B,MAAA,MAAA2E,GAAA,CAAAtG,aAAA,GAAA2B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACtFnC,EAAA,CAAAE,UAAA,yBAAA8G,2DAAA;YAAA,OAAeF,GAAA,CAAApG,QAAA,EAAU;UAAA,EAAC;UAD5BV,EAAA,CAAAW,YAAA,EACsD;UACtDX,EAAA,CAAAgD,UAAA,IAAAiE,uCAAA,qBACsD;UAE1DjH,EADE,CAAAW,YAAA,EAAgB,EACZ;UAEJX,EADF,CAAAC,cAAA,cAA+B,gBACmB;UAAAD,EAAA,CAAAmB,MAAA,gCAAI;UAAAnB,EAAA,CAAAW,YAAA,EAAQ;UAE1DX,EADF,CAAAC,cAAA,qBAAe,gBAEyC;UADOD,EAAA,CAAAiC,gBAAA,2BAAAiF,8DAAA/E,MAAA;YAAAnC,EAAA,CAAAqC,kBAAA,CAAAyE,GAAA,CAAAhG,cAAA,EAAAqB,MAAA,MAAA2E,GAAA,CAAAhG,cAAA,GAAAqB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UACvFnC,EAAA,CAAAE,UAAA,yBAAAiH,4DAAA;YAAA,OAAeL,GAAA,CAAApG,QAAA,EAAU;UAAA,EAAC;UAD5BV,EAAA,CAAAW,YAAA,EACsD;UACtDX,EAAA,CAAAgD,UAAA,KAAAoE,wCAAA,qBACuD;UAE3DpH,EADE,CAAAW,YAAA,EAAgB,EACZ;UAGFX,EAFJ,CAAAC,cAAA,cAA+B,eACY,kBACmD;UAAtDD,EAAA,CAAAE,UAAA,mBAAAmH,uDAAA;YAAA,OAASP,GAAA,CAAA5B,OAAA,EAAS;UAAA,EAAC;UACrDlF,EAAA,CAAAe,SAAA,mBAA0C;UAC5Cf,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAC,cAAA,kBAAsG;UAAvDD,EAAA,CAAAE,UAAA,mBAAAoH,uDAAA;YAAA,OAASR,GAAA,CAAApG,QAAA,EAAU;UAAA,EAAC;UAEjEV,EADA,CAAAgD,UAAA,KAAAuE,wCAAA,sBAAkD,KAAAC,wCAAA,sBACgB;UAClExH,EAAA,CAAAC,cAAA,gBAAsC;UAAAD,EAAA,CAAAmB,MAAA,IAAgC;UAMlFnB,EANkF,CAAAW,YAAA,EAAO,EACtE,EACL,EACF,EACF,EACF,EACF;UAKJX,EAFF,CAAAC,cAAA,eAAsF,eAEhB;UAKlED,EAJA,CAAAgD,UAAA,KAAAyE,oCAAA,kBAAwD,KAAAC,oCAAA,kBAIR;UAGhD1H,EAAA,CAAAC,cAAA,iBAA0B;UACxBD,EAAA,CAAAmB,MAAA,IACF;UACFnB,EADE,CAAAW,YAAA,EAAQ,EACJ;UAGNX,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAgD,UAAA,KAAA2E,oCAAA,kBACwC;UAM1C3H,EAAA,CAAAW,YAAA,EAAM;UASNX,EANA,CAAAgD,UAAA,KAAA4E,oCAAA,kBAA8E,KAAAC,oCAAA,kBAME;UAKlF7H,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAgD,UAAA,KAAA8E,oCAAA,kBAAgG;UAclG9H,EAAA,CAAAW,YAAA,EAAM;;;UAvFmEX,EAAA,CAAAoB,SAAA,GAA2B;UAA3BpB,EAAA,CAAAwC,gBAAA,YAAAsE,GAAA,CAAAtG,aAAA,CAA2B;UAC3DR,EAAA,CAAAqB,UAAA,aAAAyF,GAAA,CAAApD,SAAA,CAAsB;UACzC1D,EAAA,CAAAoB,SAAA,EAAmB;UAAnBpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAtG,aAAA,CAAmB;UAOgCR,EAAA,CAAAoB,SAAA,GAA4B;UAA5BpB,EAAA,CAAAwC,gBAAA,YAAAsE,GAAA,CAAAhG,cAAA,CAA4B;UAC5Dd,EAAA,CAAAqB,UAAA,aAAAyF,GAAA,CAAApD,SAAA,CAAsB;UACzC1D,EAAA,CAAAoB,SAAA,EAAoB;UAApBpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAhG,cAAA,CAAoB;UAM0Bd,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAqB,UAAA,aAAAyF,GAAA,CAAApD,SAAA,CAAsB;UAGV1D,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAqB,UAAA,aAAAyF,GAAA,CAAApD,SAAA,CAAsB;UAC9E1D,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAAqB,UAAA,UAAAyF,GAAA,CAAApD,SAAA,CAAgB;UAChB1D,EAAA,CAAAoB,SAAA,EAAe;UAAfpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAApD,SAAA,CAAe;UACa1D,EAAA,CAAAoB,SAAA,GAAgC;UAAhCpB,EAAA,CAAA8B,iBAAA,CAAAgF,GAAA,CAAApD,SAAA,sCAAgC;UAYtC1D,EAAA,CAAAoB,SAAA,GAAc;UAAdpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAvD,QAAA,CAAc;UAIhDvD,EAAA,CAAAoB,SAAA,EAAe;UAAfpB,EAAA,CAAAqB,UAAA,UAAAyF,GAAA,CAAAvD,QAAA,CAAe;UAInBvD,EAAA,CAAAoB,SAAA,GACF;UADEpB,EAAA,CAAA+H,kBAAA,aAAAjB,GAAA,CAAApE,YAAA,0BAAAoE,GAAA,CAAAxE,SAAA,SAAAwE,GAAA,CAAAlB,UAAA,aACF;UAK0C5F,EAAA,CAAAoB,SAAA,GAAkB;UAAlBpB,EAAA,CAAAqB,UAAA,YAAAyF,GAAA,CAAAnD,eAAA,CAAkB;UAUxD3D,EAAA,CAAAoB,SAAA,EAAkC;UAAlCpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAAnD,eAAA,CAAAR,MAAA,OAAkC;UAMSnD,EAAA,CAAAoB,SAAA,EAA6B;UAA7BpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAApE,YAAA,GAAAoE,GAAA,CAAArE,QAAA,CAA6B;UAQ1EzC,EAAA,CAAAoB,SAAA,EAA8B;UAA9BpB,EAAA,CAAAqB,UAAA,SAAAyF,GAAA,CAAA5D,aAAA,CAAAC,MAAA,KAA8B;;;qBD9DlC5D,YAAY,EAAAyI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1I,WAAW,EAAA2I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX7I,iBAAiB,EAAA8I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,iBAAA,EACjB/I,aAAa,EAAA6I,EAAA,CAAAG,gBAAA,EACb/I,cAAc,EAAA4I,EAAA,CAAAI,iBAAA,EACd/I,YAAY,EAAA2I,EAAA,CAAAK,eAAA,EACZ/I,YAAY,EACZC,mBAAmB;MAAA+I,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}