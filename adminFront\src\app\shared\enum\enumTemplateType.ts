export enum EnumTemplateType {
  CustomerChangeRequirement = 1, // 客變需求
  ItemTemplate = 2,              // 項目模板
  SpaceTemplate = 3              // 空間模板
}

export class EnumTemplateTypeHelper {
  static getDisplayName(templateType: EnumTemplateType): string {
    switch (templateType) {
      case EnumTemplateType.CustomerChangeRequirement:
        return '客變需求';
      case EnumTemplateType.ItemTemplate:
        return '項目模板';
      case EnumTemplateType.SpaceTemplate:
        return '空間模板';
      default:
        return '未知';
    }
  }

  static getTemplateTypeList(): Array<{ value: EnumTemplateType; label: string }> {
    return [
      { value: EnumTemplateType.CustomerChangeRequirement, label: '客變需求' },
      { value: EnumTemplateType.ItemTemplate, label: '項目模板' },
      { value: EnumTemplateType.SpaceTemplate, label: '空間模板' }
    ];
  }
}
