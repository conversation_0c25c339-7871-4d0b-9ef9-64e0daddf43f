{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(ctx_r2.templateModal));\n    });\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const template_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openEditModal(ctx_r2.templateModal, template_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 41)(14, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_Template_button_click_14_listener() {\n      const template_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r6 = i0.ɵɵreference(79);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r5, templateDetailModal_r6));\n    });\n    i0.ɵɵelement(15, \"i\", 43);\n    i0.ɵɵtext(16, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TemplateComponent_tr_70_button_17_Template, 3, 0, \"button\", 44)(18, TemplateComponent_tr_70_button_18_Template, 3, 0, \"button\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r5.CTemplateType === 1 ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : template_r5.CTemplateType === 2 ? \"\\u9805\\u76EE\\u6A21\\u677F\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r5.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r5.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r5.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, template_r5.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r5.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_44_Template_div_click_0_listener() {\n      const space_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 105);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r12 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r12.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r12.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r12.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵtext(2, \" \\u5171 \");\n    i0.ɵɵelementStart(3, \"span\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ngx-pagination\", 37);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.spaceTotalRecords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_47_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_47_span_4_Template_button_click_2_listener() {\n      const space_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r15));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r15.CPart, \" \");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"label\", 110);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 111);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_74_div_47_span_4_Template, 3, 1, \"span\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_69_Template_div_click_0_listener() {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleItemSelection(item_r17));\n    });\n    i0.ɵɵelementStart(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 105);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", item_r17.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r17.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵtext(2, \" \\u5171 \");\n    i0.ɵɵelementStart(3, \"span\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ngx-pagination\", 37);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_74_div_71_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.itemPageIndex, $event) || (ctx_r2.itemPageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_74_div_71_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.itemPageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.itemTotalRecords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.itemPageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.itemPageSize)(\"CollectionSize\", ctx_r2.itemTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 129);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_8_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 130);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_8_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1, \" \\u55AE\\u50F9\\u5FC5\\u9808\\u5927\\u65BC0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_8_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1, \" \\u8ACB\\u8F38\\u5165\\u5177\\u9AD4\\u7684\\u55AE\\u4F4D\\uFF0C\\u4E0D\\u80FD\\u70BA\\u7A7A\\u6216\\u9810\\u8A2D\\u503C\\u300C\\u5F0F\\u300D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 120)(2, \"div\", 5)(3, \"span\", 121);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateComponent_ng_template_74_div_72_div_8_i_5_Template, 1, 0, \"i\", 122)(6, TemplateComponent_ng_template_74_div_72_div_8_i_6_Template, 1, 0, \"i\", 123);\n    i0.ɵɵelementStart(7, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_72_div_8_Template_button_click_7_listener() {\n      const item_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedItem(item_r20));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"small\", 83);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 120)(11, \"label\", 125);\n    i0.ɵɵtext(12, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 126);\n    i0.ɵɵlistener(\"input\", function TemplateComponent_ng_template_74_div_72_div_8_Template_input_input_13_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateItemPrice(item_r20, +$event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, TemplateComponent_ng_template_74_div_72_div_8_div_14_Template, 2, 0, \"div\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 120)(16, \"label\", 125);\n    i0.ɵɵtext(17, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 128);\n    i0.ɵɵlistener(\"input\", function TemplateComponent_ng_template_74_div_72_div_8_Template_input_input_18_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateItemUnit(item_r20, $event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, TemplateComponent_ng_template_74_div_72_div_8_div_19_Template, 2, 0, \"div\", 127);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r20 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"badge-success\", ctx_r2.isItemValid(item_r20))(\"badge-warning\", !ctx_r2.isItemValid(item_r20));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r20.CPart, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemValid(item_r20));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isItemValid(item_r20));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r20.CLocation || \"-\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r20.CUnitPrice || item_r20.CUnitPrice <= 0);\n    i0.ɵɵproperty(\"value\", item_r20.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r20.CUnitPrice || item_r20.CUnitPrice <= 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r20.CUnit || item_r20.CUnit.trim() === \"\" || item_r20.CUnit.trim() === \"\\u5F0F\");\n    i0.ɵɵproperty(\"value\", item_r20.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r20.CUnit || item_r20.CUnit.trim() === \"\" || item_r20.CUnit.trim() === \"\\u5F0F\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 115)(2, \"label\", 116);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\", 83);\n    i0.ɵɵtext(5);\n    i0.ɵɵelement(6, \"i\", 117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 118);\n    i0.ɵɵtemplate(8, TemplateComponent_ng_template_74_div_72_div_8_Template, 20, 16, \"div\", 119);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u9805\\u76EE (\", ctx_r2.selectedItemsForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A: \", ctx_r2.getValidItemsCount(), \" / \", ctx_r2.selectedItemsForTemplate.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedItemsForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 55);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_5_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 59)(9, \"div\", 60)(10, \"div\", 61)(11, \"div\", 62)(12, \"label\", 63);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64)(15, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_74_Template_input_keydown_control_enter_15_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r10));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 60)(17, \"div\", 61)(18, \"div\", 62)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64)(22, \"nb-tabset\", 67)(23, \"nb-tab\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"div\", 69)(25, \"div\", 70)(26, \"div\", 71)(27, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_27_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 71)(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_29_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 74)(31, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(32, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(34, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 79)(36, \"div\", 80)(37, \"div\", 5)(38, \"input\", 81);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_74_Template_input_change_38_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"label\", 82);\n    i0.ɵɵtext(40, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"small\", 83);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 84);\n    i0.ɵɵtemplate(44, TemplateComponent_ng_template_74_div_44_Template, 6, 4, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, TemplateComponent_ng_template_74_div_45_Template, 3, 0, \"div\", 86)(46, TemplateComponent_ng_template_74_div_46_Template, 7, 4, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, TemplateComponent_ng_template_74_div_47_Template, 5, 2, \"div\", 88);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"nb-tab\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_nb_tab_click_48_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(49, \"div\", 69)(50, \"div\", 70)(51, \"div\", 71)(52, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_52_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.itemSearchKeyword, $event) || (ctx_r2.itemSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_52_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 71)(54, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_54_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.itemSearchLocation, $event) || (ctx_r2.itemSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_54_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 74)(56, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemReset());\n    });\n    i0.ɵɵelement(57, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemSearch());\n    });\n    i0.ɵɵelement(59, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 79)(61, \"div\", 80)(62, \"div\", 5)(63, \"input\", 90);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_74_Template_input_change_63_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllItems());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"label\", 91);\n    i0.ɵɵtext(65, \"\\u5168\\u9078\\u7576\\u9801\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"small\", 83);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 84);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_74_div_69_Template, 6, 4, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(70, TemplateComponent_ng_template_74_div_70_Template, 3, 0, \"div\", 86)(71, TemplateComponent_ng_template_74_div_71_Template, 7, 4, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(72, TemplateComponent_ng_template_74_div_72_Template, 9, 4, \"div\", 88);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(73, \"div\", 60)(74, \"div\", 61)(75, \"div\", 62)(76, \"label\", 92);\n    i0.ɵɵtext(77, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"div\", 64)(79, \"nb-form-field\", 93)(80, \"nb-select\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_80_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(81, \"nb-option\", 16)(82, \"span\", 5);\n    i0.ɵɵelement(83, \"i\", 95);\n    i0.ɵɵtext(84, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"nb-option\", 16)(86, \"span\", 5);\n    i0.ɵɵelement(87, \"i\", 96);\n    i0.ɵɵtext(88, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(89, \"nb-card-footer\", 97)(90, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_90_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵelement(91, \"i\", 99);\n    i0.ɵɵtext(92, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_93_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r10));\n    });\n    i0.ɵɵelement(94, \"i\", 101);\n    i0.ɵɵtext(95, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.itemSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.itemSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allItemsSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.itemTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.itemPageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.itemTotalRecords / ctx_r2.itemPageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableItemsForTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableItemsForTemplate.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTotalRecords > ctx_r2.itemPageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedItemsForTemplate.length > 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 132)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 133);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_5_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r22));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 59)(9, \"div\", 60)(10, \"div\", 61)(11, \"div\", 62)(12, \"label\", 134);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64)(15, \"input\", 135);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_76_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 60)(17, \"div\", 61)(18, \"div\", 62)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64)(22, \"nb-tabset\", 67)(23, \"nb-tab\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"span\", 136);\n    i0.ɵɵtext(25, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 69)(27, \"div\", 137);\n    i0.ɵɵelement(28, \"i\", 51);\n    i0.ɵɵtext(29, \" \\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\uFF0C\\u7A7A\\u9593\\u914D\\u7F6E\\u8ACB\\u5728\\u6A21\\u677F\\u8A73\\u60C5\\u4E2D\\u9032\\u884C\\u7BA1\\u7406\\u3002 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-tab\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_nb_tab_click_30_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(31, \"span\", 136);\n    i0.ɵɵtext(32, \"\\u9805\\u76EE\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 69)(34, \"div\", 137);\n    i0.ɵɵelement(35, \"i\", 51);\n    i0.ɵɵtext(36, \" \\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\uFF0C\\u9805\\u76EE\\u914D\\u7F6E\\u53CA\\u55AE\\u50F9\\u55AE\\u4F4D\\u8A2D\\u5B9A\\u8ACB\\u5728\\u6A21\\u677F\\u8A73\\u60C5\\u4E2D\\u9032\\u884C\\u7BA1\\u7406\\u3002 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(37, \"div\", 60)(38, \"div\", 61)(39, \"div\", 62)(40, \"label\", 138);\n    i0.ɵɵtext(41, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 64)(43, \"nb-form-field\", 93)(44, \"nb-select\", 139);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_76_Template_nb_select_ngModelChange_44_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(45, \"nb-option\", 16)(46, \"span\", 5);\n    i0.ɵɵelement(47, \"i\", 95);\n    i0.ɵɵtext(48, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"nb-option\", 16)(50, \"span\", 5);\n    i0.ɵɵelement(51, \"i\", 96);\n    i0.ɵɵtext(52, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(53, \"nb-card-footer\", 97)(54, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_54_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r22));\n    });\n    i0.ɵɵelement(55, \"i\", 99);\n    i0.ɵɵtext(56, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_57_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r22));\n    });\n    i0.ɵɵelement(58, \"i\", 140);\n    i0.ɵɵtext(59, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_78_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 163);\n    i0.ɵɵelement(1, \"i\", 164);\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 171);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 172);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r25 = ctx.$implicit;\n    const i_r26 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r26 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r25.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r25.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 166)(1, \"table\", 167)(2, \"thead\")(3, \"tr\")(4, \"th\", 168);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 169);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 170);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_78_div_70_div_1_Template, 12, 1, \"div\", 165)(2, TemplateComponent_ng_template_78_div_70_div_2_Template, 3, 0, \"div\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 141);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_78_Template_button_click_5_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r24));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 142)(9, \"div\", 143)(10, \"h6\", 144);\n    i0.ɵɵelement(11, \"i\", 145);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 146)(14, \"div\", 59)(15, \"div\", 9)(16, \"div\", 147)(17, \"label\", 148);\n    i0.ɵɵelement(18, \"i\", 149);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 150);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"div\", 147)(24, \"label\", 148);\n    i0.ɵɵelement(25, \"i\", 151);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 150)(28, \"span\", 40);\n    i0.ɵɵelement(29, \"i\", 152);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 147)(33, \"label\", 148);\n    i0.ɵɵelement(34, \"i\", 153);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 150);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"div\", 147)(41, \"label\", 148);\n    i0.ɵɵelement(42, \"i\", 154);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 150);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"div\", 147)(48, \"label\", 148);\n    i0.ɵɵelement(49, \"i\", 155);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 150);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 147)(56, \"label\", 148);\n    i0.ɵɵelement(57, \"i\", 156);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 150);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 157)(62, \"div\", 158)(63, \"h6\", 144);\n    i0.ɵɵelement(64, \"i\", 159);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 160);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 146);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_78_div_69_Template, 4, 0, \"div\", 161)(70, TemplateComponent_ng_template_78_div_70_Template, 3, 2, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 97)(72, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_78_Template_button_click_72_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r24));\n    });\n    i0.ɵɵelement(73, \"i\", 99);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.searchTemplateType = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\n    this.availableItemsForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.itemPageSize = 10;\n    this.itemTotalRecords = 0;\n    this.allItemsSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n    // 模態框模式控制\n    this.isEditMode = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      CTemplateType: this.searchTemplateType,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 載入項目模板可用項目（使用空間列表作為基礎）\n  loadAvailableItemsForTemplate() {\n    const request = {\n      CPart: this.itemSearchKeyword || null,\n      CLocation: this.itemSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.itemPageIndex,\n      PageSize: this.itemPageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableItemsForTemplate = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\n          CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\n          CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\n        })) || [];\n        this.itemTotalRecords = response.TotalItems || 0;\n        this.updateAllItemsSelectedState();\n      }\n    })).subscribe();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 項目搜尋功能\n  onItemSearch() {\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  onItemReset() {\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  itemPageChanged(page) {\n    this.itemPageIndex = page;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.isEditMode = false;\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.loadAvailableSpaces();\n    this.loadAvailableItemsForTemplate();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.isEditMode = true;\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    // 編輯模式下不需要載入選擇器數據\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  // 計算模態框寬度\n  get modalWidth() {\n    return this.isEditMode ? '550px' : '800px';\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\n      return false;\n    }\n    // 驗證項目模板的單價和單位\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      for (const item of this.selectedItemsForTemplate) {\n        // 檢核單價\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\n          return false;\n        }\n        // 檢核單位\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\n          return false;\n        }\n        // 檢核單價是否為有效數字\n        if (isNaN(item.CUnitPrice)) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\n          return false;\n        }\n        // 檢核單位長度\n        if (item.CUnit.trim().length > 10) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間或項目）\n  saveTemplateDetails(templateId, ref) {\n    let details = [];\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n      // 空間模板的詳細資料\n      details = this.selectedSpacesForTemplate.map(space => ({\n        CTemplateDetailId: null,\n        CReleateId: space.CSpaceID,\n        CPart: space.CPart,\n        CLocation: space.CLocation\n      }));\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      // 項目模板的詳細資料，包含單價和單位\n      details = this.selectedItemsForTemplate.map(item => ({\n        CTemplateDetailId: null,\n        CReleateId: item.CSpaceID,\n        CPart: item.CPart,\n        CLocation: item.CLocation,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit\n      }));\n    }\n    if (details.length > 0) {\n      const templateData = {\n        CTemplateId: templateId,\n        CTemplateName: this.templateDetail.CTemplateName,\n        CTemplateType: this.templateDetail.CTemplateType,\n        CStatus: this.templateDetail.CStatus,\n        Details: details\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('建立模板成功');\n          ref.close();\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '保存模板詳細資料失敗');\n        }\n      })).subscribe();\n    } else {\n      this.message.showSucessMSG('建立模板成功');\n      ref.close();\n      this.loadTemplateList();\n    }\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 項目選擇相關方法\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    if (item.selected) {\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n        const newItem = {\n          ...item,\n          CUnitPrice: 0,\n          // 設為0，強制用戶輸入\n          CUnit: '' // 設為空，強制用戶輸入\n        };\n        this.selectedItemsForTemplate.push(newItem);\n      }\n    } else {\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    }\n    this.updateAllItemsSelectedState();\n  }\n  toggleAllItems() {\n    this.allItemsSelected = !this.allItemsSelected;\n    this.availableItemsForTemplate.forEach(item => {\n      item.selected = this.allItemsSelected;\n      if (this.allItemsSelected) {\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n          const newItem = {\n            ...item,\n            CUnitPrice: 0,\n            // 設為0，強制用戶輸入\n            CUnit: '' // 設為空，強制用戶輸入\n          };\n          this.selectedItemsForTemplate.push(newItem);\n        }\n      } else {\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n      }\n    });\n  }\n  removeSelectedItem(item) {\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.selected = false;\n    }\n    this.updateAllItemsSelectedState();\n  }\n  updateAllItemsSelectedState() {\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 && this.availableItemsForTemplate.every(item => item.selected);\n  }\n  // 更新選中項目的單價和單位\n  updateItemPrice(item, price) {\n    // 確保價格為有效數字且大於0\n    if (isNaN(price) || price < 0) {\n      price = 0;\n    }\n    item.CUnitPrice = price;\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnitPrice = price;\n    }\n  }\n  updateItemUnit(item, unit) {\n    // 清理單位字串\n    unit = unit ? unit.trim() : '';\n    item.CUnit = unit;\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnit = unit;\n    }\n  }\n  // 檢查項目是否有效（用於UI顯示）\n  isItemValid(item) {\n    return !!(item.CUnitPrice && item.CUnitPrice > 0 && item.CUnit && item.CUnit.trim() !== '' && item.CUnit.trim() !== '式');\n  }\n  // 獲取已完成設定的項目數量\n  getValidItemsCount() {\n    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      viewQuery: function TemplateComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 80,\n      vars: 15,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"templateType\", 1, \"label\", \"col-3\"], [\"id\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 2, \"width\", \"120px\"], [\"scope\", \"col\", 2, \"width\", \"200px\"], [\"scope\", \"col\", 2, \"width\", \"100px\"], [\"scope\", \"col\", 2, \"width\", \"180px\"], [\"scope\", \"col\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"template-type-tabs\"], [\"tabTitle\", \"\\u7A7A\\u9593\\u6A21\\u677F\", 3, \"click\", \"active\"], [1, \"mt-3\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"w-100 d-flex flex-column align-items-center mt-4\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [\"tabTitle\", \"\\u9805\\u76EE\\u6A21\\u677F\", 3, \"click\", \"active\"], [\"type\", \"checkbox\", \"id\", \"selectAllItems\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllItems\", 1, \"mb-0\", \"font-weight-bold\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"w-100\", \"d-flex\", \"flex-column\", \"align-items-center\", \"mt-4\"], [1, \"mb-2\", \"text-secondary\", 2, \"font-size\", \"0.95rem\"], [1, \"fw-bold\", \"text-primary\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\", \"font-weight-bold\"], [\"title\", \"\\u55AE\\u50F9\\u5FC5\\u9808\\u5927\\u65BC0\\uFF0C\\u55AE\\u4F4D\\u4E0D\\u80FD\\u70BA\\u7A7A\\u6216\\u9810\\u8A2D\\u503C\\u300C\\u5F0F\\u300D\", 1, \"fas\", \"fa-info-circle\", \"ms-1\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"max-height\", \"400px\", \"overflow-y\", \"auto\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"mb-3\"], [1, \"badge\", \"me-2\"], [\"class\", \"fas fa-check-circle text-success me-1\", \"title\", \"\\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A\", 4, \"ngIf\"], [\"class\", \"fas fa-exclamation-triangle text-warning me-1\", \"title\", \"\\u8ACB\\u5B8C\\u6210\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\\u8A2D\\u5B9A\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn-close\", \"ms-auto\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [1, \"form-label\", \"small\", \"required-field\"], [\"type\", \"number\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", \"min\", \"0.01\", \"step\", \"0.01\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"input\", \"value\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5177\\u9AD4\\u55AE\\u4F4D\\uFF08\\u5982\\uFF1A\\u576A\\u3001\\u7D44\\u3001\\u500B\\uFF09\", \"maxlength\", \"10\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"input\", \"value\"], [\"title\", \"\\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A\", 1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-1\"], [\"title\", \"\\u8ACB\\u5B8C\\u6210\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\\u8A2D\\u5B9A\", 1, \"fas\", \"fa-exclamation-triangle\", \"text-warning\", \"me-1\"], [1, \"invalid-feedback\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"editTemplateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editTemplateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"editTemplateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"slot\", \"tabTitle\"], [1, \"alert\", \"alert-info\"], [\"for\", \"editTemplateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"editTemplateStatus\", \"name\", \"editTemplateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 7);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 12)(16, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 12)(22, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(23, \"nb-option\", 16);\n          i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 16);\n          i0.ɵɵtext(26, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 16);\n          i0.ɵɵtext(28, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"div\", 9)(30, \"div\", 10)(31, \"label\", 17);\n          i0.ɵɵtext(32, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-form-field\", 12)(34, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTemplateType, $event) || (ctx.searchTemplateType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_34_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 16);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 16);\n          i0.ɵɵtext(38, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 16);\n          i0.ɵɵtext(40, \"\\u9805\\u76EE\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(41, \"div\", 9);\n          i0.ɵɵelementStart(42, \"div\", 19)(43, \"div\", 20)(44, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(45, \"i\", 22);\n          i0.ɵɵtext(46, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(48, \"i\", 24);\n          i0.ɵɵtext(49, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 19)(51, \"div\", 25);\n          i0.ɵɵtemplate(52, TemplateComponent_button_52_Template, 3, 0, \"button\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 27)(54, \"table\", 28)(55, \"thead\")(56, \"tr\")(57, \"th\", 29);\n          i0.ɵɵtext(58, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 30);\n          i0.ɵɵtext(60, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 31);\n          i0.ɵɵtext(62, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 32);\n          i0.ɵɵtext(64, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 29);\n          i0.ɵɵtext(66, \"\\u5EFA\\u7ACB\\u8005\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 33);\n          i0.ɵɵtext(68, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"tbody\");\n          i0.ɵɵtemplate(70, TemplateComponent_tr_70_Template, 19, 11, \"tr\", 34)(71, TemplateComponent_tr_71_Template, 4, 0, \"tr\", 35);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"nb-card-footer\", 36)(73, \"ngx-pagination\", 37);\n          i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(74, TemplateComponent_ng_template_74_Template, 96, 26, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(76, TemplateComponent_ng_template_76_Template, 60, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(78, TemplateComponent_ng_template_78_Template, 75, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTemplateType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbTabsetComponent, i2.NbTabComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n\\n.btn-close-white[_ngcontent-%COMP%] {\\n  filter: invert(1) grayscale(100%) brightness(200%);\\n}\\n\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset {\\n  border-bottom: 2px solid #f1f3f4;\\n  margin-bottom: 0;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab {\\n  padding: 0;\\n  margin-right: 8px;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link {\\n  padding: 14px 24px;\\n  border: none;\\n  border-radius: 8px 8px 0 0;\\n  background-color: transparent;\\n  color: #6c757d;\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link:hover {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active {\\n  background-color: #007bff;\\n  color: #fff;\\n  font-weight: 600;\\n  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background-color: #007bff;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content {\\n  padding: 0;\\n  border: none;\\n  background: transparent;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content .tab-pane.active {\\n  display: block;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid !important;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n  border: 1px dashed #ccc;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: block;\\n  width: 100%;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border: 2px solid #e9ecef !important;\\n  border-radius: 12px;\\n  background-color: #fff !important;\\n  transition: all 0.3s ease;\\n  min-height: 80px;\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50 !important;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  margin-bottom: 4px;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d !important;\\n  line-height: 1.3;\\n  font-weight: 400;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f8f9ff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #0056b3;\\n  font-weight: 700;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: bold;\\n}\\n\\n@media (max-width: 1200px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateComponent_button_52_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openCreateModal", "templateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateComponent_tr_70_button_17_Template_button_click_0_listener", "_r7", "template_r5", "$implicit", "openEditModal", "TemplateComponent_tr_70_button_18_Template_button_click_0_listener", "_r8", "deleteTemplate", "TemplateComponent_tr_70_Template_button_click_14_listener", "_r4", "templateDetailModal_r6", "ɵɵreference", "viewTemplateDetail", "ɵɵtemplate", "TemplateComponent_tr_70_button_17_Template", "TemplateComponent_tr_70_button_18_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "CTemplateType", "ɵɵtextInterpolate", "CTemplateName", "ɵɵproperty", "CStatus", "ɵɵpipeBind2", "CCreateDt", "CCreator", "isUpdate", "isDelete", "TemplateComponent_ng_template_74_div_44_Template_div_click_0_listener", "space_r12", "_r11", "toggleSpaceSelection", "ɵɵclassProp", "selected", "<PERSON>art", "CLocation", "ɵɵtwoWayListener", "TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener", "$event", "_r13", "ɵɵtwoWayBindingSet", "spacePageIndex", "spacePageChanged", "spaceTotalRecords", "ɵɵtwoWayProperty", "spacePageSize", "TemplateComponent_ng_template_74_div_47_span_4_Template_button_click_2_listener", "space_r15", "_r14", "removeSelectedSpace", "TemplateComponent_ng_template_74_div_47_span_4_Template", "selectedSpacesForTemplate", "length", "TemplateComponent_ng_template_74_div_69_Template_div_click_0_listener", "item_r17", "_r16", "toggleItemSelection", "TemplateComponent_ng_template_74_div_71_Template_ngx_pagination_PageChange_6_listener", "_r18", "itemPageIndex", "itemPageChanged", "itemTotalRecords", "itemPageSize", "TemplateComponent_ng_template_74_div_72_div_8_i_5_Template", "TemplateComponent_ng_template_74_div_72_div_8_i_6_Template", "TemplateComponent_ng_template_74_div_72_div_8_Template_button_click_7_listener", "item_r20", "_r19", "removeSelectedItem", "TemplateComponent_ng_template_74_div_72_div_8_Template_input_input_13_listener", "updateItemPrice", "target", "value", "TemplateComponent_ng_template_74_div_72_div_8_div_14_Template", "TemplateComponent_ng_template_74_div_72_div_8_Template_input_input_18_listener", "updateItemUnit", "TemplateComponent_ng_template_74_div_72_div_8_div_19_Template", "isItemValid", "CUnitPrice", "CUnit", "trim", "TemplateComponent_ng_template_74_div_72_div_8_Template", "selectedItemsForTemplate", "ɵɵtextInterpolate2", "getValidItemsCount", "TemplateComponent_ng_template_74_Template_button_click_5_listener", "ref_r10", "_r9", "dialogRef", "onClose", "TemplateComponent_ng_template_74_Template_input_ngModelChange_15_listener", "templateDetail", "TemplateComponent_ng_template_74_Template_input_keydown_control_enter_15_listener", "onSubmit", "TemplateComponent_ng_template_74_Template_nb_tab_click_23_listener", "SpaceTemplate", "TemplateComponent_ng_template_74_Template_input_ngModelChange_27_listener", "spaceSearchKeyword", "TemplateComponent_ng_template_74_Template_input_keyup_enter_27_listener", "onSpaceSearch", "TemplateComponent_ng_template_74_Template_input_ngModelChange_29_listener", "spaceSearchLocation", "TemplateComponent_ng_template_74_Template_input_keyup_enter_29_listener", "TemplateComponent_ng_template_74_Template_button_click_31_listener", "onSpaceReset", "TemplateComponent_ng_template_74_Template_button_click_33_listener", "TemplateComponent_ng_template_74_Template_input_change_38_listener", "toggleAllSpaces", "TemplateComponent_ng_template_74_div_44_Template", "TemplateComponent_ng_template_74_div_45_Template", "TemplateComponent_ng_template_74_div_46_Template", "TemplateComponent_ng_template_74_div_47_Template", "TemplateComponent_ng_template_74_Template_nb_tab_click_48_listener", "ItemTemplate", "TemplateComponent_ng_template_74_Template_input_ngModelChange_52_listener", "itemSearchKeyword", "TemplateComponent_ng_template_74_Template_input_keyup_enter_52_listener", "onItemSearch", "TemplateComponent_ng_template_74_Template_input_ngModelChange_54_listener", "itemSearchLocation", "TemplateComponent_ng_template_74_Template_input_keyup_enter_54_listener", "TemplateComponent_ng_template_74_Template_button_click_56_listener", "onItemReset", "TemplateComponent_ng_template_74_Template_button_click_58_listener", "TemplateComponent_ng_template_74_Template_input_change_63_listener", "toggleAllItems", "TemplateComponent_ng_template_74_div_69_Template", "TemplateComponent_ng_template_74_div_70_Template", "TemplateComponent_ng_template_74_div_71_Template", "TemplateComponent_ng_template_74_div_72_Template", "TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_80_listener", "TemplateComponent_ng_template_74_Template_button_click_90_listener", "TemplateComponent_ng_template_74_Template_button_click_93_listener", "allSpacesSelected", "ɵɵtextInterpolate3", "Math", "ceil", "availableSpaces", "allItemsSelected", "availableItemsForTemplate", "TemplateComponent_ng_template_76_Template_button_click_5_listener", "ref_r22", "_r21", "TemplateComponent_ng_template_76_Template_input_ngModelChange_15_listener", "TemplateComponent_ng_template_76_Template_nb_tab_click_23_listener", "TemplateComponent_ng_template_76_Template_nb_tab_click_30_listener", "TemplateComponent_ng_template_76_Template_nb_select_ngModelChange_44_listener", "TemplateComponent_ng_template_76_Template_button_click_54_listener", "TemplateComponent_ng_template_76_Template_button_click_57_listener", "i_r26", "space_r25", "TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template", "templateDetailSpaces", "TemplateComponent_ng_template_78_div_70_div_1_Template", "TemplateComponent_ng_template_78_div_70_div_2_Template", "TemplateComponent_ng_template_78_Template_button_click_5_listener", "ref_r24", "_r23", "TemplateComponent_ng_template_78_div_69_Template", "TemplateComponent_ng_template_78_div_70_Template", "TemplateComponent_ng_template_78_Template_button_click_72_listener", "selectedTemplateDetail", "ɵɵclassMap", "CUpdateDt", "CUpdator", "isLoadingTemplateDetail", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "searchKeyword", "searchStatus", "searchTemplateType", "isEditMode", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "TotalItems", "showErrorMSG", "Message", "subscribe", "apiSpaceGetSpaceListPost$Json", "CSpaceID", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "loadAvailableItemsForTemplate", "find", "updateAllItemsSelectedState", "pageChanged", "page", "modal", "open", "context", "autoFocus", "template", "ref", "close", "modalWidth", "validateTemplateForm", "updateTemplate", "createTemplate", "undefined", "isNaN", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "details", "space", "CTemplateDetailId", "CReleateId", "Details", "confirm", "apiTemplateDeleteTemplatePost$Json", "apiTemplateGetTemplateDetailByIdPost$Json", "push", "filter", "for<PERSON>ach", "availableSpace", "every", "newItem", "availableItem", "price", "unit", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "TemplateService", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "viewQuery", "TemplateComponent_Query", "rf", "ctx", "TemplateComponent_Template_input_ngModelChange_16_listener", "_r1", "TemplateComponent_Template_input_keyup_enter_16_listener", "TemplateComponent_Template_nb_select_ngModelChange_22_listener", "TemplateComponent_Template_nb_select_selectedChange_22_listener", "TemplateComponent_Template_nb_select_ngModelChange_34_listener", "TemplateComponent_Template_nb_select_selected<PERSON><PERSON>e_34_listener", "TemplateComponent_Template_button_click_44_listener", "TemplateComponent_Template_button_click_47_listener", "TemplateComponent_button_52_Template", "TemplateComponent_tr_70_Template", "TemplateComponent_tr_71_Template", "TemplateComponent_Template_ngx_pagination_PageChange_73_listener", "TemplateComponent_ng_template_74_Template", "ɵɵtemplateRefExtractor", "TemplateComponent_ng_template_76_Template", "TemplateComponent_ng_template_78_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbTabsetComponent", "NbTabComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n// 項目模板選擇項目介面（擴展空間選擇項目，添加單價和單位）\r\nexport interface ItemPickListItem extends SpacePickListItem {\r\n  CUnitPrice?: number;\r\n  CUnit?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n  searchTemplateType: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\r\n  availableItemsForTemplate: ItemPickListItem[] = [];\r\n  selectedItemsForTemplate: ItemPickListItem[] = [];\r\n  itemSearchKeyword: string = '';\r\n  itemSearchLocation: string = '';\r\n  itemPageIndex = 1;\r\n  itemPageSize = 10;\r\n  itemTotalRecords = 0;\r\n  allItemsSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  // 模態框模式控制\r\n  isEditMode = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      CTemplateType: this.searchTemplateType,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 載入項目模板可用項目（使用空間列表作為基礎）\r\n  loadAvailableItemsForTemplate(): void {\r\n    const request = {\r\n      CPart: this.itemSearchKeyword || null,\r\n      CLocation: this.itemSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.itemPageIndex,\r\n      PageSize: this.itemPageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableItemsForTemplate = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\r\n            CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\r\n            CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\r\n          })) || [];\r\n          this.itemTotalRecords = response.TotalItems || 0;\r\n          this.updateAllItemsSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 項目搜尋功能\r\n  onItemSearch(): void {\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  onItemReset(): void {\r\n    this.itemSearchKeyword = '';\r\n    this.itemSearchLocation = '';\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  itemPageChanged(page: number): void {\r\n    this.itemPageIndex = page;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.isEditMode = false;\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.selectedItemsForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.loadAvailableItemsForTemplate();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.isEditMode = true;\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    // 編輯模式下不需要載入選擇器數據\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  // 計算模態框寬度\r\n  get modalWidth(): string {\r\n    return this.isEditMode ? '550px' : '800px';\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\r\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\r\n      return false;\r\n    }\r\n\r\n    // 驗證項目模板的單價和單位\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      for (const item of this.selectedItemsForTemplate) {\r\n        // 檢核單價\r\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位\r\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單價是否為有效數字\r\n        if (isNaN(item.CUnitPrice)) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位長度\r\n        if (item.CUnit.trim().length > 10) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間或項目）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    let details: any[] = [];\r\n\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      // 空間模板的詳細資料\r\n      details = this.selectedSpacesForTemplate.map(space => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: space.CSpaceID,\r\n        CPart: space.CPart,\r\n        CLocation: space.CLocation\r\n      }));\r\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      // 項目模板的詳細資料，包含單價和單位\r\n      details = this.selectedItemsForTemplate.map(item => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: item.CSpaceID,\r\n        CPart: item.CPart,\r\n        CLocation: item.CLocation,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit\r\n      }));\r\n    }\r\n\r\n    if (details.length > 0) {\r\n      const templateData: SaveTemplateArgs = {\r\n        CTemplateId: templateId,\r\n        CTemplateName: this.templateDetail.CTemplateName,\r\n        CTemplateType: this.templateDetail.CTemplateType,\r\n        CStatus: this.templateDetail.CStatus,\r\n        Details: details\r\n      };\r\n\r\n      this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('建立模板成功');\r\n            ref.close();\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '保存模板詳細資料失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    } else {\r\n      this.message.showSucessMSG('建立模板成功');\r\n      ref.close();\r\n      this.loadTemplateList();\r\n    }\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  // 項目選擇相關方法\r\n  toggleItemSelection(item: ItemPickListItem): void {\r\n    item.selected = !item.selected;\r\n\r\n    if (item.selected) {\r\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\r\n        const newItem = {\r\n          ...item,\r\n          CUnitPrice: 0, // 設為0，強制用戶輸入\r\n          CUnit: '' // 設為空，強制用戶輸入\r\n        };\r\n        this.selectedItemsForTemplate.push(newItem);\r\n      }\r\n    } else {\r\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  toggleAllItems(): void {\r\n    this.allItemsSelected = !this.allItemsSelected;\r\n\r\n    this.availableItemsForTemplate.forEach(item => {\r\n      item.selected = this.allItemsSelected;\r\n      if (this.allItemsSelected) {\r\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\r\n          const newItem = {\r\n            ...item,\r\n            CUnitPrice: 0, // 設為0，強制用戶輸入\r\n            CUnit: '' // 設為空，強制用戶輸入\r\n          };\r\n          this.selectedItemsForTemplate.push(newItem);\r\n        }\r\n      } else {\r\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedItem(item: ItemPickListItem): void {\r\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.selected = false;\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  updateAllItemsSelectedState(): void {\r\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 &&\r\n      this.availableItemsForTemplate.every(item => item.selected);\r\n  }\r\n\r\n  // 更新選中項目的單價和單位\r\n  updateItemPrice(item: ItemPickListItem, price: number): void {\r\n    // 確保價格為有效數字且大於0\r\n    if (isNaN(price) || price < 0) {\r\n      price = 0;\r\n    }\r\n\r\n    item.CUnitPrice = price;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnitPrice = price;\r\n    }\r\n  }\r\n\r\n  updateItemUnit(item: ItemPickListItem, unit: string): void {\r\n    // 清理單位字串\r\n    unit = unit ? unit.trim() : '';\r\n\r\n    item.CUnit = unit;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnit = unit;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否有效（用於UI顯示）\r\n  isItemValid(item: ItemPickListItem): boolean {\r\n    return !!(item.CUnitPrice && item.CUnitPrice > 0 &&\r\n      item.CUnit && item.CUnit.trim() !== '' && item.CUnit.trim() !== '式');\r\n  }\r\n\r\n  // 獲取已完成設定的項目數量\r\n  getValidItemsCount(): number {\r\n    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜尋條件區域 -->\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateName\" class=\"label col-3\">模板名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"templateName\" nbInput class=\"w-full\" placeholder=\"搜尋模板名稱...\"\r\n              [(ngModel)]=\"searchKeyword\" (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateType\" class=\"label col-3\">模板類型</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"templateType\" placeholder=\"選擇模板類型...\" [(ngModel)]=\"searchTemplateType\"\r\n              (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">空間模板</nb-option>\r\n              <nb-option [value]=\"2\">項目模板</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(templateModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表表格 -->\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr>\r\n            <th scope=\"col\" style=\"width: 120px;\">模板類型</th>\r\n            <th scope=\"col\" style=\"width: 200px;\">模板名稱</th>\r\n            <th scope=\"col\" style=\"width: 100px;\">狀態</th>\r\n            <th scope=\"col\" style=\"width: 180px;\">建立時間</th>\r\n            <th scope=\"col\" style=\"width: 120px;\">建立者</th>\r\n            <th scope=\"col\" style=\"width: 140px;\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let template of templateList\">\r\n            <td>\r\n              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}\r\n            </td>\r\n            <td>{{ template.CTemplateName }}</td>\r\n            <td>\r\n              <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n              </span>\r\n            </td>\r\n            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td>{{ template.CCreator || '-' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button class=\"btn btn-outline-info btn-sm me-1\"\r\n                (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                <i class=\"fas fa-eye\"></i>查看\r\n              </button>\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-warning btn-sm me-1\"\r\n                (click)=\"openEditModal(templateModal, template)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"templateList.length === 0\">\r\n            <td colspan=\"5\" class=\"text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模板模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\" (keydown.control.enter)=\"onSubmit(ref)\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <!-- 空間選擇區域 -->\r\n                    <div class=\"mt-3\">\r\n                      <!-- 搜尋區域 -->\r\n                      <div class=\"row mb-3\">\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                            [(ngModel)]=\"spaceSearchKeyword\" (keyup.enter)=\"onSpaceSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                            [(ngModel)]=\"spaceSearchLocation\" (keyup.enter)=\"onSpaceSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-2\">\r\n                          <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onSpaceReset()\">\r\n                            <i class=\"fas fa-undo\"></i>\r\n                          </button>\r\n                          <button class=\"btn btn-sm btn-secondary\" (click)=\"onSpaceSearch()\">\r\n                            <i class=\"fas fa-search\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 可選空間列表 -->\r\n                      <div class=\"border rounded p-3\" style=\"background-color: #f8f9fa;\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <div class=\"d-flex align-items-center\">\r\n                            <input type=\"checkbox\" id=\"selectAll\" [checked]=\"allSpacesSelected\"\r\n                              (change)=\"toggleAllSpaces()\" class=\"me-2\">\r\n                            <label for=\"selectAll\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\r\n                          </div>\r\n                          <small class=\"text-muted\">\r\n                            共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords /\r\n                            spacePageSize) }} 頁\r\n                          </small>\r\n                        </div>\r\n\r\n                        <!-- 空間項目網格 -->\r\n                        <div class=\"space-grid\">\r\n                          <div class=\"space-item\" *ngFor=\"let space of availableSpaces\"\r\n                            [class.selected]=\"space.selected\" (click)=\"toggleSpaceSelection(space)\">\r\n                            <div class=\"space-card\">\r\n                              <div class=\"space-name\">{{ space.CPart }}</div>\r\n                              <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 空間列表為空時的提示 -->\r\n                        <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n                          <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的空間\r\n                        </div>\r\n\r\n                        <!-- 分頁 -->\r\n                        <div class=\"w-100 d-flex flex-column align-items-center mt-4\"\r\n                          *ngIf=\"spaceTotalRecords > spacePageSize\">\r\n                          <div class=\"mb-2 text-secondary\" style=\"font-size: 0.95rem;\">\r\n                            共 <span class=\"fw-bold text-primary\">{{ spaceTotalRecords }}</span> 筆資料\r\n                          </div>\r\n                          <ngx-pagination [(Page)]=\"spacePageIndex\" [PageSize]=\"spacePageSize\"\r\n                            [CollectionSize]=\"spaceTotalRecords\" (PageChange)=\"spacePageChanged($event)\">\r\n                          </ngx-pagination>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 已選空間 -->\r\n                      <div class=\"mt-3\" *ngIf=\"selectedSpacesForTemplate.length > 0\">\r\n                        <label class=\"mb-2 font-weight-bold\">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>\r\n                        <div class=\"border rounded p-2\" style=\"max-height: 150px; overflow-y: auto;\">\r\n                          <span class=\"badge badge-primary me-1 mb-1\" *ngFor=\"let space of selectedSpacesForTemplate\">\r\n                            {{ space.CPart }}\r\n                            <button type=\"button\" class=\"btn-close btn-close-white ms-1\"\r\n                              (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"></button>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <!-- 項目選擇區域 -->\r\n                    <div class=\"mt-3\">\r\n                      <!-- 搜尋區域 -->\r\n                      <div class=\"row mb-3\">\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                            [(ngModel)]=\"itemSearchKeyword\" (keyup.enter)=\"onItemSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                            [(ngModel)]=\"itemSearchLocation\" (keyup.enter)=\"onItemSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-2\">\r\n                          <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onItemReset()\">\r\n                            <i class=\"fas fa-undo\"></i>\r\n                          </button>\r\n                          <button class=\"btn btn-sm btn-secondary\" (click)=\"onItemSearch()\">\r\n                            <i class=\"fas fa-search\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 可選項目列表 -->\r\n                      <div class=\"border rounded p-3\" style=\"background-color: #f8f9fa;\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <div class=\"d-flex align-items-center\">\r\n                            <input type=\"checkbox\" id=\"selectAllItems\" [checked]=\"allItemsSelected\"\r\n                              (change)=\"toggleAllItems()\" class=\"me-2\">\r\n                            <label for=\"selectAllItems\" class=\"mb-0 font-weight-bold\">全選當頁項目</label>\r\n                          </div>\r\n                          <small class=\"text-muted\">\r\n                            共 {{ itemTotalRecords }} 筆，第 {{ itemPageIndex }} / {{ Math.ceil(itemTotalRecords /\r\n                            itemPageSize) }} 頁\r\n                          </small>\r\n                        </div>\r\n\r\n                        <!-- 項目網格 -->\r\n                        <div class=\"space-grid\">\r\n                          <div class=\"space-item\" *ngFor=\"let item of availableItemsForTemplate\"\r\n                            [class.selected]=\"item.selected\" (click)=\"toggleItemSelection(item)\">\r\n                            <div class=\"space-card\">\r\n                              <div class=\"space-name\">{{ item.CPart }}</div>\r\n                              <div class=\"space-location\">{{ item.CLocation || '-' }}</div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 項目列表為空時的提示 -->\r\n                        <div *ngIf=\"availableItemsForTemplate.length === 0\" class=\"text-center text-muted py-4\">\r\n                          <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的項目\r\n                        </div>\r\n\r\n                        <!-- 分頁 -->\r\n                        <div class=\"w-100 d-flex flex-column align-items-center mt-4\"\r\n                          *ngIf=\"itemTotalRecords > itemPageSize\">\r\n                          <div class=\"mb-2 text-secondary\" style=\"font-size: 0.95rem;\">\r\n                            共 <span class=\"fw-bold text-primary\">{{ itemTotalRecords }}</span> 筆資料\r\n                          </div>\r\n                          <ngx-pagination [(Page)]=\"itemPageIndex\" [PageSize]=\"itemPageSize\"\r\n                            [CollectionSize]=\"itemTotalRecords\" (PageChange)=\"itemPageChanged($event)\">\r\n                          </ngx-pagination>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 已選項目及單價單位設定 -->\r\n                      <div class=\"mt-3\" *ngIf=\"selectedItemsForTemplate.length > 0\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                          <label class=\"mb-0 font-weight-bold\">已選擇的項目 ({{ selectedItemsForTemplate.length }})</label>\r\n                          <small class=\"text-muted\">\r\n                            已完成設定: {{ getValidItemsCount() }} / {{ selectedItemsForTemplate.length }}\r\n                            <i class=\"fas fa-info-circle ms-1\" title=\"單價必須大於0，單位不能為空或預設值「式」\"></i>\r\n                          </small>\r\n                        </div>\r\n                        <div class=\"border rounded p-3\" style=\"max-height: 400px; overflow-y: auto;\">\r\n                          <div class=\"row\" *ngFor=\"let item of selectedItemsForTemplate; let i = index\">\r\n                            <div class=\"col-md-4 mb-3\">\r\n                              <div class=\"d-flex align-items-center\">\r\n                                <span class=\"badge me-2\" [class.badge-success]=\"isItemValid(item)\"\r\n                                  [class.badge-warning]=\"!isItemValid(item)\">\r\n                                  {{ item.CPart }}\r\n                                </span>\r\n                                <i class=\"fas fa-check-circle text-success me-1\" *ngIf=\"isItemValid(item)\"\r\n                                  title=\"已完成設定\"></i>\r\n                                <i class=\"fas fa-exclamation-triangle text-warning me-1\" *ngIf=\"!isItemValid(item)\"\r\n                                  title=\"請完成單價和單位設定\"></i>\r\n                                <button type=\"button\" class=\"btn-close ms-auto\" (click)=\"removeSelectedItem(item)\"\r\n                                  style=\"font-size: 0.7rem;\"></button>\r\n                              </div>\r\n                              <small class=\"text-muted\">{{ item.CLocation || '-' }}</small>\r\n                            </div>\r\n                            <div class=\"col-md-4 mb-3\">\r\n                              <label class=\"form-label small required-field\">單價</label>\r\n                              <input type=\"number\" class=\"form-control form-control-sm\" [value]=\"item.CUnitPrice\"\r\n                                (input)=\"updateItemPrice(item, +$any($event.target).value)\" placeholder=\"請輸入單價\"\r\n                                min=\"0.01\" step=\"0.01\" required\r\n                                [class.is-invalid]=\"!item.CUnitPrice || item.CUnitPrice <= 0\" />\r\n                              <div class=\"invalid-feedback\" *ngIf=\"!item.CUnitPrice || item.CUnitPrice <= 0\">\r\n                                單價必須大於0\r\n                              </div>\r\n                            </div>\r\n                            <div class=\"col-md-4 mb-3\">\r\n                              <label class=\"form-label small required-field\">單位</label>\r\n                              <input type=\"text\" class=\"form-control form-control-sm\" [value]=\"item.CUnit\"\r\n                                (input)=\"updateItemUnit(item, $any($event.target).value)\" placeholder=\"請輸入具體單位（如：坪、組、個）\"\r\n                                maxlength=\"10\" required\r\n                                [class.is-invalid]=\"!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'\" />\r\n                              <div class=\"invalid-feedback\"\r\n                                *ngIf=\"!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'\">\r\n                                請輸入具體的單位，不能為空或預設值「式」\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"templateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"templateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模板模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"editTemplateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"editTemplateName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <span slot=\"tabTitle\">空間模板</span>\r\n\r\n                    <!-- 編輯模式下空間模板說明 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"fas fa-info-circle me-2\"></i>\r\n                        編輯模式下，空間配置請在模板詳情中進行管理。\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <span slot=\"tabTitle\">項目模板</span>\r\n\r\n                    <!-- 編輯模式下項目模板說明 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"fas fa-info-circle me-2\"></i>\r\n                        編輯模式下，項目配置及單價單位設定請在模板詳情中進行管理。\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"editTemplateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"editTemplateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-tag me-2 text-primary\"></i>模板名稱\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-toggle-on me-2 text-success\"></i>狀態\r\n                </label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\"\r\n                    [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    <i [class]=\"selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\"\r\n                      class=\"me-1\"></i>\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-plus me-2 text-info\"></i>建立時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-plus me-2 text-warning\"></i>建立者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-edit me-2 text-info\"></i>更新時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-edit me-2 text-warning\"></i>更新者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\"\r\n          style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- Loading 狀態 -->\r\n          <div *ngIf=\"isLoadingTemplateDetail\" class=\"text-center py-4\">\r\n            <i class=\"fas fa-spinner fa-spin me-2 text-primary\" style=\"font-size: 1.2rem;\"></i>\r\n            <span class=\"text-muted\">載入中...</span>\r\n          </div>\r\n\r\n          <!-- 空間列表 -->\r\n          <div *ngIf=\"!isLoadingTemplateDetail\">\r\n            <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n              <table class=\"table table-sm\">\r\n                <thead>\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"col-1\">#</th>\r\n                    <th scope=\"col\" class=\"col-7\">項目名稱</th>\r\n                    <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                    <td>{{ i + 1 }}</td>\r\n                    <td>\r\n                      <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CPart }}\r\n                    </td>\r\n                    <td>\r\n                      <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            <!-- 沒有空間時的提示 -->\r\n            <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAO7D,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;ICyDrFC,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAH,MAAA,CAAAI,aAAA,CAA8B;IAAA,EAAC;IAChGV,EAAA,CAAAW,SAAA,YAAgC;IAAAX,EAAA,CAAAY,MAAA,gCAClC;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAoCLb,EAAA,CAAAC,cAAA,iBACmD;IAAjDD,EAAA,CAAAE,UAAA,mBAAAY,mEAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,aAAA,CAAAZ,MAAA,CAAAI,aAAA,EAAAM,WAAA,CAAsC;IAAA,EAAC;IAChDhB,EAAA,CAAAW,SAAA,YAA2B;IAAAX,EAAA,CAAAY,MAAA,oBAC7B;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IACTb,EAAA,CAAAC,cAAA,iBAAkG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAiB,mEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAJ,WAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,cAAA,CAAAL,WAAA,CAAwB;IAAA,EAAC;IAC/FhB,EAAA,CAAAW,SAAA,YAA4B;IAAAX,EAAA,CAAAY,MAAA,oBAC9B;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAtBXb,EADF,CAAAC,cAAA,SAA0C,SACpC;IACFD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA4B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAEnCb,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAa,YAAA,EAAO,EACJ;IACLb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAmD;;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC5Db,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAY,MAAA,IAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAErCb,EADF,CAAAC,cAAA,cAA0B,kBAEsC;IAA5DD,EAAA,CAAAE,UAAA,mBAAAoB,0DAAA;MAAA,MAAAN,WAAA,GAAAhB,EAAA,CAAAI,aAAA,CAAAmB,GAAA,EAAAN,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAiB,sBAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoB,kBAAA,CAAAV,WAAA,EAAAQ,sBAAA,CAAiD;IAAA,EAAC;IAC3DxB,EAAA,CAAAW,SAAA,aAA0B;IAAAX,EAAA,CAAAY,MAAA,qBAC5B;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAKTb,EAJA,CAAA2B,UAAA,KAAAC,0CAAA,qBACmD,KAAAC,0CAAA,qBAG+C;IAItG7B,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IAvBDb,EAAA,CAAA8B,SAAA,GACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAf,WAAA,CAAAgB,aAAA,sCAAAhB,WAAA,CAAAgB,aAAA,+CACF;IACIhC,EAAA,CAAA8B,SAAA,GAA4B;IAA5B9B,EAAA,CAAAiC,iBAAA,CAAAjB,WAAA,CAAAkB,aAAA,CAA4B;IAEVlC,EAAA,CAAA8B,SAAA,GAAwE;IAAxE9B,EAAA,CAAAmC,UAAA,YAAAnB,WAAA,CAAAoB,OAAA,6CAAwE;IAC1FpC,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAf,WAAA,CAAAoB,OAAA,8CACF;IAEEpC,EAAA,CAAA8B,SAAA,GAAmD;IAAnD9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,QAAArB,WAAA,CAAAsB,SAAA,sBAAmD;IACnDtC,EAAA,CAAA8B,SAAA,GAA8B;IAA9B9B,EAAA,CAAAiC,iBAAA,CAAAjB,WAAA,CAAAuB,QAAA,QAA8B;IAMvBvC,EAAA,CAAA8B,SAAA,GAAc;IAAd9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAkC,QAAA,CAAc;IAIdxC,EAAA,CAAA8B,SAAA,EAAc;IAAd9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAmC,QAAA,CAAc;;;;;IAMzBzC,EADF,CAAAC,cAAA,SAAsC,aACI;IACtCD,EAAA,CAAAW,SAAA,YAAuC;IAAAX,EAAA,CAAAY,MAAA,wDACzC;IACFZ,EADE,CAAAa,YAAA,EAAK,EACF;;;;;;IA+FWb,EAAA,CAAAC,cAAA,eAC0E;IAAtCD,EAAA,CAAAE,UAAA,mBAAAwC,sEAAA;MAAA,MAAAC,SAAA,GAAA3C,EAAA,CAAAI,aAAA,CAAAwC,IAAA,EAAA3B,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuC,oBAAA,CAAAF,SAAA,CAA2B;IAAA,EAAC;IAErE3C,EADF,CAAAC,cAAA,eAAwB,eACE;IAAAD,EAAA,CAAAY,MAAA,GAAiB;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC/Cb,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAY,MAAA,GAA4B;IAE5DZ,EAF4D,CAAAa,YAAA,EAAM,EAC1D,EACF;;;;IALJb,EAAA,CAAA8C,WAAA,aAAAH,SAAA,CAAAI,QAAA,CAAiC;IAEP/C,EAAA,CAAA8B,SAAA,GAAiB;IAAjB9B,EAAA,CAAAiC,iBAAA,CAAAU,SAAA,CAAAK,KAAA,CAAiB;IACbhD,EAAA,CAAA8B,SAAA,GAA4B;IAA5B9B,EAAA,CAAAiC,iBAAA,CAAAU,SAAA,CAAAM,SAAA,QAA4B;;;;;IAM9DjD,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAW,SAAA,YAAuC;IAAAX,EAAA,CAAAY,MAAA,8DACzC;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;;IAKJb,EAFF,CAAAC,cAAA,eAC4C,eACmB;IAC3DD,EAAA,CAAAY,MAAA,eAAE;IAAAZ,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IAACb,EAAA,CAAAY,MAAA,2BACtE;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,yBAC+E;IAD/DD,EAAA,CAAAkD,gBAAA,wBAAAC,sFAAAC,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAiD,cAAA,EAAAH,MAAA,MAAA9C,MAAA,CAAAiD,cAAA,GAAAH,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAyB;IACFpD,EAAA,CAAAE,UAAA,wBAAAiD,sFAAAC,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAkD,gBAAA,CAAAJ,MAAA,CAAwB;IAAA,EAAC;IAEhFpD,EADE,CAAAa,YAAA,EAAiB,EACb;;;;IALmCb,EAAA,CAAA8B,SAAA,GAAuB;IAAvB9B,EAAA,CAAAiC,iBAAA,CAAA3B,MAAA,CAAAmD,iBAAA,CAAuB;IAE9CzD,EAAA,CAAA8B,SAAA,GAAyB;IAAzB9B,EAAA,CAAA0D,gBAAA,SAAApD,MAAA,CAAAiD,cAAA,CAAyB;IACvCvD,EADwC,CAAAmC,UAAA,aAAA7B,MAAA,CAAAqD,aAAA,CAA0B,mBAAArD,MAAA,CAAAmD,iBAAA,CAC9B;;;;;;IAStCzD,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAY,MAAA,GACA;IAAAZ,EAAA,CAAAC,cAAA,kBACkE;IAAhED,EAAA,CAAAE,UAAA,mBAAA0D,gFAAA;MAAA,MAAAC,SAAA,GAAA7D,EAAA,CAAAI,aAAA,CAAA0D,IAAA,EAAA7C,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyD,mBAAA,CAAAF,SAAA,CAA0B;IAAA,EAAC;IACxC7D,EADoE,CAAAa,YAAA,EAAS,EACtE;;;;IAHLb,EAAA,CAAA8B,SAAA,EACA;IADA9B,EAAA,CAAA+B,kBAAA,MAAA8B,SAAA,CAAAb,KAAA,MACA;;;;;IAJJhD,EADF,CAAAC,cAAA,cAA+D,iBACxB;IAAAD,EAAA,CAAAY,MAAA,GAA+C;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAC5Fb,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAA2B,UAAA,IAAAqC,uDAAA,oBAA4F;IAMhGhE,EADE,CAAAa,YAAA,EAAM,EACF;;;;IARiCb,EAAA,CAAA8B,SAAA,GAA+C;IAA/C9B,EAAA,CAAA+B,kBAAA,2CAAAzB,MAAA,CAAA2D,yBAAA,CAAAC,MAAA,MAA+C;IAEpBlE,EAAA,CAAA8B,SAAA,GAA4B;IAA5B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA2D,yBAAA,CAA4B;;;;;;IAmD1FjE,EAAA,CAAAC,cAAA,eACuE;IAApCD,EAAA,CAAAE,UAAA,mBAAAiE,sEAAA;MAAA,MAAAC,QAAA,GAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA,EAAApD,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgE,mBAAA,CAAAF,QAAA,CAAyB;IAAA,EAAC;IAElEpE,EADF,CAAAC,cAAA,eAAwB,eACE;IAAAD,EAAA,CAAAY,MAAA,GAAgB;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC9Cb,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAY,MAAA,GAA2B;IAE3DZ,EAF2D,CAAAa,YAAA,EAAM,EACzD,EACF;;;;IALJb,EAAA,CAAA8C,WAAA,aAAAsB,QAAA,CAAArB,QAAA,CAAgC;IAEN/C,EAAA,CAAA8B,SAAA,GAAgB;IAAhB9B,EAAA,CAAAiC,iBAAA,CAAAmC,QAAA,CAAApB,KAAA,CAAgB;IACZhD,EAAA,CAAA8B,SAAA,GAA2B;IAA3B9B,EAAA,CAAAiC,iBAAA,CAAAmC,QAAA,CAAAnB,SAAA,QAA2B;;;;;IAM7DjD,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAW,SAAA,YAAuC;IAAAX,EAAA,CAAAY,MAAA,8DACzC;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;;IAKJb,EAFF,CAAAC,cAAA,eAC0C,eACqB;IAC3DD,EAAA,CAAAY,MAAA,eAAE;IAAAZ,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IAACb,EAAA,CAAAY,MAAA,2BACrE;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,yBAC6E;IAD7DD,EAAA,CAAAkD,gBAAA,wBAAAqB,sFAAAnB,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmE,aAAA,EAAArB,MAAA,MAAA9C,MAAA,CAAAmE,aAAA,GAAArB,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAwB;IACFpD,EAAA,CAAAE,UAAA,wBAAAqE,sFAAAnB,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAoE,eAAA,CAAAtB,MAAA,CAAuB;IAAA,EAAC;IAE9EpD,EADE,CAAAa,YAAA,EAAiB,EACb;;;;IALmCb,EAAA,CAAA8B,SAAA,GAAsB;IAAtB9B,EAAA,CAAAiC,iBAAA,CAAA3B,MAAA,CAAAqE,gBAAA,CAAsB;IAE7C3E,EAAA,CAAA8B,SAAA,GAAwB;IAAxB9B,EAAA,CAAA0D,gBAAA,SAAApD,MAAA,CAAAmE,aAAA,CAAwB;IACtCzE,EADuC,CAAAmC,UAAA,aAAA7B,MAAA,CAAAsE,YAAA,CAAyB,mBAAAtE,MAAA,CAAAqE,gBAAA,CAC7B;;;;;IAsB/B3E,EAAA,CAAAW,SAAA,aACoB;;;;;IACpBX,EAAA,CAAAW,SAAA,aACyB;;;;;IAY3BX,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAY,MAAA,8CACF;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;IAQNb,EAAA,CAAAC,cAAA,eAC+E;IAC7ED,EAAA,CAAAY,MAAA,iIACF;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;;IAhCJb,EAHN,CAAAC,cAAA,cAA8E,eACjD,aACc,gBAEQ;IAC3CD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IAGPb,EAFA,CAAA2B,UAAA,IAAAkD,0DAAA,iBACgB,IAAAC,0DAAA,iBAEK;IACrB9E,EAAA,CAAAC,cAAA,kBAC6B;IADmBD,EAAA,CAAAE,UAAA,mBAAA6E,+EAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAI,aAAA,CAAA6E,IAAA,EAAAhE,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,kBAAA,CAAAF,QAAA,CAAwB;IAAA,EAAC;IAEpFhF,EAD+B,CAAAa,YAAA,EAAS,EAClC;IACNb,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAY,MAAA,GAA2B;IACvDZ,EADuD,CAAAa,YAAA,EAAQ,EACzD;IAEJb,EADF,CAAAC,cAAA,gBAA2B,kBACsB;IAAAD,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACzDb,EAAA,CAAAC,cAAA,kBAGkE;IAFhED,EAAA,CAAAE,UAAA,mBAAAiF,+EAAA/B,MAAA;MAAA,MAAA4B,QAAA,GAAAhF,EAAA,CAAAI,aAAA,CAAA6E,IAAA,EAAAhE,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8E,eAAA,CAAAJ,QAAA,GAAA5B,MAAA,CAAAiC,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAD7DtF,EAAA,CAAAa,YAAA,EAGkE;IAClEb,EAAA,CAAA2B,UAAA,KAAA4D,6DAAA,mBAA+E;IAGjFvF,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,gBAA2B,kBACsB;IAAAD,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACzDb,EAAA,CAAAC,cAAA,kBAG8F;IAF5FD,EAAA,CAAAE,UAAA,mBAAAsF,+EAAApC,MAAA;MAAA,MAAA4B,QAAA,GAAAhF,EAAA,CAAAI,aAAA,CAAA6E,IAAA,EAAAhE,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmF,cAAA,CAAAT,QAAA,EAAA5B,MAAA,CAAAiC,MAAA,CAAAC,KAAA,CAA+C;IAAA,EAAC;IAD3DtF,EAAA,CAAAa,YAAA,EAG8F;IAC9Fb,EAAA,CAAA2B,UAAA,KAAA+D,6DAAA,mBAC+E;IAInF1F,EADE,CAAAa,YAAA,EAAM,EACF;;;;;IAlCyBb,EAAA,CAAA8B,SAAA,GAAyC;IAChE9B,EADuB,CAAA8C,WAAA,kBAAAxC,MAAA,CAAAqF,WAAA,CAAAX,QAAA,EAAyC,mBAAA1E,MAAA,CAAAqF,WAAA,CAAAX,QAAA,EACtB;IAC1ChF,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAiD,QAAA,CAAAhC,KAAA,MACF;IACkDhD,EAAA,CAAA8B,SAAA,EAAuB;IAAvB9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAqF,WAAA,CAAAX,QAAA,EAAuB;IAEfhF,EAAA,CAAA8B,SAAA,EAAwB;IAAxB9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAqF,WAAA,CAAAX,QAAA,EAAwB;IAK1DhF,EAAA,CAAA8B,SAAA,GAA2B;IAA3B9B,EAAA,CAAAiC,iBAAA,CAAA+C,QAAA,CAAA/B,SAAA,QAA2B;IAOnDjD,EAAA,CAAA8B,SAAA,GAA6D;IAA7D9B,EAAA,CAAA8C,WAAA,gBAAAkC,QAAA,CAAAY,UAAA,IAAAZ,QAAA,CAAAY,UAAA,MAA6D;IAHL5F,EAAA,CAAAmC,UAAA,UAAA6C,QAAA,CAAAY,UAAA,CAAyB;IAIpD5F,EAAA,CAAA8B,SAAA,EAA8C;IAA9C9B,EAAA,CAAAmC,UAAA,UAAA6C,QAAA,CAAAY,UAAA,IAAAZ,QAAA,CAAAY,UAAA,MAA8C;IAS3E5F,EAAA,CAAA8B,SAAA,GAAyF;IAAzF9B,EAAA,CAAA8C,WAAA,gBAAAkC,QAAA,CAAAa,KAAA,IAAAb,QAAA,CAAAa,KAAA,CAAAC,IAAA,aAAAd,QAAA,CAAAa,KAAA,CAAAC,IAAA,gBAAyF;IAHnC9F,EAAA,CAAAmC,UAAA,UAAA6C,QAAA,CAAAa,KAAA,CAAoB;IAKzE7F,EAAA,CAAA8B,SAAA,EAA0E;IAA1E9B,EAAA,CAAAmC,UAAA,UAAA6C,QAAA,CAAAa,KAAA,IAAAb,QAAA,CAAAa,KAAA,CAAAC,IAAA,aAAAd,QAAA,CAAAa,KAAA,CAAAC,IAAA,gBAA0E;;;;;IAxCjF9F,EAFJ,CAAAC,cAAA,cAA8D,eACQ,iBAC7B;IAAAD,EAAA,CAAAY,MAAA,GAA8C;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAC3Fb,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAY,MAAA,GACA;IAAAZ,EAAA,CAAAW,SAAA,aAAqE;IAEzEX,EADE,CAAAa,YAAA,EAAQ,EACJ;IACNb,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAA2B,UAAA,IAAAoE,sDAAA,qBAA8E;IAuClF/F,EADE,CAAAa,YAAA,EAAM,EACF;;;;IA9CmCb,EAAA,CAAA8B,SAAA,GAA8C;IAA9C9B,EAAA,CAAA+B,kBAAA,2CAAAzB,MAAA,CAAA0F,wBAAA,CAAA9B,MAAA,MAA8C;IAEjFlE,EAAA,CAAA8B,SAAA,GACA;IADA9B,EAAA,CAAAiG,kBAAA,sCAAA3F,MAAA,CAAA4F,kBAAA,WAAA5F,MAAA,CAAA0F,wBAAA,CAAA9B,MAAA,MACA;IAIgClE,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA0F,wBAAA,CAA6B;;;;;;IApMnFhG,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAW,SAAA,YAAoD;IAAAX,EAAA,CAAAY,MAAA,gCACtD;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAiG,kEAAA;MAAA,MAAAC,OAAA,GAAApG,EAAA,CAAAI,aAAA,CAAAiG,GAAA,EAAAC,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5FpG,EAAA,CAAAW,SAAA,YAA4B;IAEhCX,EADE,CAAAa,YAAA,EAAS,EACM;IAQPb,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAENb,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAAkD,gBAAA,2BAAAsD,0EAAApD,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmG,cAAA,CAAAvE,aAAA,EAAAkB,MAAA,MAAA9C,MAAA,CAAAmG,cAAA,CAAAvE,aAAA,GAAAkB,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAA0C;IAAqBpD,EAAA,CAAAE,UAAA,mCAAAwG,kFAAA;MAAA,MAAAN,OAAA,GAAApG,EAAA,CAAAI,aAAA,CAAAiG,GAAA,EAAAC,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAyBF,MAAA,CAAAqG,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAKhHpG,EANQ,CAAAa,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAb,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAGJb,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAA0G,mEAAA;MAAA5G,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,GAAA1B,MAAA,CAAAR,gBAAA,CAAA+G,aAAA;IAAA,EAAuE;IAMjE7G,EAJN,CAAAC,cAAA,eAAkB,eAEM,eACE,iBAG0B;IAD5CD,EAAA,CAAAkD,gBAAA,2BAAA4D,0EAAA1D,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAyG,kBAAA,EAAA3D,MAAA,MAAA9C,MAAA,CAAAyG,kBAAA,GAAA3D,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAgC;IAACpD,EAAA,CAAAE,UAAA,yBAAA8G,wEAAA;MAAAhH,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA2G,aAAA,EAAe;IAAA,EAAC;IAEpEjH,EAHE,CAAAa,YAAA,EAE8C,EAC1C;IAEJb,EADF,CAAAC,cAAA,eAAsB,iBAG0B;IAD5CD,EAAA,CAAAkD,gBAAA,2BAAAgE,0EAAA9D,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAA6G,mBAAA,EAAA/D,MAAA,MAAA9C,MAAA,CAAA6G,mBAAA,GAAA/D,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAiC;IAACpD,EAAA,CAAAE,UAAA,yBAAAkH,wEAAA;MAAApH,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA2G,aAAA,EAAe;IAAA,EAAC;IAErEjH,EAHE,CAAAa,YAAA,EAE8C,EAC1C;IAEJb,EADF,CAAAC,cAAA,eAAsB,kBAC2D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAmH,mEAAA;MAAArH,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgH,YAAA,EAAc;IAAA,EAAC;IAC5EtH,EAAA,CAAAW,SAAA,aAA2B;IAC7BX,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,kBAAmE;IAA1BD,EAAA,CAAAE,UAAA,mBAAAqH,mEAAA;MAAAvH,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2G,aAAA,EAAe;IAAA,EAAC;IAChEjH,EAAA,CAAAW,SAAA,aAA6B;IAGnCX,EAFI,CAAAa,YAAA,EAAS,EACL,EACF;IAMAb,EAHN,CAAAC,cAAA,eAAmE,eACG,cAC3B,iBAEO;IAA1CD,EAAA,CAAAE,UAAA,oBAAAsH,mEAAA;MAAAxH,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAmH,eAAA,EAAiB;IAAA,EAAC;IAD9BzH,EAAA,CAAAa,YAAA,EAC4C;IAC5Cb,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAY,MAAA,4CAAM;IAC7DZ,EAD6D,CAAAa,YAAA,EAAQ,EAC/D;IACNb,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAY,MAAA,IAEF;IACFZ,EADE,CAAAa,YAAA,EAAQ,EACJ;IAGNb,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA2B,UAAA,KAAA+F,gDAAA,kBAC0E;IAM5E1H,EAAA,CAAAa,YAAA,EAAM;IAQNb,EALA,CAAA2B,UAAA,KAAAgG,gDAAA,kBAA8E,KAAAC,gDAAA,kBAMlC;IAQ9C5H,EAAA,CAAAa,YAAA,EAAM;IAGNb,EAAA,CAAA2B,UAAA,KAAAkG,gDAAA,kBAA+D;IAWnE7H,EADE,CAAAa,YAAA,EAAM,EACC;IACTb,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA4H,mEAAA;MAAA9H,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,GAAA1B,MAAA,CAAAR,gBAAA,CAAAiI,YAAA;IAAA,EAAsE;IAMhE/H,EAJN,CAAAC,cAAA,eAAkB,eAEM,eACE,iBAG0B;IAD5CD,EAAA,CAAAkD,gBAAA,2BAAA8E,0EAAA5E,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAA2H,iBAAA,EAAA7E,MAAA,MAAA9C,MAAA,CAAA2H,iBAAA,GAAA7E,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAA+B;IAACpD,EAAA,CAAAE,UAAA,yBAAAgI,wEAAA;MAAAlI,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA6H,YAAA,EAAc;IAAA,EAAC;IAElEnI,EAHE,CAAAa,YAAA,EAE8C,EAC1C;IAEJb,EADF,CAAAC,cAAA,eAAsB,iBAG0B;IAD5CD,EAAA,CAAAkD,gBAAA,2BAAAkF,0EAAAhF,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAA+H,kBAAA,EAAAjF,MAAA,MAAA9C,MAAA,CAAA+H,kBAAA,GAAAjF,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAgC;IAACpD,EAAA,CAAAE,UAAA,yBAAAoI,wEAAA;MAAAtI,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA6H,YAAA,EAAc;IAAA,EAAC;IAEnEnI,EAHE,CAAAa,YAAA,EAE8C,EAC1C;IAEJb,EADF,CAAAC,cAAA,eAAsB,kBAC0D;IAAxBD,EAAA,CAAAE,UAAA,mBAAAqI,mEAAA;MAAAvI,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkI,WAAA,EAAa;IAAA,EAAC;IAC3ExI,EAAA,CAAAW,SAAA,aAA2B;IAC7BX,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,kBAAkE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAuI,mEAAA;MAAAzI,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6H,YAAA,EAAc;IAAA,EAAC;IAC/DnI,EAAA,CAAAW,SAAA,aAA6B;IAGnCX,EAFI,CAAAa,YAAA,EAAS,EACL,EACF;IAMAb,EAHN,CAAAC,cAAA,eAAmE,eACG,cAC3B,iBAEM;IAAzCD,EAAA,CAAAE,UAAA,oBAAAwI,mEAAA;MAAA1I,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAqI,cAAA,EAAgB;IAAA,EAAC;IAD7B3I,EAAA,CAAAa,YAAA,EAC2C;IAC3Cb,EAAA,CAAAC,cAAA,iBAA0D;IAAAD,EAAA,CAAAY,MAAA,4CAAM;IAClEZ,EADkE,CAAAa,YAAA,EAAQ,EACpE;IACNb,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAY,MAAA,IAEF;IACFZ,EADE,CAAAa,YAAA,EAAQ,EACJ;IAGNb,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA2B,UAAA,KAAAiH,gDAAA,kBACuE;IAMzE5I,EAAA,CAAAa,YAAA,EAAM;IAQNb,EALA,CAAA2B,UAAA,KAAAkH,gDAAA,kBAAwF,KAAAC,gDAAA,kBAM9C;IAQ5C9I,EAAA,CAAAa,YAAA,EAAM;IAGNb,EAAA,CAAA2B,UAAA,KAAAoH,gDAAA,kBAA8D;IAuD5E/I,EANY,CAAAa,YAAA,EAAM,EACC,EACC,EACR,EACF,EACF,EACF;IAMAb,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAY,MAAA,sBACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAGJb,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEe;IADZD,EAAA,CAAAkD,gBAAA,2BAAA8F,8EAAA5F,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiG,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmG,cAAA,CAAArE,OAAA,EAAAgB,MAAA,MAAA9C,MAAA,CAAAmG,cAAA,CAAArE,OAAA,GAAAgB,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAoC;IAG/DpD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAW,SAAA,aAAqD;IAAAX,EAAA,CAAAY,MAAA,qBACvD;IACFZ,EADE,CAAAa,YAAA,EAAO,EACG;IAEVb,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAW,SAAA,aAAoD;IAAAX,EAAA,CAAAY,MAAA,qBACtD;IAWlBZ,EAXkB,CAAAa,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EAGF,EACO;IAGbb,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA+I,mEAAA;MAAA,MAAA7C,OAAA,GAAApG,EAAA,CAAAI,aAAA,CAAAiG,GAAA,EAAAC,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExEpG,EAAA,CAAAW,SAAA,aAAiC;IAAAX,EAAA,CAAAY,MAAA,qBACnC;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,mBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAgJ,mEAAA;MAAA,MAAA9C,OAAA,GAAApG,EAAA,CAAAI,aAAA,CAAAiG,GAAA,EAAAC,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqG,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAE1DpG,EAAA,CAAAW,SAAA,cAAiC;IAAAX,EAAA,CAAAY,MAAA,qBACnC;IAEJZ,EAFI,CAAAa,YAAA,EAAS,EACM,EACT;;;;IA1QMb,EAAA,CAAA8B,SAAA,IAA0C;IAA1C9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAmG,cAAA,CAAAvE,aAAA,CAA0C;IAgBlBlC,EAAA,CAAA8B,SAAA,GAA0E;IAA1E9B,EAAA,CAAAmC,UAAA,WAAA7B,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAA+G,aAAA,CAA0E;IAQxF7G,EAAA,CAAA8B,SAAA,GAAgC;IAAhC9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAyG,kBAAA,CAAgC;IAKhC/G,EAAA,CAAA8B,SAAA,GAAiC;IAAjC9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA6G,mBAAA,CAAiC;IAiBKnH,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA6I,iBAAA,CAA6B;IAKnEnJ,EAAA,CAAA8B,SAAA,GAEF;IAFE9B,EAAA,CAAAoJ,kBAAA,aAAA9I,MAAA,CAAAmD,iBAAA,0BAAAnD,MAAA,CAAAiD,cAAA,SAAAjD,MAAA,CAAA+I,IAAA,CAAAC,IAAA,CAAAhJ,MAAA,CAAAmD,iBAAA,GAAAnD,MAAA,CAAAqD,aAAA,cAEF;IAK0C3D,EAAA,CAAA8B,SAAA,GAAkB;IAAlB9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAiJ,eAAA,CAAkB;IAUxDvJ,EAAA,CAAA8B,SAAA,EAAkC;IAAlC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAiJ,eAAA,CAAArF,MAAA,OAAkC;IAMrClE,EAAA,CAAA8B,SAAA,EAAuC;IAAvC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAmD,iBAAA,GAAAnD,MAAA,CAAAqD,aAAA,CAAuC;IAWzB3D,EAAA,CAAA8B,SAAA,EAA0C;IAA1C9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA2D,yBAAA,CAAAC,MAAA,KAA0C;IAYzClE,EAAA,CAAA8B,SAAA,EAAyE;IAAzE9B,EAAA,CAAAmC,UAAA,WAAA7B,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAAiI,YAAA,CAAyE;IAQvF/H,EAAA,CAAA8B,SAAA,GAA+B;IAA/B9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA2H,iBAAA,CAA+B;IAK/BjI,EAAA,CAAA8B,SAAA,GAAgC;IAAhC9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA+H,kBAAA,CAAgC;IAiBWrI,EAAA,CAAA8B,SAAA,GAA4B;IAA5B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAkJ,gBAAA,CAA4B;IAKvExJ,EAAA,CAAA8B,SAAA,GAEF;IAFE9B,EAAA,CAAAoJ,kBAAA,aAAA9I,MAAA,CAAAqE,gBAAA,0BAAArE,MAAA,CAAAmE,aAAA,SAAAnE,MAAA,CAAA+I,IAAA,CAAAC,IAAA,CAAAhJ,MAAA,CAAAqE,gBAAA,GAAArE,MAAA,CAAAsE,YAAA,cAEF;IAKyC5E,EAAA,CAAA8B,SAAA,GAA4B;IAA5B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAmJ,yBAAA,CAA4B;IAUjEzJ,EAAA,CAAA8B,SAAA,EAA4C;IAA5C9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAmJ,yBAAA,CAAAvF,MAAA,OAA4C;IAM/ClE,EAAA,CAAA8B,SAAA,EAAqC;IAArC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAqE,gBAAA,GAAArE,MAAA,CAAAsE,YAAA,CAAqC;IAWvB5E,EAAA,CAAA8B,SAAA,EAAyC;IAAzC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA0F,wBAAA,CAAA9B,MAAA,KAAyC;IAmEjClE,EAAA,CAAA8B,SAAA,GAAoC;IAApC9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAmG,cAAA,CAAArE,OAAA,CAAoC;IAEtDpC,EAAA,CAAA8B,SAAA,EAAW;IAAX9B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA8B,SAAA,GAAW;IAAX9B,EAAA,CAAAmC,UAAA,YAAW;;;;;;IAiCpCnC,EAFJ,CAAAC,cAAA,mBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAW,SAAA,aAA6C;IAAAX,EAAA,CAAAY,MAAA,gCAC/C;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAwJ,kEAAA;MAAA,MAAAC,OAAA,GAAA3J,EAAA,CAAAI,aAAA,CAAAwJ,IAAA,EAAAtD,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,OAAA,CAAAoD,OAAA,CAAY;IAAA,EAAC;IAE5F3J,EAAA,CAAAW,SAAA,YAA4B;IAEhCX,EADE,CAAAa,YAAA,EAAS,EACM;IAQPb,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAENb,EADF,CAAAC,cAAA,eAA8B,kBAG6C;IADvED,EAAA,CAAAkD,gBAAA,2BAAA2G,0EAAAzG,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAwJ,IAAA;MAAA,MAAAtJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmG,cAAA,CAAAvE,aAAA,EAAAkB,MAAA,MAAA9C,MAAA,CAAAmG,cAAA,CAAAvE,aAAA,GAAAkB,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAA0C;IAKpDpD,EANQ,CAAAa,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAb,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAGJb,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAA4J,mEAAA;MAAA9J,EAAA,CAAAI,aAAA,CAAAwJ,IAAA;MAAA,MAAAtJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,GAAA1B,MAAA,CAAAR,gBAAA,CAAA+G,aAAA;IAAA,EAAuE;IACvE7G,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IAI/Bb,EADF,CAAAC,cAAA,eAAkB,gBACc;IAC5BD,EAAA,CAAAW,SAAA,aAAuC;IACvCX,EAAA,CAAAY,MAAA,8IACF;IAEJZ,EAFI,CAAAa,YAAA,EAAM,EACF,EACC;IACTb,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA6J,mEAAA;MAAA/J,EAAA,CAAAI,aAAA,CAAAwJ,IAAA;MAAA,MAAAtJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,GAAA1B,MAAA,CAAAR,gBAAA,CAAAiI,YAAA;IAAA,EAAsE;IACtE/H,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IAI/Bb,EADF,CAAAC,cAAA,eAAkB,gBACc;IAC5BD,EAAA,CAAAW,SAAA,aAAuC;IACvCX,EAAA,CAAAY,MAAA,wLACF;IAOdZ,EAPc,CAAAa,YAAA,EAAM,EACF,EACC,EACC,EACR,EACF,EACF,EACF;IAMAb,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAY,MAAA,sBACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAGJb,EAFJ,CAAAC,cAAA,eAA8B,yBACE,sBAEe;IADRD,EAAA,CAAAkD,gBAAA,2BAAA8G,8EAAA5G,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAwJ,IAAA;MAAA,MAAAtJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmG,cAAA,CAAArE,OAAA,EAAAgB,MAAA,MAAA9C,MAAA,CAAAmG,cAAA,CAAArE,OAAA,GAAAgB,MAAA;MAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;IAAA,EAAoC;IAGnEpD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAW,SAAA,aAAqD;IAAAX,EAAA,CAAAY,MAAA,qBACvD;IACFZ,EADE,CAAAa,YAAA,EAAO,EACG;IAEVb,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAW,SAAA,aAAoD;IAAAX,EAAA,CAAAY,MAAA,qBACtD;IAWlBZ,EAXkB,CAAAa,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EAGF,EACO;IAGbb,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA+J,mEAAA;MAAA,MAAAN,OAAA,GAAA3J,EAAA,CAAAI,aAAA,CAAAwJ,IAAA,EAAAtD,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,OAAA,CAAAoD,OAAA,CAAY;IAAA,EAAC;IAExE3J,EAAA,CAAAW,SAAA,aAAiC;IAAAX,EAAA,CAAAY,MAAA,qBACnC;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,mBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAgK,mEAAA;MAAA,MAAAP,OAAA,GAAA3J,EAAA,CAAAI,aAAA,CAAAwJ,IAAA,EAAAtD,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqG,QAAA,CAAAgD,OAAA,CAAa;IAAA,EAAC;IAE1D3J,EAAA,CAAAW,SAAA,cAAgC;IAAAX,EAAA,CAAAY,MAAA,qBAClC;IAEJZ,EAFI,CAAAa,YAAA,EAAS,EACM,EACT;;;;IAzFMb,EAAA,CAAA8B,SAAA,IAA0C;IAA1C9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAmG,cAAA,CAAAvE,aAAA,CAA0C;IAgBlBlC,EAAA,CAAA8B,SAAA,GAA0E;IAA1E9B,EAAA,CAAAmC,UAAA,WAAA7B,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAA+G,aAAA,CAA0E;IAY1E7G,EAAA,CAAA8B,SAAA,GAAyE;IAAzE9B,EAAA,CAAAmC,UAAA,WAAA7B,MAAA,CAAAmG,cAAA,CAAAzE,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAAiI,YAAA,CAAyE;IA4B9D/H,EAAA,CAAA8B,SAAA,IAAoC;IAApC9B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAmG,cAAA,CAAArE,OAAA,CAAoC;IAE1DpC,EAAA,CAAA8B,SAAA,EAAW;IAAX9B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA8B,SAAA,GAAW;IAAX9B,EAAA,CAAAmC,UAAA,YAAW;;;;;IA0HhCnC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAW,SAAA,aAAmF;IACnFX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,4BAAM;IACjCZ,EADiC,CAAAa,YAAA,EAAO,EAClC;;;;;IAeIb,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAY,MAAA,GAAW;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACpBb,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAW,SAAA,aAA2C;IAAAX,EAAA,CAAAY,MAAA,GAC7C;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAW,SAAA,aAAqD;IAAAX,EAAA,CAAAY,MAAA,GACvD;IACFZ,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IAPCb,EAAA,CAAA8B,SAAA,GAAW;IAAX9B,EAAA,CAAAiC,iBAAA,CAAAkI,KAAA,KAAW;IAE8BnK,EAAA,CAAA8B,SAAA,GAC7C;IAD6C9B,EAAA,CAAA+B,kBAAA,KAAAqI,SAAA,CAAApH,KAAA,MAC7C;IAEuDhD,EAAA,CAAA8B,SAAA,GACvD;IADuD9B,EAAA,CAAA+B,kBAAA,KAAAqI,SAAA,CAAAnH,SAAA,aACvD;;;;;IAbAjD,EAJR,CAAAC,cAAA,eAAsE,iBACtC,YACrB,SACD,cAC4B;IAAAD,EAAA,CAAAY,MAAA,QAAC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACpCb,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAEtCZ,EAFsC,CAAAa,YAAA,EAAK,EACpC,EACC;IACRb,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA2B,UAAA,KAAA0I,4DAAA,iBAA8D;IAWpErK,EAFI,CAAAa,YAAA,EAAQ,EACF,EACJ;;;;IAXsBb,EAAA,CAAA8B,SAAA,IAAyB;IAAzB9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAgK,oBAAA,CAAyB;;;;;IAcrDtK,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAW,SAAA,YAAuC;IAAAX,EAAA,CAAAY,MAAA,0EACzC;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;IA3BRb,EAAA,CAAAC,cAAA,UAAsC;IAyBpCD,EAxBA,CAAA2B,UAAA,IAAA4I,sDAAA,oBAAsE,IAAAC,sDAAA,kBAwBa;IAGrFxK,EAAA,CAAAa,YAAA,EAAM;;;;IA3B2Bb,EAAA,CAAA8B,SAAA,EAAqC;IAArC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAgK,oBAAA,CAAApG,MAAA,KAAqC;IAwB9DlE,EAAA,CAAA8B,SAAA,EAAuC;IAAvC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAgK,oBAAA,CAAApG,MAAA,OAAuC;;;;;;IAxHnDlE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAW,SAAA,aAAyC;IAAAX,EAAA,CAAAY,MAAA,gCAC3C;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAuK,kEAAA;MAAA,MAAAC,OAAA,GAAA1K,EAAA,CAAAI,aAAA,CAAAuK,IAAA,EAAArE,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,OAAA,CAAAmE,OAAA,CAAY;IAAA,EAAC;IAE5F1K,EAAA,CAAAW,SAAA,YAA4B;IAEhCX,EADE,CAAAa,YAAA,EAAS,EACM;IAMXb,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAW,SAAA,cAAoD;IAAAX,EAAA,CAAAY,MAAA,iCACtD;IACFZ,EADE,CAAAa,YAAA,EAAK,EACD;IAKEb,EAJR,CAAAC,cAAA,gBAAuB,eACJ,cACO,gBACS,kBACgB;IACzCD,EAAA,CAAAW,SAAA,cAA4C;IAAAX,EAAA,CAAAY,MAAA,iCAC9C;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAY,MAAA,IAAkD;IAEtEZ,EAFsE,CAAAa,YAAA,EAAI,EAClE,EACF;IAGFb,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAW,SAAA,cAAkD;IAAAX,EAAA,CAAAY,MAAA,qBACpD;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IAENb,EADF,CAAAC,cAAA,cAAgB,gBAE4E;IACxFD,EAAA,CAAAW,SAAA,cACmB;IACnBX,EAAA,CAAAY,MAAA,IACF;IAGNZ,EAHM,CAAAa,YAAA,EAAO,EACL,EACA,EACF;IAGFb,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAW,SAAA,cAAmD;IAAAX,EAAA,CAAAY,MAAA,iCACrD;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAY,MAAA,IAA2E;;IAE/FZ,EAF+F,CAAAa,YAAA,EAAI,EAC3F,EACF;IAGFb,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAW,SAAA,cAAkD;IAAAX,EAAA,CAAAY,MAAA,2BACpD;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAY,MAAA,IAA6C;IAEjEZ,EAFiE,CAAAa,YAAA,EAAI,EAC7D,EACF;IAGFb,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAW,SAAA,cAAmD;IAAAX,EAAA,CAAAY,MAAA,iCACrD;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAY,MAAA,IAA2E;;IAE/FZ,EAF+F,CAAAa,YAAA,EAAI,EAC3F,EACF;IAGFb,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAW,SAAA,cAAkD;IAAAX,EAAA,CAAAY,MAAA,2BACpD;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAY,MAAA,IAA6C;IAKvEZ,EALuE,CAAAa,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAMFb,EAHJ,CAAAC,cAAA,gBAAyE,gBAEA,eACzB;IAC1CD,EAAA,CAAAW,SAAA,cAA6C;IAAAX,EAAA,CAAAY,MAAA,uCAC/C;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAY,MAAA,IAAuC;IACxEZ,EADwE,CAAAa,YAAA,EAAO,EACzE;IACNb,EAAA,CAAAC,cAAA,gBAAuB;IAQrBD,EANA,CAAA2B,UAAA,KAAAiJ,gDAAA,mBAA8D,KAAAC,gDAAA,kBAMxB;IA+B5C7K,EAFI,CAAAa,YAAA,EAAM,EACF,EACO;IAGbb,EADF,CAAAC,cAAA,0BAAgH,mBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAA4K,mEAAA;MAAA,MAAAJ,OAAA,GAAA1K,EAAA,CAAAI,aAAA,CAAAuK,IAAA,EAAArE,SAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,OAAA,CAAAmE,OAAA,CAAY;IAAA,EAAC;IAE3D1K,EAAA,CAAAW,SAAA,aAAiC;IAAAX,EAAA,CAAAY,MAAA,qBACnC;IAEJZ,EAFI,CAAAa,YAAA,EAAS,EACM,EACT;;;;IA9GoBb,EAAA,CAAA8B,SAAA,IAAkD;IAAlD9B,EAAA,CAAAiC,iBAAA,EAAA3B,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAA7I,aAAA,SAAkD;IAU9DlC,EAAA,CAAA8B,SAAA,GAAuF;IAAvF9B,EAAA,CAAAmC,UAAA,aAAA7B,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAA3I,OAAA,8CAAuF;IACpFpC,EAAA,CAAA8B,SAAA,EAA+F;IAA/F9B,EAAA,CAAAgL,UAAA,EAAA1K,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAA3I,OAAA,wDAA+F;IAElGpC,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,OAAAzB,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAA3I,OAAA,+CACF;IAScpC,EAAA,CAAA8B,SAAA,GAA2E;IAA3E9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,SAAA/B,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAAzI,SAAA,6BAA2E;IAQ3EtC,EAAA,CAAA8B,SAAA,GAA6C;IAA7C9B,EAAA,CAAAiC,iBAAA,EAAA3B,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAAxI,QAAA,SAA6C;IAQ7CvC,EAAA,CAAA8B,SAAA,GAA2E;IAA3E9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,SAAA/B,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAAE,SAAA,6BAA2E;IAQ3EjL,EAAA,CAAA8B,SAAA,GAA6C;IAA7C9B,EAAA,CAAAiC,iBAAA,EAAA3B,MAAA,CAAAyK,sBAAA,kBAAAzK,MAAA,CAAAyK,sBAAA,CAAAG,QAAA,SAA6C;IAcpClL,EAAA,CAAA8B,SAAA,GAAuC;IAAvC9B,EAAA,CAAA+B,kBAAA,YAAAzB,MAAA,CAAAgK,oBAAA,CAAApG,MAAA,wBAAuC;IAIhElE,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA6K,uBAAA,CAA6B;IAM7BnL,EAAA,CAAA8B,SAAA,EAA8B;IAA9B9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAA6K,uBAAA,CAA8B;;;ADzkB9C,OAAM,MAAOC,iBAAkB,SAAQ1L,aAAa;EASlD2L,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAAtC,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAvJ,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAA6L,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAvF,cAAc,GAAqB,EAAE;IACrC,KAAAwF,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,kBAAkB,GAAkB,IAAI;IAExC;IACA,KAAA5C,eAAe,GAAwB,EAAE;IACzC,KAAAtF,yBAAyB,GAAwB,EAAE;IACnD,KAAA8C,kBAAkB,GAAW,EAAE;IAC/B,KAAAI,mBAAmB,GAAW,EAAE;IAChC,KAAA5D,cAAc,GAAG,CAAC;IAClB,KAAAI,aAAa,GAAG,EAAE;IAClB,KAAAF,iBAAiB,GAAG,CAAC;IACrB,KAAA0F,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAM,yBAAyB,GAAuB,EAAE;IAClD,KAAAzD,wBAAwB,GAAuB,EAAE;IACjD,KAAAiC,iBAAiB,GAAW,EAAE;IAC9B,KAAAI,kBAAkB,GAAW,EAAE;IAC/B,KAAA5D,aAAa,GAAG,CAAC;IACjB,KAAAG,YAAY,GAAG,EAAE;IACjB,KAAAD,gBAAgB,GAAG,CAAC;IACpB,KAAA6E,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAuB,sBAAsB,GAAwB,IAAI;IAClD,KAAAT,oBAAoB,GAA8B,EAAE;IACpD,KAAAa,uBAAuB,GAAG,KAAK;IAE/B;IACA,KAAAiB,UAAU,GAAG,KAAK;EAxClB;EA0CSC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACdtK,aAAa,EAAE,IAAI,CAAC+J,aAAa,IAAI,IAAI;MACzC7J,OAAO,EAAE,IAAI,CAAC8J,YAAY;MAC1BlK,aAAa,EAAE,IAAI,CAACmK,kBAAkB;MACtCM,SAAS,EAAE,IAAI,CAACX,SAAS;MACzBY,QAAQ,EAAE,IAAI,CAACb;KAChB;IAED,IAAI,CAACL,gBAAgB,CAACmB,mCAAmC,CAAC;MAAEC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CAC/EhN,GAAG,CAACiN,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACf,YAAY,GAAGc,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9BjL,aAAa,EAAEgL,IAAI,CAAChL,aAAc;UAClCF,aAAa,EAAEkL,IAAI,CAAClL,aAAa;UAAE;UACnCM,SAAS,EAAE4K,IAAI,CAAC5K,SAAU;UAC1B2I,SAAS,EAAEiC,IAAI,CAACjC,SAAU;UAC1B1I,QAAQ,EAAE2K,IAAI,CAAC3K,QAAQ;UACvB2I,QAAQ,EAAEgC,IAAI,CAAChC,QAAQ;UACvB9I,OAAO,EAAE8K,IAAI,CAAC9K;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC2J,YAAY,GAAGe,QAAQ,CAACM,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAAC2B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAhB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACdxJ,KAAK,EAAE,IAAI,CAAC+D,kBAAkB,IAAI,IAAI;MACtC9D,SAAS,EAAE,IAAI,CAACkE,mBAAmB,IAAI,IAAI;MAC3C/E,OAAO,EAAE,CAAC;MAAE;MACZqK,SAAS,EAAE,IAAI,CAAClJ,cAAc;MAC9BmJ,QAAQ,EAAE,IAAI,CAAC/I;KAChB;IAED,IAAI,CAAC8H,aAAa,CAAC+B,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtEhN,GAAG,CAACiN,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACxD,eAAe,GAAGuD,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxBzK,KAAK,EAAEkK,IAAI,CAAClK,KAAM;UAClBC,SAAS,EAAEiK,IAAI,CAACjK,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACkB,yBAAyB,CAACyJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAChK,iBAAiB,GAAGqJ,QAAQ,CAACM,UAAU,IAAI,CAAC;QACjD,IAAI,CAACQ,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACL,SAAS,EAAE;EACf;EAEA;EACAM,QAAQA,CAAA;IACN,IAAI,CAAC/B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,gBAAgB,EAAE;EACzB;EAEAwB,OAAOA,CAAA;IACL,IAAI,CAAC7B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,gBAAgB,EAAE;EACzB;EAEA;EACAyB,6BAA6BA,CAAA;IAC3B,MAAMvB,OAAO,GAAG;MACdxJ,KAAK,EAAE,IAAI,CAACiF,iBAAiB,IAAI,IAAI;MACrChF,SAAS,EAAE,IAAI,CAACoF,kBAAkB,IAAI,IAAI;MAC1CjG,OAAO,EAAE,CAAC;MAAE;MACZqK,SAAS,EAAE,IAAI,CAAChI,aAAa;MAC7BiI,QAAQ,EAAE,IAAI,CAAC9H;KAChB;IAED,IAAI,CAAC6G,aAAa,CAAC+B,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtEhN,GAAG,CAACiN,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACtD,yBAAyB,GAAGqD,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UAC9DO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxBzK,KAAK,EAAEkK,IAAI,CAAClK,KAAM;UAClBC,SAAS,EAAEiK,IAAI,CAACjK,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACiD,wBAAwB,CAAC0H,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;UAC/E7H,UAAU,EAAE,IAAI,CAACI,wBAAwB,CAACgI,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE7H,UAAU,IAAI,CAAC;UAClGC,KAAK,EAAE,IAAI,CAACG,wBAAwB,CAACgI,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE5H,KAAK,IAAI;SACxF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAClB,gBAAgB,GAAGmI,QAAQ,CAACM,UAAU,IAAI,CAAC;QAChD,IAAI,CAACa,2BAA2B,EAAE;MACpC;IACF,CAAC,CAAC,CACH,CAACV,SAAS,EAAE;EACf;EAEA;EACAtG,aAAaA,CAAA;IACX,IAAI,CAAC1D,cAAc,GAAG,CAAC;IACvB,IAAI,CAACgJ,mBAAmB,EAAE;EAC5B;EAEAjF,YAAYA,CAAA;IACV,IAAI,CAACP,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC5D,cAAc,GAAG,CAAC;IACvB,IAAI,CAACgJ,mBAAmB,EAAE;EAC5B;EAEA;EACApE,YAAYA,CAAA;IACV,IAAI,CAAC1D,aAAa,GAAG,CAAC;IACtB,IAAI,CAACsJ,6BAA6B,EAAE;EACtC;EAEAvF,WAAWA,CAAA;IACT,IAAI,CAACP,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACI,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC5D,aAAa,GAAG,CAAC;IACtB,IAAI,CAACsJ,6BAA6B,EAAE;EACtC;EAEA;EACAG,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACrC,SAAS,GAAGqC,IAAI;IACrB,IAAI,CAAC7B,gBAAgB,EAAE;EACzB;EAEA9I,gBAAgBA,CAAC2K,IAAY;IAC3B,IAAI,CAAC5K,cAAc,GAAG4K,IAAI;IAC1B,IAAI,CAAC5B,mBAAmB,EAAE;EAC5B;EAEA7H,eAAeA,CAACyJ,IAAY;IAC1B,IAAI,CAAC1J,aAAa,GAAG0J,IAAI;IACzB,IAAI,CAACJ,6BAA6B,EAAE;EACtC;EAEA;EACAtN,eAAeA,CAAC2N,KAAuB;IACrC,IAAI,CAAChC,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC3F,cAAc,GAAG;MACpBrE,OAAO,EAAE,CAAC;MACVJ,aAAa,EAAElC,gBAAgB,CAAC+G,aAAa,CAAC;KAC/C;IACD,IAAI,CAAC5C,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC+B,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACuG,mBAAmB,EAAE;IAC1B,IAAI,CAACwB,6BAA6B,EAAE;IACpC,IAAI,CAACxC,aAAa,CAAC8C,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEArN,aAAaA,CAACkN,KAAuB,EAAEI,QAAsB;IAC3D,IAAI,CAACpC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC3F,cAAc,GAAG;MACpB0G,WAAW,EAAEqB,QAAQ,CAACrB,WAAW;MACjCjL,aAAa,EAAEsM,QAAQ,CAACtM,aAAa;MACrCF,aAAa,EAAEwM,QAAQ,CAACxM,aAAa,IAAIlC,gBAAgB,CAAC+G,aAAa;MACvEzE,OAAO,EAAEoM,QAAQ,CAACpM,OAAO,IAAI;KAC9B;IACD;IACA,IAAI,CAACmJ,aAAa,CAAC8C,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAhI,OAAOA,CAACkI,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEA;EACA,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACvC,UAAU,GAAG,OAAO,GAAG,OAAO;EAC5C;EAEAzF,QAAQA,CAAC8H,GAAQ;IACf,IAAI,CAAC,IAAI,CAACG,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACnI,cAAc,CAAC0G,WAAW,EAAE;MACnC,IAAI,CAAC0B,cAAc,CAACJ,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACK,cAAc,CAACL,GAAG,CAAC;IAC1B;EACF;EAEA;EACAG,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACnI,cAAc,CAACvE,aAAa,EAAE4D,IAAI,EAAE,EAAE;MAC9C,IAAI,CAAC4F,OAAO,CAAC2B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC5G,cAAc,CAACzE,aAAa,KAAK+M,SAAS,IAAI,IAAI,CAACtI,cAAc,CAACzE,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAAC0J,OAAO,CAAC2B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC5G,cAAc,CAACrE,OAAO,KAAK2M,SAAS,IAAI,IAAI,CAACtI,cAAc,CAACrE,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAACsJ,OAAO,CAAC2B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC5G,cAAc,CAAC0G,WAAW,IAAI,IAAI,CAAC1G,cAAc,CAACzE,aAAa,KAAKlC,gBAAgB,CAAC+G,aAAa,IAAI,IAAI,CAAC5C,yBAAyB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAACwH,OAAO,CAAC2B,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC5G,cAAc,CAAC0G,WAAW,IAAI,IAAI,CAAC1G,cAAc,CAACzE,aAAa,KAAKlC,gBAAgB,CAACiI,YAAY,IAAI,IAAI,CAAC/B,wBAAwB,CAAC9B,MAAM,KAAK,CAAC,EAAE;MACzJ,IAAI,CAACwH,OAAO,CAAC2B,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA;IACA,IAAI,IAAI,CAAC5G,cAAc,CAACzE,aAAa,KAAKlC,gBAAgB,CAACiI,YAAY,EAAE;MACvE,KAAK,MAAMmF,IAAI,IAAI,IAAI,CAAClH,wBAAwB,EAAE;QAChD;QACA,IAAIkH,IAAI,CAACtH,UAAU,KAAKmJ,SAAS,IAAI7B,IAAI,CAACtH,UAAU,KAAK,IAAI,IAAIsH,IAAI,CAACtH,UAAU,IAAI,CAAC,EAAE;UACrF,IAAI,CAAC8F,OAAO,CAAC2B,YAAY,CAAC,MAAMH,IAAI,CAAClK,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;QAEA;QACA,IAAI,CAACkK,IAAI,CAACrH,KAAK,IAAIqH,IAAI,CAACrH,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,IAAIoH,IAAI,CAACrH,KAAK,CAACC,IAAI,EAAE,KAAK,GAAG,EAAE;UACxE,IAAI,CAAC4F,OAAO,CAAC2B,YAAY,CAAC,MAAMH,IAAI,CAAClK,KAAK,wBAAwB,CAAC;UACnE,OAAO,KAAK;QACd;QAEA;QACA,IAAIgM,KAAK,CAAC9B,IAAI,CAACtH,UAAU,CAAC,EAAE;UAC1B,IAAI,CAAC8F,OAAO,CAAC2B,YAAY,CAAC,MAAMH,IAAI,CAAClK,KAAK,aAAa,CAAC;UACxD,OAAO,KAAK;QACd;QAEA;QACA,IAAIkK,IAAI,CAACrH,KAAK,CAACC,IAAI,EAAE,CAAC5B,MAAM,GAAG,EAAE,EAAE;UACjC,IAAI,CAACwH,OAAO,CAAC2B,YAAY,CAAC,MAAMH,IAAI,CAAClK,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;MACF;IACF;IAEA,OAAO,IAAI;EACb;EAEA;EACA8L,cAAcA,CAACL,GAAQ;IACrB,MAAMQ,YAAY,GAAqB;MACrC/M,aAAa,EAAE,IAAI,CAACuE,cAAc,CAACvE,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACyE,cAAc,CAACzE,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACqE,cAAc,CAACrE;KAC9B;IAED,IAAI,CAACoJ,gBAAgB,CAAC0D,gCAAgC,CAAC;MAAEtC,IAAI,EAAEqC;IAAY,CAAE,CAAC,CAACpC,IAAI,CACjFhN,GAAG,CAACiN,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAMmC,UAAU,GAAGC,QAAQ,CAACtC,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAACqC,mBAAmB,CAACF,UAAU,EAAEV,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAC/C,OAAO,CAAC2B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAsB,cAAcA,CAACJ,GAAQ;IACrB,MAAMQ,YAAY,GAAqB;MACrC9B,WAAW,EAAE,IAAI,CAAC1G,cAAc,CAAC0G,WAAW;MAC5CjL,aAAa,EAAE,IAAI,CAACuE,cAAc,CAACvE,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACyE,cAAc,CAACzE,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACqE,cAAc,CAACrE;KAC9B;IAED,IAAI,CAACoJ,gBAAgB,CAAC0D,gCAAgC,CAAC;MAAEtC,IAAI,EAAEqC;IAAY,CAAE,CAAC,CAACpC,IAAI,CACjFhN,GAAG,CAACiN,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACrB,OAAO,CAAC4D,aAAa,CAAC,QAAQ,CAAC;QACpCb,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAACpC,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACZ,OAAO,CAAC2B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA8B,mBAAmBA,CAACF,UAAkB,EAAEV,GAAQ;IAC9C,IAAIc,OAAO,GAAU,EAAE;IAEvB,IAAI,IAAI,CAAC9I,cAAc,CAACzE,aAAa,KAAKlC,gBAAgB,CAAC+G,aAAa,EAAE;MACxE;MACA0I,OAAO,GAAG,IAAI,CAACtL,yBAAyB,CAACgJ,GAAG,CAACuC,KAAK,KAAK;QACrDC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAEF,KAAK,CAAC/B,QAAQ;QAC1BzK,KAAK,EAAEwM,KAAK,CAACxM,KAAK;QAClBC,SAAS,EAAEuM,KAAK,CAACvM;OAClB,CAAC,CAAC;IACL,CAAC,MAAM,IAAI,IAAI,CAACwD,cAAc,CAACzE,aAAa,KAAKlC,gBAAgB,CAACiI,YAAY,EAAE;MAC9E;MACAwH,OAAO,GAAG,IAAI,CAACvJ,wBAAwB,CAACiH,GAAG,CAACC,IAAI,KAAK;QACnDuC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAExC,IAAI,CAACO,QAAQ;QACzBzK,KAAK,EAAEkK,IAAI,CAAClK,KAAK;QACjBC,SAAS,EAAEiK,IAAI,CAACjK,SAAS;QACzB2C,UAAU,EAAEsH,IAAI,CAACtH,UAAU;QAC3BC,KAAK,EAAEqH,IAAI,CAACrH;OACb,CAAC,CAAC;IACL;IAEA,IAAI0J,OAAO,CAACrL,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM+K,YAAY,GAAqB;QACrC9B,WAAW,EAAEgC,UAAU;QACvBjN,aAAa,EAAE,IAAI,CAACuE,cAAc,CAACvE,aAAa;QAChDF,aAAa,EAAE,IAAI,CAACyE,cAAc,CAACzE,aAAa;QAChDI,OAAO,EAAE,IAAI,CAACqE,cAAc,CAACrE,OAAO;QACpCuN,OAAO,EAAEJ;OACV;MAED,IAAI,CAAC/D,gBAAgB,CAAC0D,gCAAgC,CAAC;QAAEtC,IAAI,EAAEqC;MAAY,CAAE,CAAC,CAACpC,IAAI,CACjFhN,GAAG,CAACiN,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACrB,OAAO,CAAC4D,aAAa,CAAC,QAAQ,CAAC;UACpCb,GAAG,CAACC,KAAK,EAAE;UACX,IAAI,CAACpC,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACZ,OAAO,CAAC2B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,YAAY,CAAC;QAC7D;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAAC7B,OAAO,CAAC4D,aAAa,CAAC,QAAQ,CAAC;MACpCb,GAAG,CAACC,KAAK,EAAE;MACX,IAAI,CAACpC,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAjL,cAAcA,CAACmN,QAAsB;IACnC,IAAIoB,OAAO,CAAC,WAAWpB,QAAQ,CAACtM,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAACsJ,gBAAgB,CAACqE,kCAAkC,CAAC;QACvDjD,IAAI,EAAE;UAAEO,WAAW,EAAEqB,QAAQ,CAACrB;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLhN,GAAG,CAACiN,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACrB,OAAO,CAAC4D,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAChD,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACZ,OAAO,CAAC2B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACA7L,kBAAkBA,CAAC8M,QAAsB,EAAEJ,KAAuB;IAChE,IAAI,CAACrD,sBAAsB,GAAGyD,QAAQ;IACtC,IAAI,CAACrD,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACb,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACiB,aAAa,CAAC8C,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAM/B,OAAO,GAA8B;MACzC2C,UAAU,EAAEX,QAAQ,CAACrB;KACtB;IAED,IAAI,CAAC3B,gBAAgB,CAACsE,yCAAyC,CAAC;MAAElD,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrFhN,GAAG,CAACiN,QAAQ,IAAG;MACb,IAAI,CAAC3B,uBAAuB,GAAG,KAAK;MACpC,IAAI2B,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACzC,oBAAoB,GAAGwC,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDwC,UAAU,EAAExC,IAAI,CAACwC,UAAW;UAC5B1M,KAAK,EAAEkK,IAAI,CAAClK,KAAM;UAClBC,SAAS,EAAEiK,IAAI,CAACjK;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAACyI,OAAO,CAAC2B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA1K,oBAAoBA,CAAC2M,KAAwB;IAC3CA,KAAK,CAACzM,QAAQ,GAAG,CAACyM,KAAK,CAACzM,QAAQ;IAEhC,IAAIyM,KAAK,CAACzM,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACkB,yBAAyB,CAACyJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK+B,KAAK,CAAC/B,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAACxJ,yBAAyB,CAAC8L,IAAI,CAAC;UAAE,GAAGP;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAACvL,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC+L,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK+B,KAAK,CAAC/B,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACG,4BAA4B,EAAE;EACrC;EAEAnG,eAAeA,CAAA;IACb,IAAI,CAAC0B,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACI,eAAe,CAAC0G,OAAO,CAACT,KAAK,IAAG;MACnCA,KAAK,CAACzM,QAAQ,GAAG,IAAI,CAACoG,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAClF,yBAAyB,CAACyJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK+B,KAAK,CAAC/B,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAACxJ,yBAAyB,CAAC8L,IAAI,CAAC;YAAE,GAAGP;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAACvL,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC+L,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK+B,KAAK,CAAC/B,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEA1J,mBAAmBA,CAACyL,KAAwB;IAC1C,IAAI,CAACvL,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC+L,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK+B,KAAK,CAAC/B,QAAQ,CAAC;IAE1G,MAAMyC,cAAc,GAAG,IAAI,CAAC3G,eAAe,CAACyE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK+B,KAAK,CAAC/B,QAAQ,CAAC;IACpF,IAAIyC,cAAc,EAAE;MAClBA,cAAc,CAACnN,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAAC6K,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAACzE,iBAAiB,GAAG,IAAI,CAACI,eAAe,CAACrF,MAAM,GAAG,CAAC,IACtD,IAAI,CAACqF,eAAe,CAAC4G,KAAK,CAACX,KAAK,IAAIA,KAAK,CAACzM,QAAQ,CAAC;EACvD;EAEA;EACAuB,mBAAmBA,CAAC4I,IAAsB;IACxCA,IAAI,CAACnK,QAAQ,GAAG,CAACmK,IAAI,CAACnK,QAAQ;IAE9B,IAAImK,IAAI,CAACnK,QAAQ,EAAE;MACjB,IAAI,CAAC,IAAI,CAACiD,wBAAwB,CAAC0H,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE;QAC1E;QACA,MAAM2C,OAAO,GAAG;UACd,GAAGlD,IAAI;UACPtH,UAAU,EAAE,CAAC;UAAE;UACfC,KAAK,EAAE,EAAE,CAAC;SACX;QACD,IAAI,CAACG,wBAAwB,CAAC+J,IAAI,CAACK,OAAO,CAAC;MAC7C;IACF,CAAC,MAAM;MACL,IAAI,CAACpK,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACgK,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IACzG;IAEA,IAAI,CAACQ,2BAA2B,EAAE;EACpC;EAEAtF,cAAcA,CAAA;IACZ,IAAI,CAACa,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAACC,yBAAyB,CAACwG,OAAO,CAAC/C,IAAI,IAAG;MAC5CA,IAAI,CAACnK,QAAQ,GAAG,IAAI,CAACyG,gBAAgB;MACrC,IAAI,IAAI,CAACA,gBAAgB,EAAE;QACzB,IAAI,CAAC,IAAI,CAACxD,wBAAwB,CAAC0H,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE;UAC1E;UACA,MAAM2C,OAAO,GAAG;YACd,GAAGlD,IAAI;YACPtH,UAAU,EAAE,CAAC;YAAE;YACfC,KAAK,EAAE,EAAE,CAAC;WACX;UACD,IAAI,CAACG,wBAAwB,CAAC+J,IAAI,CAACK,OAAO,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAACpK,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACgK,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;MACzG;IACF,CAAC,CAAC;EACJ;EAEAvI,kBAAkBA,CAACgI,IAAsB;IACvC,IAAI,CAAClH,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACgK,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAEvG,MAAM4C,aAAa,GAAG,IAAI,CAAC5G,yBAAyB,CAACuE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAI4C,aAAa,EAAE;MACjBA,aAAa,CAACtN,QAAQ,GAAG,KAAK;IAChC;IAEA,IAAI,CAACkL,2BAA2B,EAAE;EACpC;EAEAA,2BAA2BA,CAAA;IACzB,IAAI,CAACzE,gBAAgB,GAAG,IAAI,CAACC,yBAAyB,CAACvF,MAAM,GAAG,CAAC,IAC/D,IAAI,CAACuF,yBAAyB,CAAC0G,KAAK,CAACjD,IAAI,IAAIA,IAAI,CAACnK,QAAQ,CAAC;EAC/D;EAEA;EACAqC,eAAeA,CAAC8H,IAAsB,EAAEoD,KAAa;IACnD;IACA,IAAItB,KAAK,CAACsB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7BA,KAAK,GAAG,CAAC;IACX;IAEApD,IAAI,CAACtH,UAAU,GAAG0K,KAAK;IACvB;IACA,MAAMD,aAAa,GAAG,IAAI,CAAC5G,yBAAyB,CAACuE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAI4C,aAAa,EAAE;MACjBA,aAAa,CAACzK,UAAU,GAAG0K,KAAK;IAClC;EACF;EAEA7K,cAAcA,CAACyH,IAAsB,EAAEqD,IAAY;IACjD;IACAA,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAACzK,IAAI,EAAE,GAAG,EAAE;IAE9BoH,IAAI,CAACrH,KAAK,GAAG0K,IAAI;IACjB;IACA,MAAMF,aAAa,GAAG,IAAI,CAAC5G,yBAAyB,CAACuE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAI4C,aAAa,EAAE;MACjBA,aAAa,CAACxK,KAAK,GAAG0K,IAAI;IAC5B;EACF;EAEA;EACA5K,WAAWA,CAACuH,IAAsB;IAChC,OAAO,CAAC,EAAEA,IAAI,CAACtH,UAAU,IAAIsH,IAAI,CAACtH,UAAU,GAAG,CAAC,IAC9CsH,IAAI,CAACrH,KAAK,IAAIqH,IAAI,CAACrH,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,IAAIoH,IAAI,CAACrH,KAAK,CAACC,IAAI,EAAE,KAAK,GAAG,CAAC;EACxE;EAEA;EACAI,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACF,wBAAwB,CAACgK,MAAM,CAAC9C,IAAI,IAAI,IAAI,CAACvH,WAAW,CAACuH,IAAI,CAAC,CAAC,CAAChJ,MAAM;EACpF;;;uCAvlBWkH,iBAAiB,EAAApL,EAAA,CAAAwQ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1Q,EAAA,CAAAwQ,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5Q,EAAA,CAAAwQ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA9Q,EAAA,CAAAwQ,iBAAA,CAAAK,EAAA,CAAAE,YAAA,GAAA/Q,EAAA,CAAAwQ,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAjR,EAAA,CAAAwQ,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjB/F,iBAAiB;MAAAgG,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;UC7D5BvR,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAW,SAAA,qBAAiC;UACnCX,EAAA,CAAAa,YAAA,EAAiB;UAIbb,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAW,SAAA,WAA+E;UAE7EX,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAY,MAAA,iUACF;UAGNZ,EAHM,CAAAa,YAAA,EAAI,EACA,EACF,EACF;UAMAb,EAHN,CAAAC,cAAA,cAA8B,cACN,eACqC,iBACT;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAQ;UAExDb,EADF,CAAAC,cAAA,yBAA6B,iBAE8B;UAAvDD,EAAA,CAAAkD,gBAAA,2BAAAuO,2DAAArO,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA1R,EAAA,CAAAsD,kBAAA,CAAAkO,GAAA,CAAAvF,aAAA,EAAA7I,MAAA,MAAAoO,GAAA,CAAAvF,aAAA,GAAA7I,MAAA;YAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;UAAA,EAA2B;UAACpD,EAAA,CAAAE,UAAA,yBAAAyR,yDAAA;YAAA3R,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA,OAAA1R,EAAA,CAAAQ,WAAA,CAAegR,GAAA,CAAA3D,QAAA,EAAU;UAAA,EAAC;UAG9D7N,EAJM,CAAAa,YAAA,EACyD,EAC3C,EACZ,EACF;UAIFb,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACf;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAQ;UAEhDb,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAAkD,gBAAA,2BAAA0O,+DAAAxO,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA1R,EAAA,CAAAsD,kBAAA,CAAAkO,GAAA,CAAAtF,YAAA,EAAA9I,MAAA,MAAAoO,GAAA,CAAAtF,YAAA,GAAA9I,MAAA;YAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;UAAA,EAA0B;UAACpD,EAAA,CAAAE,UAAA,4BAAA2R,gEAAA;YAAA7R,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA,OAAA1R,EAAA,CAAAQ,WAAA,CAAkBgR,GAAA,CAAA3D,QAAA,EAAU;UAAA,EAAC;UACnG7N,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAY;UACxCb,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAY;UACrCb,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAIjCZ,EAJiC,CAAAa,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAIFb,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACT;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAQ;UAExDb,EADF,CAAAC,cAAA,yBAA6B,qBAEK;UADqBD,EAAA,CAAAkD,gBAAA,2BAAA4O,+DAAA1O,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA1R,EAAA,CAAAsD,kBAAA,CAAAkO,GAAA,CAAArF,kBAAA,EAAA/I,MAAA,MAAAoO,GAAA,CAAArF,kBAAA,GAAA/I,MAAA;YAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;UAAA,EAAgC;UACnFpD,EAAA,CAAAE,UAAA,4BAAA6R,gEAAA;YAAA/R,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA,OAAA1R,EAAA,CAAAQ,WAAA,CAAkBgR,GAAA,CAAA3D,QAAA,EAAU;UAAA,EAAC;UAC7B7N,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAY;UACxCb,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAY;UACvCb,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAInCZ,EAJmC,CAAAa,YAAA,EAAY,EAC7B,EACE,EACZ,EACF;UAENb,EAAA,CAAAW,SAAA,cAEM;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAA8R,oDAAA;YAAAhS,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA,OAAA1R,EAAA,CAAAQ,WAAA,CAASgR,GAAA,CAAA1D,OAAA,EAAS;UAAA,EAAC;UACvE9N,EAAA,CAAAW,SAAA,aAAgC;UAAAX,EAAA,CAAAY,MAAA,qBAClC;UAAAZ,EAAA,CAAAa,YAAA,EAAS;UACTb,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAA+R,oDAAA;YAAAjS,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA,OAAA1R,EAAA,CAAAQ,WAAA,CAASgR,GAAA,CAAA3D,QAAA,EAAU;UAAA,EAAC;UAC3D7N,EAAA,CAAAW,SAAA,aAAkC;UAAAX,EAAA,CAAAY,MAAA,qBACpC;UAEJZ,EAFI,CAAAa,YAAA,EAAS,EACL,EACF;UAGJb,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAA2B,UAAA,KAAAuQ,oCAAA,qBAAmG;UAKzGlS,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAOEb,EAJR,CAAAC,cAAA,eAAmC,iBACc,aACtC,UACD,cACoC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAC/Cb,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAC/Cb,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAC7Cb,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAC/Cb,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAY,MAAA,0BAAG;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAC9Cb,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAE5CZ,EAF4C,CAAAa,YAAA,EAAK,EAC1C,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UA2BLD,EA1BA,CAAA2B,UAAA,KAAAwQ,gCAAA,mBAA0C,KAAAC,gCAAA,iBA0BJ;UAQ9CpS,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAAkD,gBAAA,wBAAAmP,iEAAAjP,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA1R,EAAA,CAAAsD,kBAAA,CAAAkO,GAAA,CAAA1F,SAAA,EAAA1I,MAAA,MAAAoO,GAAA,CAAA1F,SAAA,GAAA1I,MAAA;YAAA,OAAApD,EAAA,CAAAQ,WAAA,CAAA4C,MAAA;UAAA,EAAoB;UAClCpD,EAAA,CAAAE,UAAA,wBAAAmS,iEAAAjP,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAsR,GAAA;YAAA,OAAA1R,EAAA,CAAAQ,WAAA,CAAcgR,GAAA,CAAAtD,WAAA,CAAA9K,MAAA,CAAmB;UAAA,EAAC;UAGxCpD,EAFI,CAAAa,YAAA,EAAiB,EACF,EACT;UA8ZVb,EA3ZA,CAAA2B,UAAA,KAAA2Q,yCAAA,kCAAAtS,EAAA,CAAAuS,sBAAA,CAA8C,KAAAC,yCAAA,iCAAAxS,EAAA,CAAAuS,sBAAA,CAsSF,KAAAE,yCAAA,kCAAAzS,EAAA,CAAAuS,sBAAA,CAqHU;;;UA7gBxCvS,EAAA,CAAA8B,SAAA,IAA2B;UAA3B9B,EAAA,CAAA0D,gBAAA,YAAA8N,GAAA,CAAAvF,aAAA,CAA2B;UASgBjM,EAAA,CAAA8B,SAAA,GAA0B;UAA1B9B,EAAA,CAAA0D,gBAAA,YAAA8N,GAAA,CAAAtF,YAAA,CAA0B;UAC1DlM,EAAA,CAAA8B,SAAA,EAAc;UAAd9B,EAAA,CAAAmC,UAAA,eAAc;UACdnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UAU6BnC,EAAA,CAAA8B,SAAA,GAAgC;UAAhC9B,EAAA,CAAA0D,gBAAA,YAAA8N,GAAA,CAAArF,kBAAA,CAAgC;UAExEnM,EAAA,CAAA8B,SAAA,EAAc;UAAd9B,EAAA,CAAAmC,UAAA,eAAc;UACdnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UAwBgBnC,EAAA,CAAA8B,SAAA,IAAc;UAAd9B,EAAA,CAAAmC,UAAA,SAAAqP,GAAA,CAAAkB,QAAA,CAAc;UAqB/B1S,EAAA,CAAA8B,SAAA,IAAe;UAAf9B,EAAA,CAAAmC,UAAA,YAAAqP,GAAA,CAAAxF,YAAA,CAAe;UA0BnChM,EAAA,CAAA8B,SAAA,EAA+B;UAA/B9B,EAAA,CAAAmC,UAAA,SAAAqP,GAAA,CAAAxF,YAAA,CAAA9H,MAAA,OAA+B;UAU1BlE,EAAA,CAAA8B,SAAA,GAAoB;UAApB9B,EAAA,CAAA0D,gBAAA,SAAA8N,GAAA,CAAA1F,SAAA,CAAoB;UAAuB9L,EAAtB,CAAAmC,UAAA,aAAAqP,GAAA,CAAA3F,QAAA,CAAqB,mBAAA2F,GAAA,CAAAzF,YAAA,CAAgC;;;qBD1E1FpM,YAAY,EAAAgT,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZnT,YAAY,EAAAoT,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAxC,EAAA,CAAAyC,eAAA,EAAAzC,EAAA,CAAA0C,mBAAA,EAAA1C,EAAA,CAAA2C,qBAAA,EAAA3C,EAAA,CAAA4C,qBAAA,EAAA5C,EAAA,CAAA6C,gBAAA,EAAA7C,EAAA,CAAA8C,iBAAA,EAAA9C,EAAA,CAAA+C,iBAAA,EAAA/C,EAAA,CAAAgD,iBAAA,EAAAhD,EAAA,CAAAiD,cAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}