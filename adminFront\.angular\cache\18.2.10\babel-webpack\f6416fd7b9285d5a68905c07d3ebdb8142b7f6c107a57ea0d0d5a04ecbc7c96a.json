{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"ngb-pagination\", 3);\n    i0.ɵɵtwoWayListener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.Page, $event) || (ctx_r1.Page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pageChange());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r1.CollectionSize);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r1.Page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r1.PageSize)(\"maxSize\", 5)(\"boundaryLinks\", true);\n  }\n}\nfunction PaginationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u7E3D\\u8A18\\u9304\\u6578\\uFF1A\", ctx_r1.CollectionSize, \"\\n\");\n  }\n}\nexport let PaginationComponent = /*#__PURE__*/(() => {\n  class PaginationComponent {\n    constructor() {\n      this.PageChange = new EventEmitter();\n      this.PageSize = 0;\n      this.CollectionSize = 0;\n      this.PageSizeChange = new EventEmitter();\n      this.CollectionSizeChange = new EventEmitter();\n    }\n    ngOnInit() {}\n    pageChange() {\n      this.PageChange.emit(this.Page);\n    }\n    static {\n      this.ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PaginationComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PaginationComponent,\n        selectors: [[\"ngx-pagination\"]],\n        inputs: {\n          Page: \"Page\",\n          PageSize: \"PageSize\",\n          CollectionSize: \"CollectionSize\"\n        },\n        outputs: {\n          PageChange: \"PageChange\",\n          PageSizeChange: \"PageSizeChange\",\n          CollectionSizeChange: \"CollectionSizeChange\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"d-flex justify-content-center p-2 pagination-wrapper\", 4, \"ngIf\"], [\"class\", \"text-center pagination-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"p-2\", \"pagination-wrapper\"], [1, \"pagination-theme\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\", \"maxSize\", \"boundaryLinks\"], [1, \"text-center\", \"pagination-info\"]],\n        template: function PaginationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 2, 5, \"div\", 0)(1, PaginationComponent_div_1_Template, 2, 1, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          }\n        },\n        dependencies: [NgIf, NgbPagination],\n        styles: [\"@charset \\\"UTF-8\\\";.pagination-wrapper[_ngcontent-%COMP%]{margin:1rem 0;padding:.5rem}.pagination-info[_ngcontent-%COMP%]{margin-top:.75rem;font-size:.875rem;color:#5a5a5a;font-weight:500}.pagination-empty[_ngcontent-%COMP%]{color:#dc3545!important;font-weight:600;padding:1rem;background-color:#f8d7da;border:1px solid #DC3545;border-radius:.5rem;margin:1rem 0}  ngb-pagination .pagination{gap:.25rem;margin-bottom:0}  ngb-pagination .pagination .page-item .page-link{color:#ae9b66;background-color:#fff;border:1px solid #E9ECEF;border-radius:.375rem;padding:.5rem .75rem;font-size:.875rem;font-weight:500;transition:all .2s ease-in-out;box-shadow:none}  ngb-pagination .pagination .page-item .page-link:hover{background-color:#b8a6760d;color:#c4b382;border-color:#b8a6764d;box-shadow:0 1px 3px #b8a6761a;transform:translateY(-1px)}  ngb-pagination .pagination .page-item .page-link:focus{background-color:#b8a6760d;color:#c4b382;border-color:#b8a67680;box-shadow:0 0 0 .2rem #b8a67640;outline:none}  ngb-pagination .pagination .page-item.active .page-link{background:linear-gradient(135deg,#b8a676,#a69660);color:#fff;border-color:#ae9b66;box-shadow:0 2px 8px #b8a67626;font-weight:600}  ngb-pagination .pagination .page-item.active .page-link:hover,   ngb-pagination .pagination .page-item.active .page-link:focus{background:linear-gradient(135deg,#c4b382,#a89660);border-color:#c4b382;box-shadow:0 4px 12px #b8a67633;transform:none}  ngb-pagination .pagination .page-item.disabled .page-link{color:#ced4da;background-color:#f8f9fa;border-color:#e9ecef;cursor:not-allowed;opacity:.6}  ngb-pagination .pagination .page-item.disabled .page-link:hover,   ngb-pagination .pagination .page-item.disabled .page-link:focus{background-color:#f8f9fa;color:#ced4da;border-color:#e9ecef;box-shadow:none;transform:none}  ngb-pagination .pagination .page-item:first-child .page-link,   ngb-pagination .pagination .page-item:last-child .page-link{font-weight:600}@media (max-width: 576px){  ngb-pagination .pagination{gap:.125rem}  ngb-pagination .pagination .page-item .page-link{padding:.375rem .5rem;font-size:.75rem}  .text-center{font-size:.75rem}}[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item .page-link{background-color:#1a1a1a;color:#fff;border-color:#404040}[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item .page-link:hover{background-color:#b8a6761a;color:#b8a676}[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item.active .page-link{background:linear-gradient(135deg,#b8a676,#a69660);color:#fff}[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item.disabled .page-link{background-color:#2d2d2d;color:#ccc}[data-theme=dark][_ngcontent-%COMP%]     .text-center{color:#ccc}[data-theme=dark][_ngcontent-%COMP%]     .text-center.text-danger{background-color:#b8a6761a;border-color:#c82333}\"]\n      });\n    }\n  }\n  return PaginationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}