{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule } from '@nebular/theme';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nlet TemplateSelectorComponent = class TemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.CTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n    // 新增：模板類型選擇相關屬性\n    this.selectedTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\n  }\n  ngOnInit() {\n    // 初始化選擇的模板類型\n    this.selectedTemplateType = this.CTemplateType;\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數，使用當前選擇的模板類型\n    const getTemplateListArgs = {\n      CTemplateType: this.selectedTemplateType,\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  // 新增：當模板類型選擇變更時的處理\n  onTemplateTypeChange() {\n    // 清空當前選擇\n    this.resetSelections();\n    // 重新載入對應類型的模板\n    this.loadTemplatesFromAPI();\n  }\n  // 新增：獲取當前選擇的模板類型名稱\n  getCurrentTemplateTypeName() {\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n  }\n  // 新增：根據模板類型獲取對話框標題\n  getDialogTitle() {\n    const templateTypeName = this.getCurrentTemplateTypeName();\n    return `${templateTypeName}選擇器`;\n  }\n  // 新增：根據模板類型獲取選擇區域標題\n  getSelectionTitle() {\n    const templateTypeName = this.getCurrentTemplateTypeName();\n    return `選擇${templateTypeName}項目`;\n  }\n  // 新增：根據模板類型獲取確認套用標題\n  getConfirmationTitle() {\n    const templateTypeName = this.getCurrentTemplateTypeName();\n    return `確認套用${templateTypeName}`;\n  }\n  // 新增：根據模板類型獲取套用摘要文案\n  getApplySummaryText() {\n    const templateTypeName = this.getCurrentTemplateTypeName();\n    const count = this.getSelectedItems().length;\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return `將套用 <strong>${templateTypeName}</strong>：${count}個空間配置項目`;\n    } else {\n      return `將套用 <strong>${templateTypeName}</strong>：${count}個工程項目`;\n    }\n  }\n  // 新增：根據模板類型獲取明細項目描述\n  getDetailItemsDescription(count) {\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return `包含 ${count} 個空間配置項目：`;\n    } else {\n      return `包含 ${count} 個工程明細項目：`;\n    }\n  }\n  // 新增：根據模板類型獲取無模板提示文案\n  getNoTemplatesMessage() {\n    const templateTypeName = this.getCurrentTemplateTypeName();\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return `暫無可用的${templateTypeName}，請稍後再試或聯繫管理員`;\n    } else {\n      return `暫無可用的${templateTypeName}，請稍後再試或聯繫管理員`;\n    }\n  }\n  // 新增：根據模板類型獲取無明細提示文案\n  getNoDetailsMessage() {\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return '此空間模板暫無配置項目';\n    } else {\n      return '此項目模板暫無工程明細';\n    }\n  }\n  // 新增：根據模板類型獲取衝突警告文案\n  getConflictWarningText() {\n    const conflictCount = this.getConflictCount();\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return `檢測到 ${conflictCount} 個空間配置可能與現有設定重複，系統將自動處理衝突項目。`;\n    } else {\n      return `檢測到 ${conflictCount} 個工程項目可能與現有需求重複，系統將自動處理衝突項目。`;\n    }\n  }\n  // 新增：根據模板類型獲取確認套用按鈕文案\n  getApplyButtonText() {\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return '確認套用空間配置';\n    } else {\n      return '確認套用工程項目';\n    }\n  }\n  // 新增：根據模板類型獲取步驟1標題\n  getStep1Title() {\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return '1. 選擇空間模板';\n    } else {\n      return '1. 選擇項目模板';\n    }\n  }\n  // 新增：根據模板類型獲取步驟2標題\n  getStep2Title() {\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return '2. 確認空間配置';\n    } else {\n      return '2. 確認工程項目';\n    }\n  }\n  // 新增：根據模板類型獲取位置標籤文案\n  getLocationLabel() {\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\n      return '空間位置';\n    } else {\n      return '施工位置';\n    }\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const progressTexts = {\n      1: this.selectedTemplateType === EnumTemplateType.SpaceTemplate ? `請選擇要套用的空間配置項目` : `請選擇要套用的工程項目`,\n      2: this.selectedTemplateType === EnumTemplateType.SpaceTemplate ? '確認空間配置套用詳情' : '確認工程項目套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: templateTypeName,\n      selectedItems: this.getSelectedItems(),\n      templateDetails: new Map(this.selectedTemplateDetails),\n      // 傳遞已載入的模板明細\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n};\n__decorate([Input()], TemplateSelectorComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], TemplateSelectorComponent.prototype, \"CTemplateType\", void 0);\n__decorate([Output()], TemplateSelectorComponent.prototype, \"templateApplied\", void 0);\nTemplateSelectorComponent = __decorate([Component({\n  selector: 'app-template-selector',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule],\n  templateUrl: './template-selector.component.html',\n  styleUrls: ['./template-selector.component.scss']\n})], TemplateSelectorComponent);\nexport { TemplateSelectorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "EnumTemplateType", "EnumTemplateTypeHelper", "TemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "CTemplateType", "SpaceTemplate", "templateApplied", "currentStep", "templates", "selectedTemplateDetails", "Map", "selectedTemplateType", "templateTypeOptions", "getTemplateTypeList", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "selected", "error", "onTemplateItemChange", "onTemplateTypeChange", "resetSelections", "getCurrentTemplateTypeName", "getDisplayName", "getDialogTitle", "templateTypeName", "getSelectionTitle", "getConfirmationTitle", "getApplySummaryText", "count", "getSelectedItems", "length", "getDetailItemsDescription", "getNoTemplatesMessage", "getNoDetailsMessage", "getConflictWarningText", "conflictCount", "getConflictCount", "getApplyButtonText", "getStep1Title", "getStep2Title", "getLocationLabel", "filter", "getSelectedTotalPrice", "canProceed", "nextStep", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "CTemplateId", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "getTemplateDetails", "get", "previousStep", "getProgressText", "progressTexts", "hasConflicts", "applyTemplate", "config", "spaceId", "spaceName", "templateDetails", "totalPrice", "emit", "close", "reset", "template", "clear", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef,\r\n  NbSelectModule\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule\r\n  ],\r\n  templateUrl: './template-selector.component.html',\r\n  styleUrls: ['./template-selector.component.scss']\r\n})\r\nexport class TemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  // 新增：模板類型選擇相關屬性\r\n  selectedTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<TemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 初始化選擇的模板類型\r\n    this.selectedTemplateType = this.CTemplateType;\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數，使用當前選擇的模板類型\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.selectedTemplateType,\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  // 新增：當模板類型選擇變更時的處理\r\n  onTemplateTypeChange() {\r\n    // 清空當前選擇\r\n    this.resetSelections();\r\n    // 重新載入對應類型的模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  // 新增：獲取當前選擇的模板類型名稱\r\n  getCurrentTemplateTypeName(): string {\r\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n  }\r\n\r\n  // 新增：根據模板類型獲取對話框標題\r\n  getDialogTitle(): string {\r\n    const templateTypeName = this.getCurrentTemplateTypeName();\r\n    return `${templateTypeName}選擇器`;\r\n  }\r\n\r\n  // 新增：根據模板類型獲取選擇區域標題\r\n  getSelectionTitle(): string {\r\n    const templateTypeName = this.getCurrentTemplateTypeName();\r\n    return `選擇${templateTypeName}項目`;\r\n  }\r\n\r\n  // 新增：根據模板類型獲取確認套用標題\r\n  getConfirmationTitle(): string {\r\n    const templateTypeName = this.getCurrentTemplateTypeName();\r\n    return `確認套用${templateTypeName}`;\r\n  }\r\n\r\n  // 新增：根據模板類型獲取套用摘要文案\r\n  getApplySummaryText(): string {\r\n    const templateTypeName = this.getCurrentTemplateTypeName();\r\n    const count = this.getSelectedItems().length;\r\n\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return `將套用 <strong>${templateTypeName}</strong>：${count}個空間配置項目`;\r\n    } else {\r\n      return `將套用 <strong>${templateTypeName}</strong>：${count}個工程項目`;\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取明細項目描述\r\n  getDetailItemsDescription(count: number): string {\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return `包含 ${count} 個空間配置項目：`;\r\n    } else {\r\n      return `包含 ${count} 個工程明細項目：`;\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取無模板提示文案\r\n  getNoTemplatesMessage(): string {\r\n    const templateTypeName = this.getCurrentTemplateTypeName();\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return `暫無可用的${templateTypeName}，請稍後再試或聯繫管理員`;\r\n    } else {\r\n      return `暫無可用的${templateTypeName}，請稍後再試或聯繫管理員`;\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取無明細提示文案\r\n  getNoDetailsMessage(): string {\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return '此空間模板暫無配置項目';\r\n    } else {\r\n      return '此項目模板暫無工程明細';\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取衝突警告文案\r\n  getConflictWarningText(): string {\r\n    const conflictCount = this.getConflictCount();\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return `檢測到 ${conflictCount} 個空間配置可能與現有設定重複，系統將自動處理衝突項目。`;\r\n    } else {\r\n      return `檢測到 ${conflictCount} 個工程項目可能與現有需求重複，系統將自動處理衝突項目。`;\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取確認套用按鈕文案\r\n  getApplyButtonText(): string {\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return '確認套用空間配置';\r\n    } else {\r\n      return '確認套用工程項目';\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取步驟1標題\r\n  getStep1Title(): string {\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return '1. 選擇空間模板';\r\n    } else {\r\n      return '1. 選擇項目模板';\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取步驟2標題\r\n  getStep2Title(): string {\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return '2. 確認空間配置';\r\n    } else {\r\n      return '2. 確認工程項目';\r\n    }\r\n  }\r\n\r\n  // 新增：根據模板類型獲取位置標籤文案\r\n  getLocationLabel(): string {\r\n    if (this.selectedTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      return '空間位置';\r\n    } else {\r\n      return '施工位置';\r\n    }\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const progressTexts = {\r\n      1: this.selectedTemplateType === EnumTemplateType.SpaceTemplate\r\n        ? `請選擇要套用的空間配置項目`\r\n        : `請選擇要套用的工程項目`,\r\n      2: this.selectedTemplateType === EnumTemplateType.SpaceTemplate\r\n        ? '確認空間配置套用詳情'\r\n        : '確認工程項目套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: templateTypeName,\r\n      selectedItems: this.getSelectedItems(),\r\n      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,cAAc,QACT,gBAAgB;AAGvB,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;AA8BxF,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAapCC,YACUC,eAAgC,EAChCC,SAAiD;IADjD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAdV,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAWP,gBAAgB,CAACQ,aAAa;IACrD,KAAAC,eAAe,GAAG,IAAInB,YAAY,EAAuB;IAEnE,KAAAoB,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;IAExE;IACA,KAAAC,oBAAoB,GAAWd,gBAAgB,CAACQ,aAAa;IAC7D,KAAAO,mBAAmB,GAAGd,sBAAsB,CAACe,mBAAmB,EAAE;EAK9D;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACP,aAAa;IAC9C;IACA,IAAI,CAACW,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CZ,aAAa,EAAE,IAAI,CAACO,oBAAoB;MACxCM,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAClB,eAAe,CAACmB,4CAA4C,CAAC;MAChEC,IAAI,EAAEL;KACP,CAAC,CAACM,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAAClB,SAAS,GAAGgB,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACrB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACtB,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAuB,oBAAoBA,CAAA;IAClB;EAAA;EAGF;EACAC,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACC,eAAe,EAAE;IACtB;IACA,IAAI,CAAClB,oBAAoB,EAAE;EAC7B;EAEA;EACAmB,0BAA0BA,CAAA;IACxB,OAAOpC,sBAAsB,CAACqC,cAAc,CAAC,IAAI,CAACxB,oBAAoB,CAAC;EACzE;EAEA;EACAyB,cAAcA,CAAA;IACZ,MAAMC,gBAAgB,GAAG,IAAI,CAACH,0BAA0B,EAAE;IAC1D,OAAO,GAAGG,gBAAgB,KAAK;EACjC;EAEA;EACAC,iBAAiBA,CAAA;IACf,MAAMD,gBAAgB,GAAG,IAAI,CAACH,0BAA0B,EAAE;IAC1D,OAAO,KAAKG,gBAAgB,IAAI;EAClC;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAMF,gBAAgB,GAAG,IAAI,CAACH,0BAA0B,EAAE;IAC1D,OAAO,OAAOG,gBAAgB,EAAE;EAClC;EAEA;EACAG,mBAAmBA,CAAA;IACjB,MAAMH,gBAAgB,GAAG,IAAI,CAACH,0BAA0B,EAAE;IAC1D,MAAMO,KAAK,GAAG,IAAI,CAACC,gBAAgB,EAAE,CAACC,MAAM;IAE5C,IAAI,IAAI,CAAChC,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,eAAegC,gBAAgB,aAAaI,KAAK,SAAS;IACnE,CAAC,MAAM;MACL,OAAO,eAAeJ,gBAAgB,aAAaI,KAAK,OAAO;IACjE;EACF;EAEA;EACAG,yBAAyBA,CAACH,KAAa;IACrC,IAAI,IAAI,CAAC9B,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,MAAMoC,KAAK,WAAW;IAC/B,CAAC,MAAM;MACL,OAAO,MAAMA,KAAK,WAAW;IAC/B;EACF;EAEA;EACAI,qBAAqBA,CAAA;IACnB,MAAMR,gBAAgB,GAAG,IAAI,CAACH,0BAA0B,EAAE;IAC1D,IAAI,IAAI,CAACvB,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,QAAQgC,gBAAgB,cAAc;IAC/C,CAAC,MAAM;MACL,OAAO,QAAQA,gBAAgB,cAAc;IAC/C;EACF;EAEA;EACAS,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACnC,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,aAAa;IACtB,CAAC,MAAM;MACL,OAAO,aAAa;IACtB;EACF;EAEA;EACA0C,sBAAsBA,CAAA;IACpB,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC7C,IAAI,IAAI,CAACtC,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,OAAO2C,aAAa,8BAA8B;IAC3D,CAAC,MAAM;MACL,OAAO,OAAOA,aAAa,8BAA8B;IAC3D;EACF;EAEA;EACAE,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACvC,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,UAAU;IACnB,CAAC,MAAM;MACL,OAAO,UAAU;IACnB;EACF;EAEA;EACA8C,aAAaA,CAAA;IACX,IAAI,IAAI,CAACxC,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,WAAW;IACpB,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF;EAEA;EACA+C,aAAaA,CAAA;IACX,IAAI,IAAI,CAACzC,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,WAAW;IACpB,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF;EAEA;EACAgD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC1C,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,EAAE;MAChE,OAAO,MAAM;IACf,CAAC,MAAM;MACL,OAAO,MAAM;IACf;EACF;EAEAqC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAClC,SAAS,CAAC8C,MAAM,CAAC1B,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;EACrD;EAEA0B,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACjD,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACmC,gBAAgB,EAAE,CAACC,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAc,QAAQA,CAAA;IACN,IAAI,IAAI,CAACD,UAAU,EAAE,IAAI,IAAI,CAACjD,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACmD,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACnD,WAAW,EAAE;IACpB;EACF;EAEAmD,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAACjB,gBAAgB,EAAE;IAE7CiB,aAAa,CAACC,OAAO,CAAChC,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAACiC,WAAW,IAAI,CAAC,IAAI,CAACpD,uBAAuB,CAACqD,GAAG,CAAClC,IAAI,CAACiC,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACE,sBAAsB,CAACnC,IAAI,CAACiC,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAC/D,eAAe,CAACiE,yCAAyC,CAAC;MAC7D7C,IAAI,EAAE4C;KACP,CAAC,CAAC3C,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACjB,uBAAuB,CAAC0D,GAAG,CAACH,UAAU,EAAExC,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDI,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACrB,uBAAuB,CAAC0D,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAI,kBAAkBA,CAACJ,UAAkB;IACnC,OAAO,IAAI,CAACvD,uBAAuB,CAAC4D,GAAG,CAACL,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEAM,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/D,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAgE,eAAeA,CAAA;IACb,MAAMlC,gBAAgB,GAAGvC,sBAAsB,CAACqC,cAAc,CAAC,IAAI,CAACxB,oBAAoB,CAAC;IACzF,MAAM6D,aAAa,GAAG;MACpB,CAAC,EAAE,IAAI,CAAC7D,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,GAC3D,eAAe,GACf,aAAa;MACjB,CAAC,EAAE,IAAI,CAACM,oBAAoB,KAAKd,gBAAgB,CAACQ,aAAa,GAC3D,YAAY,GACZ;KACL;IACD,OAAOmE,aAAa,CAAC,IAAI,CAACjE,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAkE,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAAC/B,gBAAgB,EAAE,CAACC,MAAM,GAAG,CAAC;EAC3C;EAEAM,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACwB,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAC,aAAaA,CAAA;IACX,MAAMrC,gBAAgB,GAAGvC,sBAAsB,CAACqC,cAAc,CAAC,IAAI,CAACxB,oBAAoB,CAAC;IACzF,MAAMgE,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAExC,gBAAgB;MAC3BsB,aAAa,EAAE,IAAI,CAACjB,gBAAgB,EAAE;MACtCoC,eAAe,EAAE,IAAIpE,GAAG,CAAC,IAAI,CAACD,uBAAuB,CAAC;MAAE;MACxDsE,UAAU,EAAE,IAAI,CAACxB,qBAAqB;KACvC;IAED,IAAI,CAACjD,eAAe,CAAC0E,IAAI,CAACL,MAAM,CAAC;IACjC,IAAI,CAACM,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAChD,eAAe,EAAE;IACtB,IAAI,CAAC/B,SAAS,CAAC+E,KAAK,EAAE;EACxB;EAEA;EACA;EAEQC,KAAKA,CAAA;IACX,IAAI,CAAC3E,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;EAEQyB,eAAeA,CAAA;IACrB,IAAI,CAAC1B,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,SAAS,CAACoD,OAAO,CAACuB,QAAQ,IAAG;MAChCA,QAAQ,CAACtD,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAACpB,uBAAuB,CAAC2E,KAAK,EAAE;EACtC;CACD;AAnTUC,UAAA,EAARjG,KAAK,EAAE,C,6DAA0B;AACzBiG,UAAA,EAARjG,KAAK,EAAE,C,+DAAwD;AACtDiG,UAAA,EAAThG,MAAM,EAAE,C,iEAA2D;AAHzDU,yBAAyB,GAAAsF,UAAA,EAfrCnG,SAAS,CAAC;EACToG,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlG,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,CACf;EACD6F,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACW3F,yBAAyB,CAoTrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}