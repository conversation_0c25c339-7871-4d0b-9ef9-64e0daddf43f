<!-- 空間選擇器共用組件 -->
<div class="space-picker-container">
  <!-- 搜尋區域 -->
  <div class="search-section">
    <div class="search-form p-3 bg-light border rounded">
      <div class="row g-2 align-items-end">
        <div class="col-lg-4 col-md-5">
          <label class="form-label mb-1 text-muted small">項目名稱</label>
          <nb-form-field>
            <input type="text" nbInput size="small" placeholder="輸入項目名稱" [(ngModel)]="searchKeyword"
              (keyup.enter)="onSearch()" [disabled]="isLoading" />
            <nb-icon *ngIf="searchKeyword" nbSuffix icon="close-outline" class="cursor-pointer text-muted"
              (click)="searchKeyword = ''; onSearch()" title="清除"></nb-icon>
          </nb-form-field>
        </div>
        <div class="col-lg-4 col-md-5">
          <label class="form-label mb-1 text-muted small">所屬區域</label>
          <nb-form-field>
            <input type="text" nbInput size="small" placeholder="輸入所屬區域" [(ngModel)]="searchLocation"
              (keyup.enter)="onSearch()" [disabled]="isLoading" />
            <nb-icon *ngIf="searchLocation" nbSuffix icon="close-outline" class="cursor-pointer text-muted"
              (click)="searchLocation = ''; onSearch()" title="清除"></nb-icon>
          </nb-form-field>
        </div>
        <div class="col-lg-4 col-md-2">
          <div class="search-actions d-flex gap-1">
            <button nbButton ghost size="small" (click)="onReset()" [disabled]="isLoading" title="重置">
              <nb-icon icon="refresh-outline"></nb-icon>
            </button>
            <button nbButton status="primary" size="small" (click)="onSearch()" [disabled]="isLoading" title="搜尋">
              <nb-icon *ngIf="!isLoading" icon="search-outline"></nb-icon>
              <nb-icon *ngIf="isLoading" icon="loader-outline" class="spinning"></nb-icon>
              <span class="d-none d-lg-inline ms-1">{{ isLoading ? '搜尋...' : '搜尋' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 空間列表區域 -->
  <div class="space-list-section border rounded p-3" style="background-color: #f8f9fa;">
    <!-- 列表標題和統計 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="d-flex align-items-center" *ngIf="multiple">
        <input type="checkbox" id="selectAllSpaces" [checked]="allSelected" (change)="toggleAllSpaces()" class="me-2">
        <label for="selectAllSpaces" class="mb-0 font-weight-bold">全選當頁空間</label>
      </div>
      <div *ngIf="!multiple" class="font-weight-bold">
        選擇空間
      </div>
      <small class="text-muted">
        共 {{ totalRecords }} 筆，第 {{ pageIndex }} / {{ totalPages }} 頁
      </small>
    </div>

    <!-- 空間項目網格 -->
    <div class="space-grid">
      <div class="space-item" *ngFor="let space of availableSpaces" [class.selected]="space.selected"
        (click)="toggleSpaceSelection(space)">
        <div class="space-card">
          <div class="space-name">{{ space.CPart }}</div>
          <div class="space-location">{{ space.CLocation || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 空間列表為空時的提示 -->
    <div *ngIf="availableSpaces.length === 0" class="text-center text-muted py-4">
      <nb-icon icon="info-outline" class="me-2"></nb-icon>
      沒有符合條件的空間
    </div>

    <!-- 分頁控制 -->
    <div class="d-flex justify-content-center mt-3" *ngIf="totalRecords > pageSize">
      <ngx-pagination [(Page)]="pageIndex" [PageSize]="pageSize" [CollectionSize]="totalRecords"
        (PageChange)="onPageChange($event)">
      </ngx-pagination>
    </div>
  </div>

  <!-- 已選空間摘要 -->
  <div *ngIf="selectedItems.length > 0" class="selected-summary mt-3 p-3 bg-light border rounded">
    <h6 class="mb-2">
      <nb-icon icon="checkmark-circle-outline" class="text-success me-2"></nb-icon>
      已選空間 ({{ selectedItems.length }} 項)
    </h6>
    <div class="selected-spaces-list">
      <span *ngFor="let space of selectedItems" class="badge badge-primary me-1 mb-1">
        {{ space.CPart }}
        <button type="button" class="btn-close ms-1" (click)="removeSelectedSpace(space)" style="font-size: 0.7rem;"
          aria-label="移除">
        </button>
      </span>
    </div>
  </div>
</div>
