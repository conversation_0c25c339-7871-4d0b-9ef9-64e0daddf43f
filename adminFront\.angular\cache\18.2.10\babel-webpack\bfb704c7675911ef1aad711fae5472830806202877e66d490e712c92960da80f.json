{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nlet TemplateComponent = class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.searchTemplateType = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\n    this.availableItemsForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.itemPageSize = 10;\n    this.itemTotalRecords = 0;\n    this.allItemsSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n    // 模態框模式控制\n    this.isEditMode = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      CTemplateType: this.searchTemplateType,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 載入項目模板可用項目（使用空間列表作為基礎）\n  loadAvailableItemsForTemplate() {\n    const request = {\n      CPart: this.itemSearchKeyword || null,\n      CLocation: this.itemSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.itemPageIndex,\n      PageSize: this.itemPageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableItemsForTemplate = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\n          CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\n          CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\n        })) || [];\n        this.itemTotalRecords = response.TotalItems || 0;\n        this.updateAllItemsSelectedState();\n      }\n    })).subscribe();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 項目搜尋功能\n  onItemSearch() {\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  onItemReset() {\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  itemPageChanged(page) {\n    this.itemPageIndex = page;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.isEditMode = false;\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.loadAvailableSpaces();\n    this.loadAvailableItemsForTemplate();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.isEditMode = true;\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    // 編輯模式下不需要載入選擇器數據\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  // 計算模態框寬度\n  get modalWidth() {\n    return this.isEditMode ? '550px' : '800px';\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\n      return false;\n    }\n    // 驗證項目模板的單價和單位\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      for (const item of this.selectedItemsForTemplate) {\n        // 檢核單價\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\n          return false;\n        }\n        // 檢核單位\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\n          return false;\n        }\n        // 檢核單價是否為有效數字\n        if (isNaN(item.CUnitPrice)) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\n          return false;\n        }\n        // 檢核單位長度\n        if (item.CUnit.trim().length > 10) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間或項目）\n  saveTemplateDetails(templateId, ref) {\n    let details = [];\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n      // 空間模板的詳細資料\n      details = this.selectedSpacesForTemplate.map(space => ({\n        CTemplateDetailId: null,\n        CReleateId: space.CSpaceID,\n        CPart: space.CPart,\n        CLocation: space.CLocation\n      }));\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      // 項目模板的詳細資料，包含單價和單位\n      details = this.selectedItemsForTemplate.map(item => ({\n        CTemplateDetailId: null,\n        CReleateId: item.CSpaceID,\n        CPart: item.CPart,\n        CLocation: item.CLocation,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit\n      }));\n    }\n    if (details.length > 0) {\n      const templateData = {\n        CTemplateId: templateId,\n        CTemplateName: this.templateDetail.CTemplateName,\n        CTemplateType: this.templateDetail.CTemplateType,\n        CStatus: this.templateDetail.CStatus,\n        Details: details\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('建立模板成功');\n          ref.close();\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '保存模板詳細資料失敗');\n        }\n      })).subscribe();\n    } else {\n      this.message.showSucessMSG('建立模板成功');\n      ref.close();\n      this.loadTemplateList();\n    }\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 項目選擇相關方法\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    if (item.selected) {\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n        const newItem = {\n          ...item,\n          CUnitPrice: 0,\n          // 設為0，強制用戶輸入\n          CUnit: '' // 設為空，強制用戶輸入\n        };\n        this.selectedItemsForTemplate.push(newItem);\n      }\n    } else {\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    }\n    this.updateAllItemsSelectedState();\n  }\n  toggleAllItems() {\n    this.allItemsSelected = !this.allItemsSelected;\n    this.availableItemsForTemplate.forEach(item => {\n      item.selected = this.allItemsSelected;\n      if (this.allItemsSelected) {\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n          const newItem = {\n            ...item,\n            CUnitPrice: 0,\n            // 設為0，強制用戶輸入\n            CUnit: '' // 設為空，強制用戶輸入\n          };\n          this.selectedItemsForTemplate.push(newItem);\n        }\n      } else {\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n      }\n    });\n  }\n  removeSelectedItem(item) {\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.selected = false;\n    }\n    this.updateAllItemsSelectedState();\n  }\n  updateAllItemsSelectedState() {\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 && this.availableItemsForTemplate.every(item => item.selected);\n  }\n  // 更新選中項目的單價和單位\n  updateItemPrice(item, price) {\n    // 確保價格為有效數字且大於0\n    if (isNaN(price) || price < 0) {\n      price = 0;\n    }\n    item.CUnitPrice = price;\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnitPrice = price;\n    }\n  }\n  updateItemUnit(item, unit) {\n    // 清理單位字串\n    unit = unit ? unit.trim() : '';\n    item.CUnit = unit;\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnit = unit;\n    }\n  }\n  // 檢查項目是否有效（用於UI顯示）\n  isItemValid(item) {\n    return !!(item.CUnitPrice && item.CUnitPrice > 0 && item.CUnit && item.CUnit.trim() !== '' && item.CUnit.trim() !== '式');\n  }\n  // 獲取已完成設定的項目數量\n  getValidItemsCount() {\n    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\n  }\n};\n__decorate([ViewChild('createModal', {\n  static: false\n})], TemplateComponent.prototype, \"createModal\", void 0);\n__decorate([ViewChild('editModal', {\n  static: false\n})], TemplateComponent.prototype, \"editModal\", void 0);\n__decorate([ViewChild('templateDetailModal', {\n  static: false\n})], TemplateComponent.prototype, \"templateDetailModal\", void 0);\nTemplateComponent = __decorate([Component({\n  selector: 'ngx-template',\n  templateUrl: './template.component.html',\n  styleUrls: ['./template.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BreadcrumbComponent]\n})], TemplateComponent);\nexport { TemplateComponent };", "map": {"version": 3, "names": ["BaseComponent", "Component", "ViewChild", "CommonModule", "SharedModule", "BreadcrumbComponent", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "Math", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "templateDetail", "searchKeyword", "searchStatus", "searchTemplateType", "availableSpaces", "selectedSpacesForTemplate", "spaceSearchKeyword", "spaceSearchLocation", "spacePageIndex", "spacePageSize", "spaceTotalRecords", "allSpacesSelected", "availableItemsForTemplate", "selectedItemsForTemplate", "itemSearchKeyword", "itemSearchLocation", "itemPageIndex", "itemPageSize", "itemTotalRecords", "allItemsSelected", "selectedTemplateDetail", "templateDetailSpaces", "isLoadingTemplateDetail", "isEditMode", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "CTemplateName", "CStatus", "CTemplateType", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "CCreateDt", "CUpdateDt", "CCreator", "CUpdator", "TotalItems", "showErrorMSG", "Message", "subscribe", "<PERSON>art", "CLocation", "apiSpaceGetSpaceListPost$Json", "CSpaceID", "selected", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "loadAvailableItemsForTemplate", "CUnitPrice", "find", "CUnit", "updateAllItemsSelectedState", "onSpaceSearch", "onSpaceReset", "onItemSearch", "onItemReset", "pageChanged", "page", "spacePageChanged", "itemPageChanged", "openCreateModal", "modal", "SpaceTemplate", "open", "context", "autoFocus", "openEditModal", "template", "onClose", "ref", "close", "modalWidth", "onSubmit", "validateTemplateForm", "updateTemplate", "createTemplate", "trim", "undefined", "length", "ItemTemplate", "isNaN", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "details", "space", "CTemplateDetailId", "CReleateId", "Details", "deleteTemplate", "confirm", "apiTemplateDeleteTemplatePost$Json", "viewTemplateDetail", "apiTemplateGetTemplateDetailByIdPost$Json", "toggleSpaceSelection", "push", "filter", "toggleAllSpaces", "for<PERSON>ach", "removeSelectedSpace", "availableSpace", "every", "toggleItemSelection", "newItem", "toggleAllItems", "removeSelectedItem", "availableItem", "updateItemPrice", "price", "updateItemUnit", "unit", "isItemValid", "getValidItemsCount", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { SpacePickerComponent, SpacePickerItem } from 'src/app/shared/components/space-picker/space-picker.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n// 項目模板選擇項目介面（擴展空間選擇項目，添加單價和單位）\r\nexport interface ItemPickListItem extends SpacePickListItem {\r\n  CUnitPrice?: number;\r\n  CUnit?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n  searchTemplateType: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\r\n  availableItemsForTemplate: ItemPickListItem[] = [];\r\n  selectedItemsForTemplate: ItemPickListItem[] = [];\r\n  itemSearchKeyword: string = '';\r\n  itemSearchLocation: string = '';\r\n  itemPageIndex = 1;\r\n  itemPageSize = 10;\r\n  itemTotalRecords = 0;\r\n  allItemsSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  // 模態框模式控制\r\n  isEditMode = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      CTemplateType: this.searchTemplateType,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 載入項目模板可用項目（使用空間列表作為基礎）\r\n  loadAvailableItemsForTemplate(): void {\r\n    const request = {\r\n      CPart: this.itemSearchKeyword || null,\r\n      CLocation: this.itemSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.itemPageIndex,\r\n      PageSize: this.itemPageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableItemsForTemplate = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\r\n            CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\r\n            CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\r\n          })) || [];\r\n          this.itemTotalRecords = response.TotalItems || 0;\r\n          this.updateAllItemsSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 項目搜尋功能\r\n  onItemSearch(): void {\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  onItemReset(): void {\r\n    this.itemSearchKeyword = '';\r\n    this.itemSearchLocation = '';\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  itemPageChanged(page: number): void {\r\n    this.itemPageIndex = page;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.isEditMode = false;\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.selectedItemsForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.loadAvailableItemsForTemplate();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.isEditMode = true;\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    // 編輯模式下不需要載入選擇器數據\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  // 計算模態框寬度\r\n  get modalWidth(): string {\r\n    return this.isEditMode ? '550px' : '800px';\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\r\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\r\n      return false;\r\n    }\r\n\r\n    // 驗證項目模板的單價和單位\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      for (const item of this.selectedItemsForTemplate) {\r\n        // 檢核單價\r\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位\r\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單價是否為有效數字\r\n        if (isNaN(item.CUnitPrice)) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位長度\r\n        if (item.CUnit.trim().length > 10) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間或項目）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    let details: any[] = [];\r\n\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      // 空間模板的詳細資料\r\n      details = this.selectedSpacesForTemplate.map(space => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: space.CSpaceID,\r\n        CPart: space.CPart,\r\n        CLocation: space.CLocation\r\n      }));\r\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      // 項目模板的詳細資料，包含單價和單位\r\n      details = this.selectedItemsForTemplate.map(item => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: item.CSpaceID,\r\n        CPart: item.CPart,\r\n        CLocation: item.CLocation,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit\r\n      }));\r\n    }\r\n\r\n    if (details.length > 0) {\r\n      const templateData: SaveTemplateArgs = {\r\n        CTemplateId: templateId,\r\n        CTemplateName: this.templateDetail.CTemplateName,\r\n        CTemplateType: this.templateDetail.CTemplateType,\r\n        CStatus: this.templateDetail.CStatus,\r\n        Details: details\r\n      };\r\n\r\n      this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('建立模板成功');\r\n            ref.close();\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '保存模板詳細資料失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    } else {\r\n      this.message.showSucessMSG('建立模板成功');\r\n      ref.close();\r\n      this.loadTemplateList();\r\n    }\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  // 項目選擇相關方法\r\n  toggleItemSelection(item: ItemPickListItem): void {\r\n    item.selected = !item.selected;\r\n\r\n    if (item.selected) {\r\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\r\n        const newItem = {\r\n          ...item,\r\n          CUnitPrice: 0, // 設為0，強制用戶輸入\r\n          CUnit: '' // 設為空，強制用戶輸入\r\n        };\r\n        this.selectedItemsForTemplate.push(newItem);\r\n      }\r\n    } else {\r\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  toggleAllItems(): void {\r\n    this.allItemsSelected = !this.allItemsSelected;\r\n\r\n    this.availableItemsForTemplate.forEach(item => {\r\n      item.selected = this.allItemsSelected;\r\n      if (this.allItemsSelected) {\r\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\r\n          const newItem = {\r\n            ...item,\r\n            CUnitPrice: 0, // 設為0，強制用戶輸入\r\n            CUnit: '' // 設為空，強制用戶輸入\r\n          };\r\n          this.selectedItemsForTemplate.push(newItem);\r\n        }\r\n      } else {\r\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedItem(item: ItemPickListItem): void {\r\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.selected = false;\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  updateAllItemsSelectedState(): void {\r\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 &&\r\n      this.availableItemsForTemplate.every(item => item.selected);\r\n  }\r\n\r\n  // 更新選中項目的單價和單位\r\n  updateItemPrice(item: ItemPickListItem, price: number): void {\r\n    // 確保價格為有效數字且大於0\r\n    if (isNaN(price) || price < 0) {\r\n      price = 0;\r\n    }\r\n\r\n    item.CUnitPrice = price;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnitPrice = price;\r\n    }\r\n  }\r\n\r\n  updateItemUnit(item: ItemPickListItem, unit: string): void {\r\n    // 清理單位字串\r\n    unit = unit ? unit.trim() : '';\r\n\r\n    item.CUnit = unit;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnit = unit;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否有效（用於UI顯示）\r\n  isItemValid(item: ItemPickListItem): boolean {\r\n    return !!(item.CUnitPrice && item.CUnitPrice > 0 &&\r\n      item.CUnit && item.CUnit.trim() !== '' && item.CUnit.trim() !== '式');\r\n  }\r\n\r\n  // 獲取已完成設定的項目數量\r\n  getValidItemsCount(): number {\r\n    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,qCAAqC;AACnE,SAASC,SAAS,EAAUC,SAAS,QAAqB,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAG7D,SAASC,mBAAmB,QAAQ,kDAAkD;AAKtF,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;AA6CxF,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAkB,SAAQT,aAAa;EASlDU,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAV,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAAU,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAC,cAAc,GAAqB,EAAE;IACrC,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,kBAAkB,GAAkB,IAAI;IAExC;IACA,KAAAC,eAAe,GAAwB,EAAE;IACzC,KAAAC,yBAAyB,GAAwB,EAAE;IACnD,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAC,yBAAyB,GAAuB,EAAE;IAClD,KAAAC,wBAAwB,GAAuB,EAAE;IACjD,KAAAC,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,oBAAoB,GAA8B,EAAE;IACpD,KAAAC,uBAAuB,GAAG,KAAK;IAE/B;IACA,KAAAC,UAAU,GAAG,KAAK;EAxClB;EA0CSC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACdC,aAAa,EAAE,IAAI,CAAC3B,aAAa,IAAI,IAAI;MACzC4B,OAAO,EAAE,IAAI,CAAC3B,YAAY;MAC1B4B,aAAa,EAAE,IAAI,CAAC3B,kBAAkB;MACtC4B,SAAS,EAAE,IAAI,CAAClC,SAAS;MACzBmC,QAAQ,EAAE,IAAI,CAACpC;KAChB;IAED,IAAI,CAACN,gBAAgB,CAAC2C,mCAAmC,CAAC;MAAEC,IAAI,EAAEP;IAAO,CAAE,CAAC,CAACQ,IAAI,CAC/EpD,GAAG,CAACqD,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACtC,YAAY,GAAGqC,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9Bb,aAAa,EAAEY,IAAI,CAACZ,aAAc;UAClCE,aAAa,EAAEU,IAAI,CAACV,aAAa;UAAE;UACnCY,SAAS,EAAEF,IAAI,CAACE,SAAU;UAC1BC,SAAS,EAAEH,IAAI,CAACG,SAAU;UAC1BC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;UACvBC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;UACvBhB,OAAO,EAAEW,IAAI,CAACX;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC/B,YAAY,GAAGsC,QAAQ,CAACU,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAACX,QAAQ,CAACY,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAvB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACduB,KAAK,EAAE,IAAI,CAAC5C,kBAAkB,IAAI,IAAI;MACtC6C,SAAS,EAAE,IAAI,CAAC5C,mBAAmB,IAAI,IAAI;MAC3CsB,OAAO,EAAE,CAAC;MAAE;MACZE,SAAS,EAAE,IAAI,CAACvB,cAAc;MAC9BwB,QAAQ,EAAE,IAAI,CAACvB;KAChB;IAED,IAAI,CAAClB,aAAa,CAAC6D,6BAA6B,CAAC;MAAElB,IAAI,EAAEP;IAAO,CAAE,CAAC,CAACQ,IAAI,CACtEpD,GAAG,CAACqD,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACjC,eAAe,GAAGgC,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDa,QAAQ,EAAEb,IAAI,CAACa,QAAS;UACxBH,KAAK,EAAEV,IAAI,CAACU,KAAM;UAClBC,SAAS,EAAEX,IAAI,CAACW,SAAS;UACzBG,QAAQ,EAAE,IAAI,CAACjD,yBAAyB,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC3C,iBAAiB,GAAG0B,QAAQ,CAACU,UAAU,IAAI,CAAC;QACjD,IAAI,CAACW,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EAEA;EACAS,QAAQA,CAAA;IACN,IAAI,CAAC7D,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC4B,gBAAgB,EAAE;EACzB;EAEAkC,OAAOA,CAAA;IACL,IAAI,CAAC1D,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC4B,gBAAgB,EAAE;EACzB;EAEA;EACAmC,6BAA6BA,CAAA;IAC3B,MAAMjC,OAAO,GAAG;MACduB,KAAK,EAAE,IAAI,CAACpC,iBAAiB,IAAI,IAAI;MACrCqC,SAAS,EAAE,IAAI,CAACpC,kBAAkB,IAAI,IAAI;MAC1Cc,OAAO,EAAE,CAAC;MAAE;MACZE,SAAS,EAAE,IAAI,CAACf,aAAa;MAC7BgB,QAAQ,EAAE,IAAI,CAACf;KAChB;IAED,IAAI,CAAC1B,aAAa,CAAC6D,6BAA6B,CAAC;MAAElB,IAAI,EAAEP;IAAO,CAAE,CAAC,CAACQ,IAAI,CACtEpD,GAAG,CAACqD,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACzB,yBAAyB,GAAGwB,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UAC9Da,QAAQ,EAAEb,IAAI,CAACa,QAAS;UACxBH,KAAK,EAAEV,IAAI,CAACU,KAAM;UAClBC,SAAS,EAAEX,IAAI,CAACW,SAAS;UACzBG,QAAQ,EAAE,IAAI,CAACzC,wBAAwB,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;UAC/EQ,UAAU,EAAE,IAAI,CAAChD,wBAAwB,CAACiD,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC,EAAEQ,UAAU,IAAI,CAAC;UAClGE,KAAK,EAAE,IAAI,CAAClD,wBAAwB,CAACiD,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC,EAAEU,KAAK,IAAI;SACxF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC7C,gBAAgB,GAAGkB,QAAQ,CAACU,UAAU,IAAI,CAAC;QAChD,IAAI,CAACkB,2BAA2B,EAAE;MACpC;IACF,CAAC,CAAC,CACH,CAACf,SAAS,EAAE;EACf;EAEA;EACAgB,aAAaA,CAAA;IACX,IAAI,CAACzD,cAAc,GAAG,CAAC;IACvB,IAAI,CAACkB,mBAAmB,EAAE;EAC5B;EAEAwC,YAAYA,CAAA;IACV,IAAI,CAAC5D,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACkB,mBAAmB,EAAE;EAC5B;EAEA;EACAyC,YAAYA,CAAA;IACV,IAAI,CAACnD,aAAa,GAAG,CAAC;IACtB,IAAI,CAAC4C,6BAA6B,EAAE;EACtC;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACtD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAAC4C,6BAA6B,EAAE;EACtC;EAEA;EACAS,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACzE,SAAS,GAAGyE,IAAI;IACrB,IAAI,CAAC7C,gBAAgB,EAAE;EACzB;EAEA8C,gBAAgBA,CAACD,IAAY;IAC3B,IAAI,CAAC9D,cAAc,GAAG8D,IAAI;IAC1B,IAAI,CAAC5C,mBAAmB,EAAE;EAC5B;EAEA8C,eAAeA,CAACF,IAAY;IAC1B,IAAI,CAACtD,aAAa,GAAGsD,IAAI;IACzB,IAAI,CAACV,6BAA6B,EAAE;EACtC;EAEA;EACAa,eAAeA,CAACC,KAAuB;IACrC,IAAI,CAACnD,UAAU,GAAG,KAAK;IACvB,IAAI,CAACvB,cAAc,GAAG;MACpB6B,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE9C,gBAAgB,CAAC2F,aAAa,CAAC;KAC/C;IACD,IAAI,CAACtE,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACQ,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACa,mBAAmB,EAAE;IAC1B,IAAI,CAACkC,6BAA6B,EAAE;IACpC,IAAI,CAACvE,aAAa,CAACuF,IAAI,CAACF,KAAK,EAAE;MAC7BG,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAACL,KAAuB,EAAEM,QAAsB;IAC3D,IAAI,CAACzD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACvB,cAAc,GAAG;MACpByC,WAAW,EAAEuC,QAAQ,CAACvC,WAAW;MACjCb,aAAa,EAAEoD,QAAQ,CAACpD,aAAa;MACrCE,aAAa,EAAEkD,QAAQ,CAAClD,aAAa,IAAI9C,gBAAgB,CAAC2F,aAAa;MACvE9C,OAAO,EAAEmD,QAAQ,CAACnD,OAAO,IAAI;KAC9B;IACD;IACA,IAAI,CAACxC,aAAa,CAACuF,IAAI,CAACF,KAAK,EAAE;MAC7BG,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAG,OAAOA,CAACC,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEA;EACA,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAAC7D,UAAU,GAAG,OAAO,GAAG,OAAO;EAC5C;EAEA8D,QAAQA,CAACH,GAAQ;IACf,IAAI,CAAC,IAAI,CAACI,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACtF,cAAc,CAACyC,WAAW,EAAE;MACnC,IAAI,CAAC8C,cAAc,CAACL,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACM,cAAc,CAACN,GAAG,CAAC;IAC1B;EACF;EAEA;EACAI,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACtF,cAAc,CAAC4B,aAAa,EAAE6D,IAAI,EAAE,EAAE;MAC9C,IAAI,CAACjG,OAAO,CAACuD,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC/C,cAAc,CAAC8B,aAAa,KAAK4D,SAAS,IAAI,IAAI,CAAC1F,cAAc,CAAC8B,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAACtC,OAAO,CAACuD,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC/C,cAAc,CAAC6B,OAAO,KAAK6D,SAAS,IAAI,IAAI,CAAC1F,cAAc,CAAC6B,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAACrC,OAAO,CAACuD,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC/C,cAAc,CAACyC,WAAW,IAAI,IAAI,CAACzC,cAAc,CAAC8B,aAAa,KAAK9C,gBAAgB,CAAC2F,aAAa,IAAI,IAAI,CAACtE,yBAAyB,CAACsF,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAACnG,OAAO,CAACuD,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC/C,cAAc,CAACyC,WAAW,IAAI,IAAI,CAACzC,cAAc,CAAC8B,aAAa,KAAK9C,gBAAgB,CAAC4G,YAAY,IAAI,IAAI,CAAC/E,wBAAwB,CAAC8E,MAAM,KAAK,CAAC,EAAE;MACzJ,IAAI,CAACnG,OAAO,CAACuD,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA;IACA,IAAI,IAAI,CAAC/C,cAAc,CAAC8B,aAAa,KAAK9C,gBAAgB,CAAC4G,YAAY,EAAE;MACvE,KAAK,MAAMpD,IAAI,IAAI,IAAI,CAAC3B,wBAAwB,EAAE;QAChD;QACA,IAAI2B,IAAI,CAACqB,UAAU,KAAK6B,SAAS,IAAIlD,IAAI,CAACqB,UAAU,KAAK,IAAI,IAAIrB,IAAI,CAACqB,UAAU,IAAI,CAAC,EAAE;UACrF,IAAI,CAACrE,OAAO,CAACuD,YAAY,CAAC,MAAMP,IAAI,CAACU,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;QAEA;QACA,IAAI,CAACV,IAAI,CAACuB,KAAK,IAAIvB,IAAI,CAACuB,KAAK,CAAC0B,IAAI,EAAE,KAAK,EAAE,IAAIjD,IAAI,CAACuB,KAAK,CAAC0B,IAAI,EAAE,KAAK,GAAG,EAAE;UACxE,IAAI,CAACjG,OAAO,CAACuD,YAAY,CAAC,MAAMP,IAAI,CAACU,KAAK,wBAAwB,CAAC;UACnE,OAAO,KAAK;QACd;QAEA;QACA,IAAI2C,KAAK,CAACrD,IAAI,CAACqB,UAAU,CAAC,EAAE;UAC1B,IAAI,CAACrE,OAAO,CAACuD,YAAY,CAAC,MAAMP,IAAI,CAACU,KAAK,aAAa,CAAC;UACxD,OAAO,KAAK;QACd;QAEA;QACA,IAAIV,IAAI,CAACuB,KAAK,CAAC0B,IAAI,EAAE,CAACE,MAAM,GAAG,EAAE,EAAE;UACjC,IAAI,CAACnG,OAAO,CAACuD,YAAY,CAAC,MAAMP,IAAI,CAACU,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;MACF;IACF;IAEA,OAAO,IAAI;EACb;EAEA;EACAsC,cAAcA,CAACN,GAAQ;IACrB,MAAMY,YAAY,GAAqB;MACrClE,aAAa,EAAE,IAAI,CAAC5B,cAAc,CAAC4B,aAAa;MAChDE,aAAa,EAAE,IAAI,CAAC9B,cAAc,CAAC8B,aAAa;MAChDD,OAAO,EAAE,IAAI,CAAC7B,cAAc,CAAC6B;KAC9B;IAED,IAAI,CAACvC,gBAAgB,CAACyG,gCAAgC,CAAC;MAAE7D,IAAI,EAAE4D;IAAY,CAAE,CAAC,CAAC3D,IAAI,CACjFpD,GAAG,CAACqD,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAM0D,UAAU,GAAGC,QAAQ,CAAC7D,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAAC4D,mBAAmB,CAACF,UAAU,EAAEd,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAC1F,OAAO,CAACuD,YAAY,CAACX,QAAQ,CAACY,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAsC,cAAcA,CAACL,GAAQ;IACrB,MAAMY,YAAY,GAAqB;MACrCrD,WAAW,EAAE,IAAI,CAACzC,cAAc,CAACyC,WAAW;MAC5Cb,aAAa,EAAE,IAAI,CAAC5B,cAAc,CAAC4B,aAAa;MAChDE,aAAa,EAAE,IAAI,CAAC9B,cAAc,CAAC8B,aAAa;MAChDD,OAAO,EAAE,IAAI,CAAC7B,cAAc,CAAC6B;KAC9B;IAED,IAAI,CAACvC,gBAAgB,CAACyG,gCAAgC,CAAC;MAAE7D,IAAI,EAAE4D;IAAY,CAAE,CAAC,CAAC3D,IAAI,CACjFpD,GAAG,CAACqD,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAC7C,OAAO,CAAC2G,aAAa,CAAC,QAAQ,CAAC;QACpCjB,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAAC1D,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACjC,OAAO,CAACuD,YAAY,CAACX,QAAQ,CAACY,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAiD,mBAAmBA,CAACF,UAAkB,EAAEd,GAAQ;IAC9C,IAAIkB,OAAO,GAAU,EAAE;IAEvB,IAAI,IAAI,CAACpG,cAAc,CAAC8B,aAAa,KAAK9C,gBAAgB,CAAC2F,aAAa,EAAE;MACxE;MACAyB,OAAO,GAAG,IAAI,CAAC/F,yBAAyB,CAACkC,GAAG,CAAC8D,KAAK,KAAK;QACrDC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAEF,KAAK,CAAChD,QAAQ;QAC1BH,KAAK,EAAEmD,KAAK,CAACnD,KAAK;QAClBC,SAAS,EAAEkD,KAAK,CAAClD;OAClB,CAAC,CAAC;IACL,CAAC,MAAM,IAAI,IAAI,CAACnD,cAAc,CAAC8B,aAAa,KAAK9C,gBAAgB,CAAC4G,YAAY,EAAE;MAC9E;MACAQ,OAAO,GAAG,IAAI,CAACvF,wBAAwB,CAAC0B,GAAG,CAACC,IAAI,KAAK;QACnD8D,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAE/D,IAAI,CAACa,QAAQ;QACzBH,KAAK,EAAEV,IAAI,CAACU,KAAK;QACjBC,SAAS,EAAEX,IAAI,CAACW,SAAS;QACzBU,UAAU,EAAErB,IAAI,CAACqB,UAAU;QAC3BE,KAAK,EAAEvB,IAAI,CAACuB;OACb,CAAC,CAAC;IACL;IAEA,IAAIqC,OAAO,CAACT,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMG,YAAY,GAAqB;QACrCrD,WAAW,EAAEuD,UAAU;QACvBpE,aAAa,EAAE,IAAI,CAAC5B,cAAc,CAAC4B,aAAa;QAChDE,aAAa,EAAE,IAAI,CAAC9B,cAAc,CAAC8B,aAAa;QAChDD,OAAO,EAAE,IAAI,CAAC7B,cAAc,CAAC6B,OAAO;QACpC2E,OAAO,EAAEJ;OACV;MAED,IAAI,CAAC9G,gBAAgB,CAACyG,gCAAgC,CAAC;QAAE7D,IAAI,EAAE4D;MAAY,CAAE,CAAC,CAAC3D,IAAI,CACjFpD,GAAG,CAACqD,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAAC7C,OAAO,CAAC2G,aAAa,CAAC,QAAQ,CAAC;UACpCjB,GAAG,CAACC,KAAK,EAAE;UACX,IAAI,CAAC1D,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACjC,OAAO,CAACuD,YAAY,CAACX,QAAQ,CAACY,OAAO,IAAI,YAAY,CAAC;QAC7D;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAACzD,OAAO,CAAC2G,aAAa,CAAC,QAAQ,CAAC;MACpCjB,GAAG,CAACC,KAAK,EAAE;MACX,IAAI,CAAC1D,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAgF,cAAcA,CAACzB,QAAsB;IACnC,IAAI0B,OAAO,CAAC,WAAW1B,QAAQ,CAACpD,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAACtC,gBAAgB,CAACqH,kCAAkC,CAAC;QACvDzE,IAAI,EAAE;UAAEO,WAAW,EAAEuC,QAAQ,CAACvC;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLpD,GAAG,CAACqD,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAAC7C,OAAO,CAAC2G,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC1E,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACjC,OAAO,CAACuD,YAAY,CAACX,QAAQ,CAACY,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACA2D,kBAAkBA,CAAC5B,QAAsB,EAAEN,KAAuB;IAChE,IAAI,CAACtD,sBAAsB,GAAG4D,QAAQ;IACtC,IAAI,CAAC1D,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACD,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAAChC,aAAa,CAACuF,IAAI,CAACF,KAAK,EAAE;MAC7BG,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAMnD,OAAO,GAA8B;MACzCqE,UAAU,EAAEhB,QAAQ,CAACvC;KACtB;IAED,IAAI,CAACnD,gBAAgB,CAACuH,yCAAyC,CAAC;MAAE3E,IAAI,EAAEP;IAAO,CAAE,CAAC,CAACQ,IAAI,CACrFpD,GAAG,CAACqD,QAAQ,IAAG;MACb,IAAI,CAACd,uBAAuB,GAAG,KAAK;MACpC,IAAIc,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAChB,oBAAoB,GAAGe,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzD+D,UAAU,EAAE/D,IAAI,CAAC+D,UAAW;UAC5BrD,KAAK,EAAEV,IAAI,CAACU,KAAM;UAClBC,SAAS,EAAEX,IAAI,CAACW;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAAC3D,OAAO,CAACuD,YAAY,CAACX,QAAQ,CAACY,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA6D,oBAAoBA,CAACT,KAAwB;IAC3CA,KAAK,CAAC/C,QAAQ,GAAG,CAAC+C,KAAK,CAAC/C,QAAQ;IAEhC,IAAI+C,KAAK,CAAC/C,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACjD,yBAAyB,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgD,KAAK,CAAChD,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAAChD,yBAAyB,CAAC0G,IAAI,CAAC;UAAE,GAAGV;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAChG,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2G,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgD,KAAK,CAAChD,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACI,4BAA4B,EAAE;EACrC;EAEAwD,eAAeA,CAAA;IACb,IAAI,CAACtG,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACP,eAAe,CAAC8G,OAAO,CAACb,KAAK,IAAG;MACnCA,KAAK,CAAC/C,QAAQ,GAAG,IAAI,CAAC3C,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACN,yBAAyB,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgD,KAAK,CAAChD,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAAChD,yBAAyB,CAAC0G,IAAI,CAAC;YAAE,GAAGV;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAAChG,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2G,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgD,KAAK,CAAChD,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEA8D,mBAAmBA,CAACd,KAAwB;IAC1C,IAAI,CAAChG,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2G,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgD,KAAK,CAAChD,QAAQ,CAAC;IAE1G,MAAM+D,cAAc,GAAG,IAAI,CAAChH,eAAe,CAAC0D,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgD,KAAK,CAAChD,QAAQ,CAAC;IACpF,IAAI+D,cAAc,EAAE;MAClBA,cAAc,CAAC9D,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACG,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAAC9C,iBAAiB,GAAG,IAAI,CAACP,eAAe,CAACuF,MAAM,GAAG,CAAC,IACtD,IAAI,CAACvF,eAAe,CAACiH,KAAK,CAAChB,KAAK,IAAIA,KAAK,CAAC/C,QAAQ,CAAC;EACvD;EAEA;EACAgE,mBAAmBA,CAAC9E,IAAsB;IACxCA,IAAI,CAACc,QAAQ,GAAG,CAACd,IAAI,CAACc,QAAQ;IAE9B,IAAId,IAAI,CAACc,QAAQ,EAAE;MACjB,IAAI,CAAC,IAAI,CAACzC,wBAAwB,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC,EAAE;QAC1E;QACA,MAAMkE,OAAO,GAAG;UACd,GAAG/E,IAAI;UACPqB,UAAU,EAAE,CAAC;UAAE;UACfE,KAAK,EAAE,EAAE,CAAC;SACX;QACD,IAAI,CAAClD,wBAAwB,CAACkG,IAAI,CAACQ,OAAO,CAAC;MAC7C;IACF,CAAC,MAAM;MACL,IAAI,CAAC1G,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACmG,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;IACzG;IAEA,IAAI,CAACW,2BAA2B,EAAE;EACpC;EAEAwD,cAAcA,CAAA;IACZ,IAAI,CAACrG,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAACP,yBAAyB,CAACsG,OAAO,CAAC1E,IAAI,IAAG;MAC5CA,IAAI,CAACc,QAAQ,GAAG,IAAI,CAACnC,gBAAgB;MACrC,IAAI,IAAI,CAACA,gBAAgB,EAAE;QACzB,IAAI,CAAC,IAAI,CAACN,wBAAwB,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC,EAAE;UAC1E;UACA,MAAMkE,OAAO,GAAG;YACd,GAAG/E,IAAI;YACPqB,UAAU,EAAE,CAAC;YAAE;YACfE,KAAK,EAAE,EAAE,CAAC;WACX;UACD,IAAI,CAAClD,wBAAwB,CAACkG,IAAI,CAACQ,OAAO,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAAC1G,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACmG,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;MACzG;IACF,CAAC,CAAC;EACJ;EAEAoE,kBAAkBA,CAACjF,IAAsB;IACvC,IAAI,CAAC3B,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACmG,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;IAEvG,MAAMqE,aAAa,GAAG,IAAI,CAAC9G,yBAAyB,CAACkD,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;IAC5F,IAAIqE,aAAa,EAAE;MACjBA,aAAa,CAACpE,QAAQ,GAAG,KAAK;IAChC;IAEA,IAAI,CAACU,2BAA2B,EAAE;EACpC;EAEAA,2BAA2BA,CAAA;IACzB,IAAI,CAAC7C,gBAAgB,GAAG,IAAI,CAACP,yBAAyB,CAAC+E,MAAM,GAAG,CAAC,IAC/D,IAAI,CAAC/E,yBAAyB,CAACyG,KAAK,CAAC7E,IAAI,IAAIA,IAAI,CAACc,QAAQ,CAAC;EAC/D;EAEA;EACAqE,eAAeA,CAACnF,IAAsB,EAAEoF,KAAa;IACnD;IACA,IAAI/B,KAAK,CAAC+B,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7BA,KAAK,GAAG,CAAC;IACX;IAEApF,IAAI,CAACqB,UAAU,GAAG+D,KAAK;IACvB;IACA,MAAMF,aAAa,GAAG,IAAI,CAAC9G,yBAAyB,CAACkD,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;IAC5F,IAAIqE,aAAa,EAAE;MACjBA,aAAa,CAAC7D,UAAU,GAAG+D,KAAK;IAClC;EACF;EAEAC,cAAcA,CAACrF,IAAsB,EAAEsF,IAAY;IACjD;IACAA,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAACrC,IAAI,EAAE,GAAG,EAAE;IAE9BjD,IAAI,CAACuB,KAAK,GAAG+D,IAAI;IACjB;IACA,MAAMJ,aAAa,GAAG,IAAI,CAAC9G,yBAAyB,CAACkD,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKb,IAAI,CAACa,QAAQ,CAAC;IAC5F,IAAIqE,aAAa,EAAE;MACjBA,aAAa,CAAC3D,KAAK,GAAG+D,IAAI;IAC5B;EACF;EAEA;EACAC,WAAWA,CAACvF,IAAsB;IAChC,OAAO,CAAC,EAAEA,IAAI,CAACqB,UAAU,IAAIrB,IAAI,CAACqB,UAAU,GAAG,CAAC,IAC9CrB,IAAI,CAACuB,KAAK,IAAIvB,IAAI,CAACuB,KAAK,CAAC0B,IAAI,EAAE,KAAK,EAAE,IAAIjD,IAAI,CAACuB,KAAK,CAAC0B,IAAI,EAAE,KAAK,GAAG,CAAC;EACxE;EAEA;EACAuC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACnH,wBAAwB,CAACmG,MAAM,CAACxE,IAAI,IAAI,IAAI,CAACuF,WAAW,CAACvF,IAAI,CAAC,CAAC,CAACmD,MAAM;EACpF;CACD;AAnlB8CsC,UAAA,EAA5CtJ,SAAS,CAAC,aAAa,EAAE;EAAEuJ,MAAM,EAAE;AAAK,CAAE,CAAC,C,qDAAgC;AACjCD,UAAA,EAA1CtJ,SAAS,CAAC,WAAW,EAAE;EAAEuJ,MAAM,EAAE;AAAK,CAAE,CAAC,C,mDAA8B;AACnBD,UAAA,EAApDtJ,SAAS,CAAC,qBAAqB,EAAE;EAAEuJ,MAAM,EAAE;AAAK,CAAE,CAAC,C,6DAAwC;AAPjFhJ,iBAAiB,GAAA+I,UAAA,EAX7BvJ,SAAS,CAAC;EACTyJ,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,2BAA2B;EACxCC,SAAS,EAAE,CAAC,2BAA2B,CAAC;EACxCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP3J,YAAY,EACZC,YAAY,EACZC,mBAAmB;CAEtB,CAAC,C,EACWI,iBAAiB,CAwlB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}