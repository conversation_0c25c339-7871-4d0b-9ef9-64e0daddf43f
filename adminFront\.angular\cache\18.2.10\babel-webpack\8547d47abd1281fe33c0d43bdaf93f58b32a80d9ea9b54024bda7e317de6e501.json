{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule } from '@nebular/theme';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_12_nb_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"nb-checkbox\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const template_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r5.selected, $event) || (template_r5.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTemplateItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 35);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const template_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r5.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r5.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r5.CTemplateId, \"\");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template, 7, 3, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"nb-icon\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u66AB\\u7121\\u53EF\\u7528\\u7684\", ctx_r1.getCurrentTemplateTypeName(), \"\\u9805\\u76EE\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20)(3, \"div\", 21);\n    i0.ɵɵelement(4, \"nb-icon\", 22);\n    i0.ɵɵtext(5, \"\\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-select\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_Template_nb_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedTemplateType, $event) || (ctx_r1.selectedTemplateType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_Template_nb_select_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTemplateTypeChange());\n    });\n    i0.ɵɵtemplate(7, SpaceTemplateSelectorComponent_div_12_nb_option_7_Template, 2, 2, \"nb-option\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 25);\n    i0.ɵɵelement(9, \"nb-icon\", 26);\n    i0.ɵɵtext(10, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 27);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_div_12_Template, 2, 1, \"div\", 28)(13, SpaceTemplateSelectorComponent_div_12_ng_template_13_Template, 3, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noTemplates_r6 = i0.ɵɵreference(14);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedTemplateType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.templateTypeOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templates.length > 0)(\"ngIfElse\", noTemplates_r6);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r7.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵelement(1, \"nb-icon\", 70);\n    i0.ɵɵtext(2, \" \\u55AE\\u50F9: \");\n    i0.ɵɵelementStart(3, \"strong\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind2(5, 1, detail_r7.CUnitPrice, \"1.0-2\"), \"\");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵelement(1, \"nb-icon\", 73);\n    i0.ɵɵtext(2, \" \\u55AE\\u4F4D: \");\n    i0.ɵɵelementStart(3, \"strong\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r7.CUnit);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtemplate(2, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_2_Template, 6, 4, \"span\", 67)(3, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_3_Template, 5, 1, \"span\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r7.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r7.CUnit);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60)(4, \"div\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template, 2, 1, \"div\", 62)(7, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_Template, 4, 2, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r8 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r7.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r7.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r7.CUnitPrice || detail_r7.CUnit);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"span\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 56);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template, 8, 4, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r1.getTemplateDetails(item_r9.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplateDetails(item_r9.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"nb-icon\", 37);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"h5\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"span\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 51);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template, 6, 2, \"div\", 52)(11, SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const noDetails_r10 = i0.ɵɵreference(12);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r9.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r9.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getTemplateDetails(item_r9.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r10);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77)(2, \"strong\");\n    i0.ɵɵelement(3, \"nb-icon\", 78);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r1.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 38)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"nb-icon\", 39);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"div\", 41);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 42);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_13_div_12_Template, 13, 5, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_div_13_Template, 6, 1, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentTemplateTypeName());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r1.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.CTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n    // 新增：模板類型選擇相關屬性\n    this.selectedTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\n  }\n  ngOnInit() {\n    // 初始化選擇的模板類型\n    this.selectedTemplateType = this.CTemplateType;\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數，使用當前選擇的模板類型\n    const getTemplateListArgs = {\n      CTemplateType: this.selectedTemplateType,\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  // 新增：當模板類型選擇變更時的處理\n  onTemplateTypeChange() {\n    // 清空當前選擇\n    this.resetSelections();\n    // 重新載入對應類型的模板\n    this.loadTemplatesFromAPI();\n  }\n  // 新增：獲取當前選擇的模板類型名稱\n  getCurrentTemplateTypeName() {\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const progressTexts = {\n      1: `請選擇要套用的${templateTypeName}項目`,\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: templateTypeName,\n      selectedItems: this.getSelectedItems(),\n      templateDetails: new Map(this.selectedTemplateDetails),\n      // 傳遞已載入的模板明細\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        CTemplateType: \"CTemplateType\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 16,\n      consts: [[\"noTemplates\", \"\"], [\"noDetails\", \"\"], [1, \"space-template-dialog\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"template-type-selector\"], [1, \"selector-label\"], [\"icon\", \"options-2-outline\", 1, \"mr-2\"], [\"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B\", 1, \"template-type-select\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"section-title\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [1, \"template-list\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"value\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"no-templates\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [\"class\", \"detail-pricing\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"detail-pricing\"], [1, \"pricing-info\"], [\"class\", \"price-label\", 4, \"ngIf\"], [\"class\", \"unit-label\", 4, \"ngIf\"], [1, \"price-label\"], [\"icon\", \"dollar-sign-outline\", 1, \"price-icon\"], [1, \"price-value\"], [1, \"unit-label\"], [\"icon\", \"cube-outline\", 1, \"unit-icon\"], [1, \"unit-value\"], [1, \"no-details\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [\"icon\", \"alert-triangle-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 7)(7, \"div\", 8)(8, \"div\", 9);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_Template, 15, 4, \"div\", 10)(13, SpaceTemplateSelectorComponent_div_13_Template, 14, 4, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 11)(15, \"div\", 12)(16, \"span\");\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_19_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SpaceTemplateSelectorComponent_button_21_Template, 2, 0, \"button\", 15)(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 1, \"button\", 16)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DecimalPipe, FormsModule, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n.space-template-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  background-color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: #ADB5BD;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #2C3E50;\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-radius: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: #FFFFFF;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  margin: 0 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: 0.3s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  color: #ADB5BD;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #AE9B66;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%] {\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  padding: 1rem;\\n  background-color: #F8F9FA;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #AE9B66;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .template-type-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 300px;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .template-type-select[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  transition: 0.3s ease;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  border-color: #AE9B66;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  background-color: rgba(184, 166, 118, 0.03);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #A69660;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-id[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #28A745;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%]   .detail-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  border-radius: 50%;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n  margin-bottom: 0.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  align-items: center;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .price-icon[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .unit-icon[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .price-icon[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .unit-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  font-size: 0.875rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .price-icon[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .price-icon[_ngcontent-%COMP%] {\\n  color: #28A745;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .unit-icon[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .unit-icon[_ngcontent-%COMP%] {\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%] {\\n  color: #1E7E34;\\n  font-weight: 600;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .unit-value[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .unit-value[_ngcontent-%COMP%] {\\n  color: #138496;\\n  font-weight: 600;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%] {\\n  background-color: #FFF3CD;\\n  border: 1px solid #FFC107;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-top: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%] {\\n  color: #E0A800;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  color: #FFC107;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #E9ECEF;\\n  background-color: #F8F9FA;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #ADB5BD;\\n  font-size: 0.875rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #AE9B66;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  border-color: #28A745;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1E7E34;\\n  border-color: #1E7E34;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-color: #CDCDCD;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-color: #5A5A5A;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .space-template-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%] {\\n  color: #D4EDDA;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .unit-value[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%]   .unit-value[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .unit-value[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-label[_ngcontent-%COMP%]   .unit-value[_ngcontent-%COMP%] {\\n  color: #D1ECF1;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-top-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNwYWNlLXRlbXBsYXRlLXNlbGVjdG9yLmNvbXBvbmVudC5zY3NzIiwiLi5cXC4uXFwuLlxcQHRoZW1lXFxzdHlsZXNcXF9jb2xvcnMuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUNBaEI7OztFQUFBO0FER0EsNkJBQUE7QUFDQTtFQUNFLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBR0Y7QUFERTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQ0FBQTtFQUNBLHlCQzBCUztBRHZCYjtBQURJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNDWVM7QURUZjtBQUFJO0VBQ0UsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLHVCQUFBO0VBQ0EsY0NPTztFRE5QLHNCQytEWTtBRDdEbEI7QUFBTTtFQUNFLGNBQUE7RUFDQSwyQ0NhRztFRFpILHNCQUFBO0FBRVI7QUFDTTtFQUNFLGtCQUFBO0FBQ1I7QUFJRTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EseUJDTlM7RURRVCxTQUFBO0VBbUNBLFNBQUE7QUFyQ0o7QUFHSTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0NBQUE7RUFDQSxvQkFBQTtBQUROO0FBR007RUFDRSxvQkFBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQytCWTtBRGhDcEI7QUFHUTtFQUNFLDZEQ3ZDUztFRHdDVCxjQzdCRztFRDhCSCw4Q0NlRTtBRGhCWjtBQUlRO0VBQ0UseUJDWks7RURhTCxjQ25DRztFRG9DSCw4Q0NTRTtBRFhaO0FBS1E7RUFDRSx5QkNuQ0s7RURvQ0wsY0MzQ0c7RUQ0Q0gseUJBQUE7QUFIVjtBQVNJO0VBQ0UsaUJBQUE7RUFnQkEsV0FBQTtFQW9HQSxXQUFBO0FBekhOO0FBT007RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0M3RE87RUQ4RFAsbUJBQUE7QUFMUjtBQU9RO0VBQ0Usb0JBQUE7RUFDQSxjQzVGVTtBRHVGcEI7QUFVTTtFQUVFLFlBQUE7QUFUUjtBQVVRO0VBQ0UscUJBQUE7RUFDQSxhQUFBO0VBQ0EseUJDbkVLO0VEb0VMLHlCQUFBO0VBQ0EsdUJBQUE7QUFSVjtBQVVVO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0N0Rkc7RUR1Rkgsc0JBQUE7QUFSWjtBQVVZO0VBQ0Usb0JBQUE7RUFDQSxjQ3JITTtBRDZHcEI7QUFZVTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtBQVZaO0FBWVk7RUFDRSxXQUFBO0FBVmQ7QUFnQlU7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0EscUJDbERRO0VEbURSLHlCQ3ZHQztFRHdHRCw4Q0MvREE7QURpRFo7QUFnQlk7RUFDRSxxQkM5SU07RUQrSU4sK0NDbEVGO0VEbUVFLDJDQ3pHRTtBRDJGaEI7QUFpQlk7RUFDRSxXQUFBO0FBZmQ7QUFpQmM7RUFDRSxtQkFBQTtFQUNBLE9BQUE7QUFmaEI7QUFpQmdCO0VBQ0UsZ0JBQUE7RUFDQSxjQ2xJSDtFRG1JRyxzQkFBQTtBQWZsQjtBQWtCZ0I7RUFDRSxtQkFBQTtFQUNBLGNDcklMO0VEc0lLLHNCQUFBO0FBaEJsQjtBQW1CZ0I7RUFDRSxtQkFBQTtFQUNBLGNDN0lEO0VEOElDLHNCQUFBO0FBakJsQjtBQW9CZ0I7RUFDRSxtQkFBQTtFQUNBLGNDbkpEO0FEaUlqQjtBQXdCVTtFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLGNDMUpDO0VEMkpELHlCQ3BKRztFRHFKSCx1QkFBQTtFQUNBLHlCQUFBO0FBdEJaO0FBd0JZO0VBQ0Usb0JBQUE7RUFDQSxjQ3ZIRjtBRGlHWjtBQThCUTtFQUNFLDZEQ2pMZTtFRGtMZiwwQ0FBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLHFCQUFBO0FBNUJWO0FBOEJVO0VBQ0UsY0NwTEc7RURxTEgsZ0JBQUE7QUE1Qlo7QUE4Qlk7RUFDRSxjQ2pOTTtBRHFMcEI7QUFrQ1U7RUFDRSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQzFMQztFRDJMRCw4Q0NsSkE7QURrSFo7QUFrQ1k7RUFDRSw2REMzTVc7RUQ0TVgsYUFBQTtFQUNBLGdDQUFBO0FBaENkO0FBa0NjO0VBQ0Usb0JBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0MvTUQ7QUQrS2Y7QUFtQ2M7RUFDRSxhQUFBO0VBQ0EsU0FBQTtBQWpDaEI7QUFtQ2dCO0VBQ0UsbUJBQUE7RUFDQSxjQ3JOTDtBRG9MYjtBQW9DZ0I7RUFDRSxtQkFBQTtFQUNBLGNDbE1IO0VEbU1HLGdCQUFBO0FBbENsQjtBQXVDWTtFQUNFLGFBQUE7QUFyQ2Q7QUF1Q2M7RUFDRSxzQkFBQTtBQXJDaEI7QUF1Q2dCO0VBQ0UsZ0JBQUE7RUFDQSxjQzFPRDtBRHFNakI7QUEwQ2dCO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQ0FBQTtBQXhDbEI7QUEwQ2tCO0VBQ0UsbUJBQUE7QUF4Q3BCO0FBMkNrQjtFQUNFLDZEQ2pRRDtFRGtRQyxjQ3ZQUDtFRHdQTyxrQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBekNwQjtBQTRDa0I7RUFDRSxPQUFBO0FBMUNwQjtBQTRDb0I7RUFDRSxnQkFBQTtFQUNBLGNDOVFQO0VEK1FPLHNCQUFBO0FBMUN0QjtBQTZDb0I7RUFDRSxtQkFBQTtFQUNBLGNDalJUO0VEa1JTLHFCQUFBO0FBM0N0QjtBQThDb0I7RUFDRSxrQkFBQTtBQTVDdEI7QUE4Q3NCO0VBQ0UsYUFBQTtFQUNBLGVBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUE1Q3hCO0FBOEN3Qjs7RUFFRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGNDclNUO0FEeVBqQjtBQThDMEI7Ozs7RUFFRSxxQkFBQTtFQUNBLG1CQUFBO0FBMUM1QjtBQTZDMEI7O0VBQ0UsY0NwUmI7QUQwT2Y7QUE2QzBCOztFQUNFLGNDdFFoQjtBRDROWjtBQTZDMEI7O0VBQ0UsY0MzUmI7RUQ0UmEsZ0JBQUE7QUExQzVCO0FBNkMwQjs7RUFDRSxjQzlRaEI7RUQrUWdCLGdCQUFBO0FBMUM1QjtBQW1EYztFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLGNDdFVIO0VEdVVHLHlCQ2hVRDtFRGlVQyx1QkFBQTtBQWpEaEI7QUFtRGdCO0VBQ0Usb0JBQUE7RUFDQSxjQ2xTTjtBRGlQWjtBQXdEUTtFQUNFLHlCQ3ZUTTtFRHdUTix5QkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLGdCQUFBO0FBdERWO0FBd0RVO0VBQ0UsY0M1VEc7RUQ2VEgsZ0JBQUE7QUF0RFo7QUF3RFk7RUFDRSxxQkFBQTtFQUNBLGNDbFVDO0FENFFmO0FBOERFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLDZCQUFBO0VBQ0EseUJDdldXO0FEMlNmO0FBOERJO0VBQ0UsY0NqWE87RURrWFAsbUJBQUE7QUE1RE47QUErREk7RUFDRSxhQUFBO0VBQ0EsWUFBQTtBQTdETjtBQStETTtFQUNFLGVBQUE7RUFDQSxzQkNsVVU7QURxUWxCO0FBK0RRO0VBQ0UsNkRDdllTO0VEd1lULHFCQzVaVTtFRDZaVixjQzlYRztBRGlVYjtBQStEVTtFQUNFLDZEQzNZYTtFRDRZYiwyQkFBQTtFQUNBLCtDQ3JWQTtBRHdSWjtBQWlFUTtFQUNFLHlCQ2xYSztFRG1YTCxxQkNuWEs7RURvWEwsY0MxWUc7QUQyVWI7QUFpRVU7RUFDRSx5QkN0WEc7RUR1WEgscUJDdlhHO0VEd1hILDJCQUFBO0VBQ0EsK0NDbFdBO0FEbVNaO0FBbUVRO0VBQ0UsNkJBQUE7RUFDQSxxQkN4WU07RUR5WU4sY0MzWk87QUQwVmpCO0FBbUVVO0VBQ0UsMkNDalpEO0VEa1pDLHFCQy9aSztFRGdhTCxjQ2phRztBRGdXZjtBQXFFUTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtBQW5FVjs7QUEwRUEsVUFBQTtBQUNBO0VBQ0U7SUFDRSxlQUFBO0lBQ0EsZUFBQTtJQUNBLGNBQUE7RUF2RUY7RUF5RUU7SUFDRSxhQUFBO0VBdkVKO0VBMEVNO0lBQ0UsbUJBQUE7SUFDQSxzQkFBQTtJQUNBLGlCQUFBO0VBeEVSO0VBK0VVO0lBQ0UsZ0JBQUE7RUE3RVo7RUFpRmdCO0lBQ0UsaUJBQUE7RUEvRWxCO0VBa0ZnQjs7O0lBR0UsaUJBQUE7RUFoRmxCO0VBMEZFO0lBQ0Usc0JBQUE7SUFDQSxZQUFBO0lBQ0Esb0JBQUE7RUF4Rko7RUEwRkk7SUFDRSx1QkFBQTtFQXhGTjtBQUNGO0FBNkZBLHFCQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0EsV0FBQTtBQTNGRjs7QUE4RkEsd0JBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7QUEzRkY7QUE2RkU7RUFDRSxxQkFBQTtFQUNBLG9CQUFBO0FBM0ZKOztBQStGQSxTQUFBO0FBQ0E7RUFDRSxxQkFBQTtBQTVGRjs7QUErRkE7RUFDRSxvQkFBQTtBQTVGRjs7QUErRkEsV0FBQTtBQUdJO0VBQ0UseUJDbFlZO0VEbVlaLDRCQy9YUTtBRGlTZDtBQWdHTTtFQUNFLGNDcFlZO0FEc1NwQjtBQWlHTTtFQUNFLGNDdlljO0FEd1N0QjtBQWlHUTtFQUNFLGNDM1lVO0VENFlWLDBDQUFBO0FBL0ZWO0FBb0dJO0VBQ0UseUJDcFpZO0FEa1RsQjtBQW9HTTtFQUNFLDRCQ25aTTtBRGlUZDtBQXNHUTtFQUNFLGNDMVpVO0FEc1RwQjtBQXdHVTtFQUNFLHlCQ2hhUTtFRGlhUixxQkM5WkU7QUR3VGQ7QUF3R1k7RUFDRSxjQ25hTTtBRDZUcEI7QUEyR1k7RUFDRSx5QkMxYU07RUQyYU4scUJDeGFBO0FEK1RkO0FBMkdjO0VBQ0UsOENBQUE7QUF6R2hCO0FBNkdnQjtFQUNFLGNDbGJFO0FEdVVwQjtBQThHZ0I7OztFQUdFLGNDdmJJO0FEMlV0QjtBQWlIWTtFQUNFLHlCQy9iTTtFRGdjTixxQkM3YkE7RUQ4YkEsY0MvYlE7QURnVnRCO0FBMkhzQjtFQUNFLGNDN2NKO0FEb1ZwQjtBQTRIc0I7RUFDRSxjQ2hkRjtBRHNWdEI7QUFnSTBCOztFQUVFLGNDeGROO0FEMFZ0QjtBQWdJNEI7O0VBQ0UsY0N0a0JkO0FEeWNoQjtBQWdJNEI7O0VBQ0UsY0N4akJqQjtBRDJiYjtBQTRJSTtFQUNFLHlCQ2pmYztFRGtmZCx5QkMvZVE7QURxV2Q7QUE0SU07RUFDRSxjQ25mYztBRHlXdEIiLCJmaWxlIjoic3BhY2UtdGVtcGxhdGUtc2VsZWN0b3IuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvLyDlsI7lhaXkuLvpoYzoibLlvanorormlbhcclxuQGltcG9ydCAnLi4vLi4vLi4vQHRoZW1lL3N0eWxlcy9fY29sb3JzJztcclxuXHJcbi8qIOepuumWk+aooeadv+mBuOaTh+WZqCAtIG5iLWRpYWxvZyDniYjmnKzmqKPlvI8gKi9cclxuLnNwYWNlLXRlbXBsYXRlLWRpYWxvZyB7XHJcbiAgbWluLXdpZHRoOiA2MDBweDtcclxuICBtYXgtd2lkdGg6IDgwMHB4O1xyXG4gIG1pbi1oZWlnaHQ6IDUwMHB4O1xyXG4gIG1heC1oZWlnaHQ6IDgwdmg7XHJcblxyXG4gIC5zcGFjZS10ZW1wbGF0ZS1oZWFkZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nOiAxLjI1cmVtIDEuNXJlbTtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkYm9yZGVyLWxpZ2h0O1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogJGJnLXByaW1hcnk7XHJcblxyXG4gICAgLnNwYWNlLXRlbXBsYXRlLXRpdGxlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogJHRleHQtcHJpbWFyeTtcclxuICAgIH1cclxuXHJcbiAgICAuY2xvc2UtYnRuIHtcclxuICAgICAgcGFkZGluZzogMC4yNXJlbTtcclxuICAgICAgbWluLXdpZHRoOiBhdXRvO1xyXG4gICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gICAgICBjb2xvcjogJHRleHQtbXV0ZWQ7XHJcbiAgICAgIHRyYW5zaXRpb246ICR0cmFuc2l0aW9uLWZhc3Q7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBjb2xvcjogJHRleHQtcHJpbWFyeTtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkYmctaG92ZXI7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICAgICAgfVxyXG5cclxuICAgICAgbmItaWNvbiB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuc3BhY2UtdGVtcGxhdGUtYm9keSB7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgbWF4LWhlaWdodDogNjB2aDtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICRiZy1wcmltYXJ5O1xyXG5cclxuICAgIC8qIOatpempn+WwjuiIqiAqL1xyXG4gICAgLnN0ZXAtbmF2IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XHJcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkYm9yZGVyLWxpZ2h0O1xyXG4gICAgICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcclxuXHJcbiAgICAgIC5zdGVwLWl0ZW0ge1xyXG4gICAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xyXG4gICAgICAgIG1hcmdpbjogMCAwLjVyZW07XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIHRyYW5zaXRpb246ICR0cmFuc2l0aW9uLW5vcm1hbDtcclxuXHJcbiAgICAgICAgJi5hY3RpdmUge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogJGdyYWRpZW50LXByaW1hcnk7XHJcbiAgICAgICAgICBjb2xvcjogJHRleHQtbGlnaHQ7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJi5jb21wbGV0ZWQge1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJHN1Y2Nlc3MtYmFzZTtcclxuICAgICAgICAgIGNvbG9yOiAkdGV4dC1saWdodDtcclxuICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctc207XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmLnBlbmRpbmcge1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGJnLXNlY29uZGFyeTtcclxuICAgICAgICAgIGNvbG9yOiAkdGV4dC1tdXRlZDtcclxuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRib3JkZXItbGlnaHQ7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyog5q2l6amf5YWn5a65ICovXHJcbiAgICAuc3RlcC1jb250ZW50IHtcclxuICAgICAgbWluLWhlaWdodDogMzAwcHg7XHJcblxyXG4gICAgICAuc2VjdGlvbi10aXRsZSB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgY29sb3I6ICR0ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuXHJcbiAgICAgICAgbmItaWNvbiB7XHJcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxuICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeS1nb2xkLWJhc2U7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvKiDmqKHmnb/pgbjmk4fljYDln58gKi9cclxuICAgICAgLnRlbXBsYXRlLXNlbGVjdGlvbiB7XHJcblxyXG4gICAgICAgIC8qIOaooeadv+mhnuWei+mBuOaTh+WZqCAqL1xyXG4gICAgICAgIC50ZW1wbGF0ZS10eXBlLXNlbGVjdG9yIHtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxuICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkYmctc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgJGJvcmRlci1saWdodDtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG5cclxuICAgICAgICAgIC5zZWxlY3Rvci1sYWJlbCB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgY29sb3I6ICR0ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XHJcblxyXG4gICAgICAgICAgICBuYi1pY29uIHtcclxuICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxuICAgICAgICAgICAgICBjb2xvcjogJHByaW1hcnktZ29sZC1iYXNlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLnRlbXBsYXRlLXR5cGUtc2VsZWN0IHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIG1heC13aWR0aDogMzAwcHg7XHJcblxyXG4gICAgICAgICAgICBuYi1zZWxlY3Qge1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAudGVtcGxhdGUtbGlzdCB7XHJcbiAgICAgICAgICAudGVtcGxhdGUtaXRlbSB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRib3JkZXItbGlnaHQ7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAkdHJhbnNpdGlvbi1ub3JtYWw7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRiZy1wcmltYXJ5O1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1nb2xkLWJhc2U7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZDtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkYmctbGlnaHQtZ29sZDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgbmItY2hlY2tib3gge1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG5cclxuICAgICAgICAgICAgICAudGVtcGxhdGUtaW5mbyB7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgZmxleDogMTtcclxuXHJcbiAgICAgICAgICAgICAgICAuaXRlbS1uYW1lIHtcclxuICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICR0ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLml0ZW0tY29kZSB7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1tdXRlZDtcclxuICAgICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuaXRlbS1zdGF0dXMge1xyXG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJHRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC5pdGVtLXR5cGUge1xyXG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJHRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5uby10ZW1wbGF0ZXMge1xyXG4gICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1tdXRlZDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGJnLXNlY29uZGFyeTtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRib3JkZXItbGlnaHQ7XHJcblxyXG4gICAgICAgICAgICBuYi1pY29uIHtcclxuICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxuICAgICAgICAgICAgICBjb2xvcjogJGluZm8tYmFzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLyog56K66KqN5aWX55So5Y2A5Z+fICovXHJcbiAgICAgIC5jb25maXJtYXRpb24tYXJlYSB7XHJcbiAgICAgICAgLnNlbGVjdGVkLXN1bW1hcnkge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogJGdyYWRpZW50LXByaW1hcnktbGlnaHQ7XHJcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAkYm9yZGVyLXByaW1hcnk7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcblxyXG4gICAgICAgICAgLnN1bW1hcnktdGV4dCB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1wcmltYXJ5O1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG5cclxuICAgICAgICAgICAgc3Ryb25nIHtcclxuICAgICAgICAgICAgICBjb2xvcjogJHByaW1hcnktZ29sZC1kYXJrO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc2VsZWN0ZWQtdGVtcGxhdGVzLWRldGFpbHMge1xyXG4gICAgICAgICAgLnRlbXBsYXRlLWRldGFpbC1zZWN0aW9uIHtcclxuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgJGJvcmRlci1saWdodDtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRiZy1wcmltYXJ5O1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXNtO1xyXG5cclxuICAgICAgICAgICAgLnRlbXBsYXRlLWRldGFpbC1oZWFkZXIge1xyXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICRncmFkaWVudC1wcmltYXJ5LWxpZ2h0O1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICRib3JkZXItbGlnaHQ7XHJcblxyXG4gICAgICAgICAgICAgIC50ZW1wbGF0ZS1uYW1lIHtcclxuICAgICAgICAgICAgICAgIG1hcmdpbjogMCAwIDAuNXJlbSAwO1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICR0ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAudGVtcGxhdGUtbWV0YSB7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgZ2FwOiAxcmVtO1xyXG5cclxuICAgICAgICAgICAgICAgIC50ZW1wbGF0ZS1pZCB7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1tdXRlZDtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAudGVtcGxhdGUtc3RhdHVzIHtcclxuICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICRzdWNjZXNzLWJhc2U7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAudGVtcGxhdGUtZGV0YWlsLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcblxyXG4gICAgICAgICAgICAgIC5kZXRhaWwtaXRlbXMtaGVhZGVyIHtcclxuICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgLmRldGFpbC1jb3VudCB7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1zZWNvbmRhcnk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAuZGV0YWlsLWl0ZW1zLWxpc3Qge1xyXG4gICAgICAgICAgICAgICAgLmRldGFpbC1pdGVtIHtcclxuICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbSAwO1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGJvcmRlci1saWdodDtcclxuXHJcbiAgICAgICAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgLmRldGFpbC1pbmRleCB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJGdyYWRpZW50LXByaW1hcnk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICR0ZXh0LWxpZ2h0O1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogMS41cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogMS41cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgIC5kZXRhaWwtaW5mbyB7XHJcbiAgICAgICAgICAgICAgICAgICAgZmxleDogMTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmRldGFpbC1wYXJ0IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJHRleHQtcHJpbWFyeTtcclxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuZGV0YWlsLWxvY2F0aW9uIHtcclxuICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJHRleHQtbXV0ZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuZGV0YWlsLXByaWNpbmcge1xyXG4gICAgICAgICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMC41cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIC5wcmljaW5nLWluZm8ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5wcmljZS1sYWJlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgLnVuaXQtbGFiZWwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1zZWNvbmRhcnk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC5wcmljZS1pY29uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC51bml0LWljb24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC5wcmljZS1pY29uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAkc3VjY2Vzcy1iYXNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLnVuaXQtaWNvbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJGluZm8tYmFzZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC5wcmljZS12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJHN1Y2Nlc3MtZGFyaztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAudW5pdC12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJGluZm8tZGFyaztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAubm8tZGV0YWlscyB7XHJcbiAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICR0ZXh0LW11dGVkO1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGJnLXNlY29uZGFyeTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgIG5iLWljb24ge1xyXG4gICAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICRpbmZvLWJhc2U7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY29uZmxpY3Qtd2FybmluZyB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkd2FybmluZy1saWdodDtcclxuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICR3YXJuaW5nLWJhc2U7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgICBtYXJnaW4tdG9wOiAxcmVtO1xyXG5cclxuICAgICAgICAgIC53YXJuaW5nLXRleHQge1xyXG4gICAgICAgICAgICBjb2xvcjogJHdhcm5pbmctZGFyaztcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuXHJcbiAgICAgICAgICAgIG5iLWljb24ge1xyXG4gICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcclxuICAgICAgICAgICAgICBjb2xvcjogJHdhcm5pbmctYmFzZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLnNwYWNlLXRlbXBsYXRlLWZvb3RlciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xyXG4gICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICRib3JkZXItbGlnaHQ7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkYmctc2Vjb25kYXJ5O1xyXG5cclxuICAgIC5wcm9ncmVzcy1pbmZvIHtcclxuICAgICAgY29sb3I6ICR0ZXh0LW11dGVkO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgfVxyXG5cclxuICAgIC5zdGVwLWJ1dHRvbnMge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDAuNzVyZW07XHJcblxyXG4gICAgICBidXR0b24ge1xyXG4gICAgICAgIG1pbi13aWR0aDogODBweDtcclxuICAgICAgICB0cmFuc2l0aW9uOiAkdHJhbnNpdGlvbi1mYXN0O1xyXG5cclxuICAgICAgICAmW25iQnV0dG9uXVtzdGF0dXM9XCJwcmltYXJ5XCJdIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICRidG4tcHJpbWFyeS1iZztcclxuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZ29sZC1iYXNlO1xyXG4gICAgICAgICAgY29sb3I6ICR0ZXh0LWxpZ2h0O1xyXG5cclxuICAgICAgICAgICY6aG92ZXI6bm90KDpkaXNhYmxlZCkge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkYnRuLXByaW1hcnktaG92ZXI7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1tZDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICZbbmJCdXR0b25dW3N0YXR1cz1cInN1Y2Nlc3NcIl0ge1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJHN1Y2Nlc3MtYmFzZTtcclxuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHN1Y2Nlc3MtYmFzZTtcclxuICAgICAgICAgIGNvbG9yOiAkdGV4dC1saWdodDtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJHN1Y2Nlc3MtZGFyaztcclxuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkc3VjY2Vzcy1kYXJrO1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmW25iQnV0dG9uXVtzdGF0dXM9XCJiYXNpY1wiXSB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcclxuICAgICAgICAgIGJvcmRlci1jb2xvcjogJGJvcmRlci1tZWRpdW07XHJcbiAgICAgICAgICBjb2xvcjogJHRleHQtc2Vjb25kYXJ5O1xyXG5cclxuICAgICAgICAgICY6aG92ZXI6bm90KDpkaXNhYmxlZCkge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkYmctaG92ZXI7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJHRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICBjb2xvcjogJHRleHQtcHJpbWFyeTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6ZGlzYWJsZWQge1xyXG4gICAgICAgICAgb3BhY2l0eTogMC42O1xyXG4gICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8qIOmfv+aHieW8j+ioreioiCAqL1xyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuc3BhY2UtdGVtcGxhdGUtZGlhbG9nIHtcclxuICAgIG1pbi13aWR0aDogOTV2dztcclxuICAgIG1heC13aWR0aDogOTV2dztcclxuICAgIG1hcmdpbjogMC41cmVtO1xyXG5cclxuICAgIC5zcGFjZS10ZW1wbGF0ZS1ib2R5IHtcclxuICAgICAgcGFkZGluZzogMXJlbTtcclxuXHJcbiAgICAgIC5zdGVwLW5hdiB7XHJcbiAgICAgICAgLnN0ZXAtaXRlbSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgcGFkZGluZzogMC40cmVtIDAuOHJlbTtcclxuICAgICAgICAgIG1hcmdpbjogMCAwLjI1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnN0ZXAtY29udGVudCB7XHJcbiAgICAgICAgLnRlbXBsYXRlLXNlbGVjdGlvbiB7XHJcbiAgICAgICAgICAudGVtcGxhdGUtbGlzdCB7XHJcbiAgICAgICAgICAgIC50ZW1wbGF0ZS1pdGVtIHtcclxuICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG5cclxuICAgICAgICAgICAgICBuYi1jaGVja2JveCB7XHJcbiAgICAgICAgICAgICAgICAudGVtcGxhdGUtaW5mbyB7XHJcbiAgICAgICAgICAgICAgICAgIC5pdGVtLW5hbWUge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAuaXRlbS1jb2RlLFxyXG4gICAgICAgICAgICAgICAgICAuaXRlbS1zdGF0dXMsXHJcbiAgICAgICAgICAgICAgICAgIC5pdGVtLXR5cGUge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnNwYWNlLXRlbXBsYXRlLWZvb3RlciB7XHJcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgIGdhcDogMC43NXJlbTtcclxuICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XHJcblxyXG4gICAgICAuc3RlcC1idXR0b25zIHtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLyogbmItZGlhbG9nIOWFp+WuueWNgOWfn+iqv+aVtCAqL1xyXG46aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi8qIOeiuuS/nSBuYi1jaGVja2JveCDmraPluLjpoa/npLogKi9cclxubmItY2hlY2tib3gge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgd2lkdGg6IDEwMCU7XHJcblxyXG4gIC5jdXN0b21pc2VkLWNvbnRyb2wtaW5wdXQge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiAwLjc1cmVtO1xyXG4gICAgbWFyZ2luLXRvcDogMC4xMjVyZW07XHJcbiAgfVxyXG59XHJcblxyXG4vKiDlt6XlhbfpoZ7liKUgKi9cclxuLm1yLTEge1xyXG4gIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcclxufVxyXG5cclxuLm1yLTIge1xyXG4gIG1hcmdpbi1yaWdodDogMC41cmVtO1xyXG59XHJcblxyXG4vKiDmt7HoibLkuLvpoYzmlK/mj7QgKi9cclxuOmhvc3QtY29udGV4dCgubmItdGhlbWUtZGFyaykge1xyXG4gIC5zcGFjZS10ZW1wbGF0ZS1kaWFsb2cge1xyXG4gICAgLnNwYWNlLXRlbXBsYXRlLWhlYWRlciB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICRkYXJrLWJnLXByaW1hcnk7XHJcbiAgICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICRkYXJrLWJvcmRlcjtcclxuXHJcbiAgICAgIC5zcGFjZS10ZW1wbGF0ZS10aXRsZSB7XHJcbiAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtcHJpbWFyeTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmNsb3NlLWJ0biB7XHJcbiAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtc2Vjb25kYXJ5O1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGNvbG9yOiAkZGFyay10ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnNwYWNlLXRlbXBsYXRlLWJvZHkge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkZGFyay1iZy1wcmltYXJ5O1xyXG5cclxuICAgICAgLnN0ZXAtbmF2IHtcclxuICAgICAgICBib3JkZXItYm90dG9tLWNvbG9yOiAkZGFyay1ib3JkZXI7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5zdGVwLWNvbnRlbnQge1xyXG4gICAgICAgIC5zZWN0aW9uLXRpdGxlIHtcclxuICAgICAgICAgIGNvbG9yOiAkZGFyay10ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAudGVtcGxhdGUtc2VsZWN0aW9uIHtcclxuICAgICAgICAgIC50ZW1wbGF0ZS10eXBlLXNlbGVjdG9yIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGRhcmstYmctc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICBib3JkZXItY29sb3I6ICRkYXJrLWJvcmRlcjtcclxuXHJcbiAgICAgICAgICAgIC5zZWxlY3Rvci1sYWJlbCB7XHJcbiAgICAgICAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtcHJpbWFyeTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC50ZW1wbGF0ZS1saXN0IHtcclxuICAgICAgICAgICAgLnRlbXBsYXRlLWl0ZW0ge1xyXG4gICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRkYXJrLWJnLXNlY29uZGFyeTtcclxuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICRkYXJrLWJvcmRlcjtcclxuXHJcbiAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgLnRlbXBsYXRlLWluZm8ge1xyXG4gICAgICAgICAgICAgICAgLml0ZW0tbmFtZSB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAkZGFyay10ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLml0ZW0tY29kZSxcclxuICAgICAgICAgICAgICAgIC5pdGVtLXN0YXR1cyxcclxuICAgICAgICAgICAgICAgIC5pdGVtLXR5cGUge1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJGRhcmstdGV4dC1zZWNvbmRhcnk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAubm8tdGVtcGxhdGVzIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkZGFyay1iZy1zZWNvbmRhcnk7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkZGFyay1ib3JkZXI7XHJcbiAgICAgICAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY29uZmlybWF0aW9uLWFyZWEge1xyXG4gICAgICAgICAgLnNlbGVjdGVkLXRlbXBsYXRlcy1kZXRhaWxzIHtcclxuICAgICAgICAgICAgLnRlbXBsYXRlLWRldGFpbC1zZWN0aW9uIHtcclxuICAgICAgICAgICAgICAudGVtcGxhdGUtZGV0YWlsLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgLmRldGFpbC1pdGVtcy1saXN0IHtcclxuICAgICAgICAgICAgICAgICAgLmRldGFpbC1pdGVtIHtcclxuICAgICAgICAgICAgICAgICAgICAuZGV0YWlsLWluZm8ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgLmRldGFpbC1wYXJ0IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtcHJpbWFyeTtcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAuZGV0YWlsLWxvY2F0aW9uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIC5kZXRhaWwtcHJpY2luZyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5wcmljaW5nLWluZm8ge1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAucHJpY2UtbGFiZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLnVuaXQtbGFiZWwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtc2Vjb25kYXJ5O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5wcmljZS12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAkc3VjY2Vzcy1saWdodDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAudW5pdC12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAkaW5mby1saWdodDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuc3BhY2UtdGVtcGxhdGUtZm9vdGVyIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGRhcmstYmctc2Vjb25kYXJ5O1xyXG4gICAgICBib3JkZXItdG9wLWNvbG9yOiAkZGFyay1ib3JkZXI7XHJcblxyXG4gICAgICAucHJvZ3Jlc3MtaW5mbyB7XHJcbiAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiIsIi8qKlxyXG4gKiDntbHkuIDoibLlvanns7vntbEgLSDph5HoibLkuLvpoYxcclxuICog5Z+65pa85Li76Imy57O7ICNCOEE2NzYg6Kit6KiI55qE5a6M5pW06Imy5b2p6auU57O7XHJcbiAqL1xyXG5cclxuLy8gPT09PT0g5Li76KaB5ZOB54mM6Imy5b2pID09PT09XHJcbiRwcmltYXJ5LWdvbGQtbGlnaHQ6ICNCOEE2NzY7ICAgICAgLy8g5Li76KaB6YeR6ImyIC0g5re66ImyXHJcbiRwcmltYXJ5LWdvbGQtYmFzZTogI0FFOUI2NjsgICAgICAgLy8g5Li76KaB6YeR6ImyIC0g5Z+656SO6ImyXHJcbiRwcmltYXJ5LWdvbGQtZGFyazogI0E2OTY2MDsgICAgICAgLy8g5Li76KaB6YeR6ImyIC0g5rex6ImyXHJcbiRwcmltYXJ5LWdvbGQtZGFya2VyOiAjOUI4QTVBOyAgICAgLy8g5Li76KaB6YeR6ImyIC0g5pu05rexXHJcbiRwcmltYXJ5LWdvbGQtaG92ZXI6ICNDNEIzODI7ICAgICAgLy8g5oe45YGc54uA5oWLXHJcbiRwcmltYXJ5LWdvbGQtYWN0aXZlOiAjQTg5NjYwOyAgICAgLy8g5rS75YuV54uA5oWLXHJcbiRwcmltYXJ5LWdvbGQtZGlzYWJsZWQ6ICNENEM4QTg7ICAgLy8g56aB55So54uA5oWLXHJcblxyXG4vLyA9PT09PSDovJTliqnph5HoibLoqr/oibLmnb8gPT09PT1cclxuJGdvbGQtNTA6ICNGRUZDRjg7ICAgICAgICAgICAgICAgICAvLyDmpbXmt6Hph5HoibLog4zmma9cclxuJGdvbGQtMTAwOiAjRjhGNkYwOyAgICAgICAgICAgICAgICAvLyDmt6Hph5HoibLog4zmma9cclxuJGdvbGQtMjAwOiAjRjBFREU1OyAgICAgICAgICAgICAgICAvLyDmt7rph5HoibLog4zmma9cclxuJGdvbGQtMzAwOiAjRThFMkQ1OyAgICAgICAgICAgICAgICAvLyDkuK3mt7rph5HoibJcclxuJGdvbGQtNDAwOiAjRDRDOEE4OyAgICAgICAgICAgICAgICAvLyDkuK3ph5HoibJcclxuJGdvbGQtNTAwOiAjQjhBNjc2OyAgICAgICAgICAgICAgICAvLyDkuLvph5HoibJcclxuJGdvbGQtNjAwOiAjQUU5QjY2OyAgICAgICAgICAgICAgICAvLyDmt7Hph5HoibJcclxuJGdvbGQtNzAwOiAjOUI4QTVBOyAgICAgICAgICAgICAgICAvLyDmm7Tmt7Hph5HoibJcclxuJGdvbGQtODAwOiAjOEE3QTRGOyAgICAgICAgICAgICAgICAvLyDmmpfph5HoibJcclxuJGdvbGQtOTAwOiAjNkI1RjNFOyAgICAgICAgICAgICAgICAvLyDmnIDmt7Hph5HoibJcclxuXHJcbi8vID09PT09IOa8uOiuiuiJsuW9qSA9PT09PVxyXG4kZ3JhZGllbnQtcHJpbWFyeTogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHByaW1hcnktZ29sZC1saWdodCAwJSwgJHByaW1hcnktZ29sZC1kYXJrIDEwMCUpO1xyXG4kZ3JhZGllbnQtcHJpbWFyeS1ob3ZlcjogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHByaW1hcnktZ29sZC1ob3ZlciAwJSwgJHByaW1hcnktZ29sZC1hY3RpdmUgMTAwJSk7XHJcbiRncmFkaWVudC1wcmltYXJ5LWxpZ2h0OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAkZ29sZC0yMDAgMCUsICRnb2xkLTMwMCAxMDAlKTtcclxuJGdyYWRpZW50LWJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sICRnb2xkLTUwIDAlLCAkZ29sZC0xMDAgMTAwJSk7XHJcblxyXG4vLyA9PT09PSDmloflrZfoibLlvakgPT09PT1cclxuJHRleHQtcHJpbWFyeTogIzJDM0U1MDsgICAgICAgICAgICAvLyDkuLvopoHmloflrZdcclxuJHRleHQtc2Vjb25kYXJ5OiAjNUE1QTVBOyAgICAgICAgICAvLyDmrKHopoHmloflrZdcclxuJHRleHQtdGVydGlhcnk6ICM2Qzc1N0Q7ICAgICAgICAgICAvLyDovJTliqnmloflrZdcclxuJHRleHQtbXV0ZWQ6ICNBREI1QkQ7ICAgICAgICAgICAgICAvLyDpnZzpn7PmloflrZdcclxuJHRleHQtZGlzYWJsZWQ6ICNDRUQ0REE7ICAgICAgICAgICAvLyDnpoHnlKjmloflrZdcclxuJHRleHQtbGlnaHQ6ICNGRkZGRkY7ICAgICAgICAgICAgICAvLyDmt7roibLmloflrZdcclxuJHRleHQtZGFyazogIzIxMjUyOTsgICAgICAgICAgICAgICAvLyDmt7HoibLmloflrZdcclxuXHJcbi8vID09PT09IOiDjOaZr+iJsuW9qSA9PT09PVxyXG4kYmctcHJpbWFyeTogI0ZGRkZGRjsgICAgICAgICAgICAgIC8vIOS4u+imgeiDjOaZr1xyXG4kYmctc2Vjb25kYXJ5OiAjRjhGOUZBOyAgICAgICAgICAgIC8vIOasoeimgeiDjOaZr1xyXG4kYmctdGVydGlhcnk6ICNGNUY1RjU7ICAgICAgICAgICAgIC8vIOesrOS4ieiDjOaZr1xyXG4kYmctY3JlYW06ICNGRUZDRjg7ICAgICAgICAgICAgICAgIC8vIOWltuayueiJsuiDjOaZr1xyXG4kYmctbGlnaHQtZ29sZDogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjAzKTsgLy8g5qW15reh6YeR6Imy6IOM5pmvXHJcbiRiZy1ob3ZlcjogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjA1KTsgICAgICAvLyDmh7jlgZzog4zmma9cclxuJGJnLXNlbGVjdGVkOiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMTUpOyAgIC8vIOmBuOS4reiDjOaZr1xyXG5cclxuLy8gPT09PT0g6YKK5qGG6Imy5b2pID09PT09XHJcbiRib3JkZXItbGlnaHQ6ICNFOUVDRUY7ICAgICAgICAgICAgLy8g5re66Imy6YKK5qGGXHJcbiRib3JkZXItbWVkaXVtOiAjQ0RDRENEOyAgICAgICAgICAgLy8g5Lit562J6YKK5qGGXHJcbiRib3JkZXItZGFyazogI0FEQjVCRDsgICAgICAgICAgICAgLy8g5rex6Imy6YKK5qGGXHJcbiRib3JkZXItcHJpbWFyeTogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjMpOyAvLyDkuLvoibLpgormoYZcclxuJGJvcmRlci1mb2N1czogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjUpOyAgIC8vIOeEpum7numCiuahhlxyXG5cclxuLy8gPT09PT0g54uA5oWL6Imy5b2pID09PT09XHJcbi8vIOaIkOWKn+eLgOaFi1xyXG4kc3VjY2Vzcy1saWdodDogI0Q0RUREQTtcclxuJHN1Y2Nlc3MtYmFzZTogIzI4QTc0NTtcclxuJHN1Y2Nlc3MtZGFyazogIzFFN0UzNDtcclxuJHN1Y2Nlc3MtZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNFOEY1RTggMCUsICNENEVEREEgMTAwJSk7XHJcblxyXG4vLyDorablkYrni4DmhYtcclxuJHdhcm5pbmctbGlnaHQ6ICNGRkYzQ0Q7XHJcbiR3YXJuaW5nLWJhc2U6ICNGRkMxMDc7XHJcbiR3YXJuaW5nLWRhcms6ICNFMEE4MDA7XHJcbiR3YXJuaW5nLWdyYWRpZW50OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjRkZGOEUxIDAlLCAjRkZGM0NEIDEwMCUpO1xyXG5cclxuLy8g6Yyv6Kqk54uA5oWLXHJcbiRlcnJvci1saWdodDogI0Y4RDdEQTtcclxuJGVycm9yLWJhc2U6ICNEQzM1NDU7XHJcbiRlcnJvci1kYXJrOiAjQzgyMzMzO1xyXG4kZXJyb3ItZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNGRkVCRUUgMCUsICNGOEQ3REEgMTAwJSk7XHJcblxyXG4vLyDos4foqIrni4DmhYtcclxuJGluZm8tbGlnaHQ6ICNEMUVDRjE7XHJcbiRpbmZvLWJhc2U6ICMxN0EyQjg7XHJcbiRpbmZvLWRhcms6ICMxMzg0OTY7XHJcbiRpbmZvLWdyYWRpZW50OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjRTNGMkZEIDAlLCAjRDFFQ0YxIDEwMCUpO1xyXG5cclxuLy8gPT09PT0g6Zmw5b2x57O757WxID09PT09XHJcbiRzaGFkb3ctc206IDAgMXB4IDNweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMSk7XHJcbiRzaGFkb3ctbWQ6IDAgMnB4IDhweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMTUpO1xyXG4kc2hhZG93LWxnOiAwIDRweCAxMnB4IHJnYmEoMTg0LCAxNjYsIDExOCwgMC4yKTtcclxuJHNoYWRvdy14bDogMCA4cHggMjRweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMjUpO1xyXG5cclxuLy8g54m55q6K6Zmw5b2xXHJcbiRzaGFkb3ctZm9jdXM6IDAgMCAwIDAuMnJlbSByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMjUpO1xyXG4kc2hhZG93LWluc2V0OiBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSwgaW5zZXQgMCAtMXB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG5cclxuLy8gPT09PT0g5YuV55Wr5ZKM6YGO5rihID09PT09XHJcbiR0cmFuc2l0aW9uLWZhc3Q6IDAuMTVzIGVhc2U7XHJcbiR0cmFuc2l0aW9uLW5vcm1hbDogMC4zcyBlYXNlO1xyXG4kdHJhbnNpdGlvbi1zbG93OiAwLjVzIGVhc2U7XHJcbiR0cmFuc2l0aW9uLWJvdW5jZTogMC4zcyBjdWJpYy1iZXppZXIoMC42OCwgLTAuNTUsIDAuMjY1LCAxLjU1KTtcclxuXHJcbi8vID09PT09IE5lYnVsYXIg5Li76aGM6Imy5b2p5pig5bCEID09PT09XHJcbi8vIOS4u+imgeiJsuW9qVxyXG4kbmItcHJpbWFyeTogJHByaW1hcnktZ29sZC1saWdodDtcclxuJG5iLXByaW1hcnktbGlnaHQ6ICRwcmltYXJ5LWdvbGQtaG92ZXI7XHJcbiRuYi1wcmltYXJ5LWRhcms6ICRwcmltYXJ5LWdvbGQtZGFyaztcclxuXHJcbi8vIOasoeimgeiJsuW9qVxyXG4kbmItc3VjY2VzczogJHN1Y2Nlc3MtYmFzZTtcclxuJG5iLXN1Y2Nlc3MtbGlnaHQ6ICRzdWNjZXNzLWxpZ2h0O1xyXG4kbmItc3VjY2Vzcy1kYXJrOiAkc3VjY2Vzcy1kYXJrO1xyXG5cclxuJG5iLXdhcm5pbmc6ICR3YXJuaW5nLWJhc2U7XHJcbiRuYi13YXJuaW5nLWxpZ2h0OiAkd2FybmluZy1saWdodDtcclxuJG5iLXdhcm5pbmctZGFyazogJHdhcm5pbmctZGFyaztcclxuXHJcbiRuYi1kYW5nZXI6ICRlcnJvci1iYXNlO1xyXG4kbmItZGFuZ2VyLWxpZ2h0OiAkZXJyb3ItbGlnaHQ7XHJcbiRuYi1kYW5nZXItZGFyazogJGVycm9yLWRhcms7XHJcblxyXG4kbmItaW5mbzogJGluZm8tYmFzZTtcclxuJG5iLWluZm8tbGlnaHQ6ICRpbmZvLWxpZ2h0O1xyXG4kbmItaW5mby1kYXJrOiAkaW5mby1kYXJrO1xyXG5cclxuLy8g6IOM5pmv5ZKM5paH5a2XXHJcbiRuYi1iZy1wcmltYXJ5OiAkYmctcHJpbWFyeTtcclxuJG5iLWJnLXNlY29uZGFyeTogJGJnLXNlY29uZGFyeTtcclxuJG5iLXRleHQtcHJpbWFyeTogJHRleHQtcHJpbWFyeTtcclxuJG5iLXRleHQtc2Vjb25kYXJ5OiAkdGV4dC1zZWNvbmRhcnk7XHJcbiRuYi10ZXh0LWhpbnQ6ICR0ZXh0LW11dGVkO1xyXG5cclxuLy8g6YKK5qGGXHJcbiRuYi1ib3JkZXItYmFzaWM6ICRib3JkZXItbGlnaHQ7XHJcbiRuYi1ib3JkZXItYWx0ZXJuYXRpdmU6ICRib3JkZXItbWVkaXVtO1xyXG5cclxuLy8gPT09PT0g54m55q6K55So6YCU6Imy5b2pID09PT09XHJcbi8vIFJhZGlvIEJ1dHRvbiDlsIjnlKhcclxuJHJhZGlvLWJnLWhvdmVyOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IGNlbnRlciwgcmdiYSgxODQsIDE2NiwgMTE4LCAwLjEpIDAlLCB0cmFuc3BhcmVudCA3MCUpO1xyXG4kcmFkaW8tYmctc2VsZWN0ZWQ6ICRncmFkaWVudC1wcmltYXJ5O1xyXG4kcmFkaW8tYmctc2VsZWN0ZWQtaG92ZXI6ICRncmFkaWVudC1wcmltYXJ5LWhvdmVyO1xyXG4kcmFkaW8taW5uZXItZG90OiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCAkdGV4dC1saWdodCAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpIDEwMCUpO1xyXG5cclxuLy8g6KGo5qC85bCI55SoXHJcbiR0YWJsZS1oZWFkZXItYmc6ICRncmFkaWVudC1wcmltYXJ5LWxpZ2h0O1xyXG4kdGFibGUtcm93LWhvdmVyOiAkYmctaG92ZXI7XHJcbiR0YWJsZS1yb3ctc2VsZWN0ZWQ6ICRiZy1zZWxlY3RlZDtcclxuJHRhYmxlLWJvcmRlcjogJGJvcmRlci1wcmltYXJ5O1xyXG5cclxuLy8g5Y2h54mH5bCI55SoXHJcbiRjYXJkLWJnOiAkYmctcHJpbWFyeTtcclxuJGNhcmQtaGVhZGVyLWJnOiAkZ3JhZGllbnQtcHJpbWFyeTtcclxuJGNhcmQtYm9yZGVyOiAkYm9yZGVyLXByaW1hcnk7XHJcbiRjYXJkLXNoYWRvdzogJHNoYWRvdy1tZDtcclxuXHJcbi8vIOaMiemIleWwiOeUqFxyXG4kYnRuLXByaW1hcnktYmc6ICRncmFkaWVudC1wcmltYXJ5O1xyXG4kYnRuLXByaW1hcnktaG92ZXI6ICRncmFkaWVudC1wcmltYXJ5LWhvdmVyO1xyXG4kYnRuLXNlY29uZGFyeS1iZzogJGJnLXNlY29uZGFyeTtcclxuJGJ0bi1zZWNvbmRhcnktaG92ZXI6ICRiZy1ob3ZlcjtcclxuXHJcbi8vID09PT09IOmfv+aHieW8j+aWt+m7nuiJsuW9qeiqv+aVtCA9PT09PVxyXG4vLyDlnKjlsI/onqLluZXkuIrkvb/nlKjmm7Tmn5TlkoznmoToibLlvalcclxuJG1vYmlsZS1wcmltYXJ5OiBsaWdodGVuKCRwcmltYXJ5LWdvbGQtbGlnaHQsIDUlKTtcclxuJG1vYmlsZS1zaGFkb3c6IHJnYmEoMTg0LCAxNjYsIDExOCwgMC4wOCk7XHJcblxyXG4vLyA9PT09PSDmt7HoibLkuLvpoYzmlK/mj7QgPT09PT1cclxuJGRhcmstYmctcHJpbWFyeTogIzFBMUExQTtcclxuJGRhcmstYmctc2Vjb25kYXJ5OiAjMkQyRDJEO1xyXG4kZGFyay10ZXh0LXByaW1hcnk6ICNGRkZGRkY7XHJcbiRkYXJrLXRleHQtc2Vjb25kYXJ5OiAjQ0NDQ0NDO1xyXG4kZGFyay1ib3JkZXI6ICM0MDQwNDA7XHJcblxyXG4vLyA9PT09PSDovJTliqnlh73mlbjoibLlvakgPT09PT1cclxuLy8g6YCP5piO5bqm6K6K5YyWXHJcbkBmdW5jdGlvbiBhbHBoYS1nb2xkKCRhbHBoYSkge1xyXG4gIEByZXR1cm4gcmdiYSgxODQsIDE2NiwgMTE4LCAkYWxwaGEpO1xyXG59XHJcblxyXG4vLyDkuq7luqboqr/mlbRcclxuQGZ1bmN0aW9uIGxpZ2h0ZW4tZ29sZCgkYW1vdW50KSB7XHJcbiAgQHJldHVybiBsaWdodGVuKCRwcmltYXJ5LWdvbGQtbGlnaHQsICRhbW91bnQpO1xyXG59XHJcblxyXG5AZnVuY3Rpb24gZGFya2VuLWdvbGQoJGFtb3VudCkge1xyXG4gIEByZXR1cm4gZGFya2VuKCRwcmltYXJ5LWdvbGQtbGlnaHQsICRhbW91bnQpO1xyXG59XHJcblxyXG4vLyA9PT09PSDoibLlvanpqZforYkgPT09PT1cclxuLy8g56K65L+d6Imy5b2p5bCN5q+U5bqm56ym5ZCIIFdDQUcg5qiZ5rqWXHJcbiRjb250cmFzdC1yYXRpby1hYTogNC41O1xyXG4kY29udHJhc3QtcmF0aW8tYWFhOiA3O1xyXG5cclxuLy8gPT09PT0g6IiK54mI55u45a655oCnID09PT09XHJcbi8vIOS/neaMgeWQkeW+jOebuOWuueaAp+eahOiuiuaVuOaYoOWwhFxyXG4kbWFpbkNvbG9yQjogJHByaW1hcnktZ29sZC1saWdodDtcclxuJG1haW5Db2xvckc6ICRzdWNjZXNzLWJhc2U7XHJcbiRtYWluQ29sb3JHcmF5OiAkdGV4dC1zZWNvbmRhcnk7XHJcbiR0ZXh0Q29sb3I6ICR0ZXh0LXByaW1hcnk7XHJcbiRtYWluQ29sb3JZOiAkd2FybmluZy1iYXNlO1xyXG4kY29sb3ItZHJvcDogJHNoYWRvdy1tZDtcclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "TemplateGetListResponse", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "template_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r1", "ɵɵnextContext", "onTemplateItemChange", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "CTemplateId", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template", "templates", "ɵɵelement", "getCurrentTemplateTypeName", "SpaceTemplateSelectorComponent_div_12_Template_nb_select_ngModelChange_6_listener", "_r1", "selectedTemplateType", "onTemplateTypeChange", "SpaceTemplateSelectorComponent_div_12_nb_option_7_Template", "SpaceTemplateSelectorComponent_div_12_div_12_Template", "SpaceTemplateSelectorComponent_div_12_ng_template_13_Template", "ɵɵtemplateRefExtractor", "templateTypeOptions", "length", "noTemplates_r6", "detail_r7", "CLocation", "ɵɵpipeBind2", "CUnitPrice", "CUnit", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_2_Template", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_3_Template", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_Template", "i_r8", "<PERSON>art", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template", "getTemplateDetails", "item_r9", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template", "SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template", "CStatus", "noDetails_r10", "getConflictCount", "SpaceTemplateSelectorComponent_div_13_div_12_Template", "SpaceTemplateSelectorComponent_div_13_div_13_Template", "getSelectedItems", "hasConflicts", "SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener", "_r11", "previousStep", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r12", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r13", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "CTemplateType", "SpaceTemplate", "templateApplied", "currentStep", "selectedTemplateDetails", "Map", "getTemplateTypeList", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "resetSelections", "getDisplayName", "filter", "getSelectedTotalPrice", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "templateTypeName", "progressTexts", "config", "spaceId", "spaceName", "templateDetails", "totalPrice", "emit", "close", "reset", "template", "clear", "ɵɵdirectiveInject", "i1", "TemplateService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_button_click_4_listener", "SpaceTemplateSelectorComponent_div_12_Template", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_Template_button_click_19_listener", "SpaceTemplateSelectorComponent_button_21_Template", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i4", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef,\r\n  NbSelectModule\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  // 新增：模板類型選擇相關屬性\r\n  selectedTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 初始化選擇的模板類型\r\n    this.selectedTemplateType = this.CTemplateType;\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數，使用當前選擇的模板類型\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.selectedTemplateType,\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  // 新增：當模板類型選擇變更時的處理\r\n  onTemplateTypeChange() {\r\n    // 清空當前選擇\r\n    this.resetSelections();\r\n    // 重新載入對應類型的模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  // 新增：獲取當前選擇的模板類型名稱\r\n  getCurrentTemplateTypeName(): string {\r\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const progressTexts = {\r\n      1: `請選擇要套用的${templateTypeName}項目`,\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: templateTypeName,\r\n      selectedItems: this.getSelectedItems(),\r\n      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->\r\n<nb-card class=\"space-template-dialog\">\r\n  <nb-card-header class=\"space-template-header\">\r\n    <div class=\"space-template-title\">空間模板選擇器</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"space-template-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇模板</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認套用</div>\r\n    </div>\r\n\r\n    <!-- 步驟1: 選擇模板 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <div class=\"template-selection\">\r\n        <!-- 模板類型選擇器 -->\r\n        <div class=\"template-type-selector\">\r\n          <div class=\"selector-label\">\r\n            <nb-icon icon=\"options-2-outline\" class=\"mr-2\"></nb-icon>模板類型\r\n          </div>\r\n          <nb-select [(ngModel)]=\"selectedTemplateType\" (ngModelChange)=\"onTemplateTypeChange()\" placeholder=\"選擇模板類型\"\r\n            class=\"template-type-select\">\r\n            <nb-option *ngFor=\"let option of templateTypeOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇模板項目\r\n        </div>\r\n        <div class=\"template-list\">\r\n          <div *ngIf=\"templates.length > 0; else noTemplates\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <nb-checkbox [(ngModel)]=\"template.selected\" (ngModelChange)=\"onTemplateItemChange()\">\r\n                <div class=\"template-info\">\r\n                  <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                  <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n\r\n\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noTemplates>\r\n            <div class=\"no-templates\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              暫無可用的{{ getCurrentTemplateTypeName() }}項目，請稍後再試或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認套用 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認套用詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將套用 <strong>{{ getCurrentTemplateTypeName() }}</strong>：{{ getSelectedItems().length }}個模板項目\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選中的模板詳情展開 -->\r\n        <div class=\"selected-templates-details\">\r\n          <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n            <div class=\"template-detail-header\">\r\n              <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n              <div class=\"template-meta\">\r\n                <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"template-detail-content\">\r\n              <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                <div class=\"detail-items-header\">\r\n                  <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                </div>\r\n                <div class=\"detail-items-list\">\r\n                  <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\" class=\"detail-item\">\r\n                    <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                    <div class=\"detail-info\">\r\n                      <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                      <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                        位置: {{ detail.CLocation }}\r\n                      </div>\r\n                      <!-- 單價和單位資訊 -->\r\n                      <div class=\"detail-pricing\" *ngIf=\"detail.CUnitPrice || detail.CUnit\">\r\n                        <div class=\"pricing-info\">\r\n                          <span class=\"price-label\" *ngIf=\"detail.CUnitPrice\">\r\n                            <nb-icon icon=\"dollar-sign-outline\" class=\"price-icon\"></nb-icon>\r\n                            單價: <strong class=\"price-value\">NT$ {{ detail.CUnitPrice | number:'1.0-2' }}</strong>\r\n                          </span>\r\n                          <span class=\"unit-label\" *ngIf=\"detail.CUnit\">\r\n                            <nb-icon icon=\"cube-outline\" class=\"unit-icon\"></nb-icon>\r\n                            單位: <strong class=\"unit-value\">{{ detail.CUnit }}</strong>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ng-template #noDetails>\r\n                <div class=\"no-details\">\r\n                  <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n                  此模板暫無明細項目\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n          <div class=\"warning-text\">\r\n            <strong><nb-icon icon=\"alert-triangle-outline\" class=\"mr-1\"></nb-icon>衝突檢測：</strong>\r\n            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"space-template-footer\">\r\n    <div class=\"progress-info\">\r\n      <span>{{ getProgressText() }}</span>\r\n    </div>\r\n    <div class=\"step-buttons\">\r\n      <button nbButton status=\"basic\" (click)=\"close()\">取消</button>\r\n      <button *ngIf=\"currentStep > 1\" nbButton status=\"basic\" (click)=\"previousStep()\">上一步</button>\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\"\r\n        (click)=\"nextStep()\">下一步</button>\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" [disabled]=\"getSelectedItems().length === 0\"\r\n        (click)=\"applyTemplate()\">確認套用</button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,cAAc,QACT,gBAAgB;AAEvB,SAA8BC,uBAAuB,QAAuD,yBAAyB;AACrI,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;ICqBnFC,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF0CH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAC1EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;;IAUET,EADF,CAAAC,cAAA,cAA8D,sBAC0B;IAAzED,EAAA,CAAAU,gBAAA,2BAAAC,iGAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAZ,EAAA,CAAAmB,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACZ,EAAA,CAAAoB,UAAA,2BAAAT,iGAAA;MAAAX,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAAiBE,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEjFvB,EADF,CAAAC,cAAA,cAA2B,cACF;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAK3DF,EAL2D,CAAAG,YAAA,EAAM,EAGvD,EACM,EACV;;;;IARSH,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAwB,gBAAA,YAAAX,WAAA,CAAAK,QAAA,CAA+B;IAEjBlB,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAyB,iBAAA,CAAAZ,WAAA,CAAAa,aAAA,CAA4B;IAC5B1B,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,kBAAA,SAAAK,WAAA,CAAAc,WAAA,KAA8B;;;;;IAL7D3B,EAAA,CAAAC,cAAA,UAAoD;IAClDD,EAAA,CAAA4B,UAAA,IAAAC,2DAAA,kBAA8D;IAUhE7B,EAAA,CAAAG,YAAA,EAAM;;;;IAVsBH,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAS,SAAA,CAAY;;;;;IAYtC9B,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA+B,SAAA,kBAAoD;IACpD/B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oCAAAa,MAAA,CAAAW,0BAAA,4FACF;;;;;;IA/BFhC,EAJN,CAAAC,cAAA,cAAoD,cAClB,cAEM,cACN;IAC1BD,EAAA,CAAA+B,SAAA,kBAAyD;IAAA/B,EAAA,CAAAE,MAAA,gCAC3D;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,oBAC+B;IADpBD,EAAA,CAAAU,gBAAA,2BAAAuB,kFAAArB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAoB,GAAA;MAAA,MAAAb,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAiB,kBAAA,CAAAI,MAAA,CAAAc,oBAAA,EAAAvB,MAAA,MAAAS,MAAA,CAAAc,oBAAA,GAAAvB,MAAA;MAAA,OAAAZ,EAAA,CAAAmB,WAAA,CAAAP,MAAA;IAAA,EAAkC;IAACZ,EAAA,CAAAoB,UAAA,2BAAAa,kFAAA;MAAAjC,EAAA,CAAAc,aAAA,CAAAoB,GAAA;MAAA,MAAAb,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAAiBE,MAAA,CAAAe,oBAAA,EAAsB;IAAA,EAAC;IAEpFpC,EAAA,CAAA4B,UAAA,IAAAS,0DAAA,wBAA6E;IAIjFrC,EADE,CAAAG,YAAA,EAAY,EACR;IAENH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA+B,SAAA,kBAAsD;IAAA/B,EAAA,CAAAE,MAAA,6CACxD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAazBD,EAZA,CAAA4B,UAAA,KAAAU,qDAAA,kBAAoD,KAAAC,6DAAA,gCAAAvC,EAAA,CAAAwC,sBAAA,CAY1B;IAQhCxC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAhCWH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwB,gBAAA,YAAAH,MAAA,CAAAc,oBAAA,CAAkC;IAEbnC,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAoB,mBAAA,CAAsB;IAUhDzC,EAAA,CAAAO,SAAA,GAA4B;IAAAP,EAA5B,CAAAI,UAAA,SAAAiB,MAAA,CAAAS,SAAA,CAAAY,MAAA,KAA4B,aAAAC,cAAA,CAAgB;;;;;IAwDtC3C,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAoC,SAAA,CAAAC,SAAA,MACF;;;;;IAII7C,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAA+B,SAAA,kBAAiE;IACjE/B,EAAA,CAAAE,MAAA,sBAAI;IAAAF,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA4C;;IAC9EF,EAD8E,CAAAG,YAAA,EAAS,EAChF;;;;IAD2BH,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAQ,kBAAA,SAAAR,EAAA,CAAA8C,WAAA,OAAAF,SAAA,CAAAG,UAAA,eAA4C;;;;;IAE9E/C,EAAA,CAAAC,cAAA,eAA8C;IAC5CD,EAAA,CAAA+B,SAAA,kBAAyD;IACzD/B,EAAA,CAAAE,MAAA,sBAAI;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IACnDF,EADmD,CAAAG,YAAA,EAAS,EACrD;;;;IAD0BH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAyB,iBAAA,CAAAmB,SAAA,CAAAI,KAAA,CAAkB;;;;;IAPrDhD,EADF,CAAAC,cAAA,cAAsE,cAC1C;IAKxBD,EAJA,CAAA4B,UAAA,IAAAqB,+EAAA,mBAAoD,IAAAC,+EAAA,mBAIN;IAKlDlD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IATyBH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAwC,SAAA,CAAAG,UAAA,CAAuB;IAIxB/C,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAwC,SAAA,CAAAI,KAAA,CAAkB;;;;;IAblDhD,EADF,CAAAC,cAAA,cAAqG,cACzE;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEzCH,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKjDH,EAJA,CAAA4B,UAAA,IAAAuB,wEAAA,kBAAsD,IAAAC,wEAAA,kBAIgB;IAa1EpD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IApBsBH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAyB,iBAAA,CAAA4B,IAAA,KAAW;IAEVrD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAyB,iBAAA,CAAAmB,SAAA,CAAAU,KAAA,CAAkB;IACbtD,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAwC,SAAA,CAAAC,SAAA,CAAsB;IAIvB7C,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAwC,SAAA,CAAAG,UAAA,IAAAH,SAAA,CAAAI,KAAA,CAAuC;;;;;IAXxEhD,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IACzFF,EADyF,CAAAG,YAAA,EAAO,EAC1F;IACNH,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA4B,UAAA,IAAA2B,kEAAA,kBAAqG;IAuBzGvD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IA1ByBH,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAQ,kBAAA,kBAAAa,MAAA,CAAAmC,kBAAA,CAAAC,OAAA,CAAA9B,WAAA,EAAAe,MAAA,0CAA4D;IAG/D1C,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAmC,kBAAA,CAAAC,OAAA,CAAA9B,WAAA,EAA0C;;;;;IA0BpE3B,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA+B,SAAA,kBAAoD;IACpD/B,EAAA,CAAAE,MAAA,+DACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA1CRH,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErDH,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAExEF,EAFwE,CAAAG,YAAA,EAAO,EACvE,EACF;IAENH,EAAA,CAAAC,cAAA,cAAqC;IA+BnCD,EA9BA,CAAA4B,UAAA,KAAA8B,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAA3D,EAAA,CAAAwC,sBAAA,CA8B3E;IAO5BxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IA7CwBH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAyB,iBAAA,CAAAgC,OAAA,CAAA/B,aAAA,CAAwB;IAEtB1B,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,kBAAA,SAAAiD,OAAA,CAAA9B,WAAA,KAA0B;IACtB3B,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAyB,iBAAA,CAAAgC,OAAA,CAAAG,OAAA,yCAAsC;IAKhE5D,EAAA,CAAAO,SAAA,GAAwD;IAAAP,EAAxD,CAAAI,UAAA,SAAAiB,MAAA,CAAAmC,kBAAA,CAAAC,OAAA,CAAA9B,WAAA,EAAAe,MAAA,KAAwD,aAAAmB,aAAA,CAAc;;;;;IA0C9E7D,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA+B,SAAA,kBAA8D;IAAA/B,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpFH,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,yBAAAa,MAAA,CAAAyC,gBAAA,+JACF;;;;;IAlEF9D,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA+B,SAAA,kBAAgE;IAAA/B,EAAA,CAAAE,MAAA,4CAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAAAH,EAAA,CAAAE,MAAA,IACzD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA4B,UAAA,KAAAmC,qDAAA,mBAA6E;IAgD/E/D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA4B,UAAA,KAAAoC,qDAAA,kBAAqD;IAOzDhE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA/DcH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAyB,iBAAA,CAAAJ,MAAA,CAAAW,0BAAA,GAAkC;IAAShC,EAAA,CAAAO,SAAA,EACzD;IADyDP,EAAA,CAAAQ,kBAAA,WAAAa,MAAA,CAAA4C,gBAAA,GAAAvB,MAAA,oCACzD;IAKsB1C,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAA4C,gBAAA,GAAqB;IAkDvCjE,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAiB,MAAA,CAAA6C,YAAA,GAAoB;;;;;;IAgB5BlE,EAAA,CAAAC,cAAA,iBAAiF;IAAzBD,EAAA,CAAAoB,UAAA,mBAAA+C,0EAAA;MAAAnE,EAAA,CAAAc,aAAA,CAAAsD,IAAA;MAAA,MAAA/C,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAASE,MAAA,CAAAgD,YAAA,EAAc;IAAA,EAAC;IAACrE,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC7FH,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAoB,UAAA,mBAAAkD,0EAAA;MAAAtE,EAAA,CAAAc,aAAA,CAAAyD,IAAA;MAAA,MAAAlD,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAASE,MAAA,CAAAmD,QAAA,EAAU;IAAA,EAAC;IAACxE,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADuBH,EAAA,CAAAI,UAAA,cAAAiB,MAAA,CAAAoD,UAAA,GAA0B;;;;;;IAEpFzE,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAoB,UAAA,mBAAAsD,0EAAA;MAAA1E,EAAA,CAAAc,aAAA,CAAA6D,IAAA;MAAA,MAAAtD,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAASE,MAAA,CAAAuD,aAAA,EAAe;IAAA,EAAC;IAAC5E,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADmBH,EAAA,CAAAI,UAAA,aAAAiB,MAAA,CAAA4C,gBAAA,GAAAvB,MAAA,OAA4C;;;AD3G9G,OAAM,MAAOmC,8BAA8B;EAazCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAdV,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAWpF,gBAAgB,CAACqF,aAAa;IACrD,KAAAC,eAAe,GAAG,IAAI/F,YAAY,EAAuB;IAEnE,KAAAgG,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAvD,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAwD,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;IAExE;IACA,KAAApD,oBAAoB,GAAWrC,gBAAgB,CAACqF,aAAa;IAC7D,KAAA1C,mBAAmB,GAAG1C,sBAAsB,CAACyF,mBAAmB,EAAE;EAK9D;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACtD,oBAAoB,GAAG,IAAI,CAAC+C,aAAa;IAC9C;IACA,IAAI,CAACQ,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CT,aAAa,EAAE,IAAI,CAAC/C,oBAAoB;MACxCyD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfnE,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACqD,eAAe,CAACe,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAACtE,SAAS,GAAGoE,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACPpF,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACY,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDyE,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACzE,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAP,oBAAoBA,CAAA;IAClB;EAAA;EAGF;EACAa,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACoE,eAAe,EAAE;IACtB;IACA,IAAI,CAACd,oBAAoB,EAAE;EAC7B;EAEA;EACA1D,0BAA0BA,CAAA;IACxB,OAAOjC,sBAAsB,CAAC0G,cAAc,CAAC,IAAI,CAACtE,oBAAoB,CAAC;EACzE;EAEA8B,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACnC,SAAS,CAAC4E,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAACpF,QAAQ,CAAC;EACrD;EAEAyF,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAlC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACY,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACpB,gBAAgB,EAAE,CAACvB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEA8B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACY,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACuB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACvB,WAAW,EAAE;IACpB;EACF;EAEAuB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAAC5C,gBAAgB,EAAE;IAE7C4C,aAAa,CAACC,OAAO,CAACR,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAAC3E,WAAW,IAAI,CAAC,IAAI,CAAC2D,uBAAuB,CAACyB,GAAG,CAACT,IAAI,CAAC3E,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACqF,sBAAsB,CAACV,IAAI,CAAC3E,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAqF,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAClC,eAAe,CAACoC,yCAAyC,CAAC;MAC7DpB,IAAI,EAAEmB;KACP,CAAC,CAAClB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACd,uBAAuB,CAAC8B,GAAG,CAACH,UAAU,EAAEf,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACjB,uBAAuB,CAAC8B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAzD,kBAAkBA,CAACyD,UAAkB;IACnC,OAAO,IAAI,CAAC3B,uBAAuB,CAAC+B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEA5C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAiC,eAAeA,CAAA;IACb,MAAMC,gBAAgB,GAAGxH,sBAAsB,CAAC0G,cAAc,CAAC,IAAI,CAACtE,oBAAoB,CAAC;IACzF,MAAMqF,aAAa,GAAG;MACpB,CAAC,EAAE,UAAUD,gBAAgB,IAAI;MACjC,CAAC,EAAE;KACJ;IACD,OAAOC,aAAa,CAAC,IAAI,CAACnC,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAnB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACvB,MAAM,GAAG,CAAC;EAC3C;EAEAoB,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAM2C,gBAAgB,GAAGxH,sBAAsB,CAAC0G,cAAc,CAAC,IAAI,CAACtE,oBAAoB,CAAC;IACzF,MAAMsF,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAEJ,gBAAgB;MAC3BV,aAAa,EAAE,IAAI,CAAC5C,gBAAgB,EAAE;MACtC2D,eAAe,EAAE,IAAIrC,GAAG,CAAC,IAAI,CAACD,uBAAuB,CAAC;MAAE;MACxDuC,UAAU,EAAE,IAAI,CAAClB,qBAAqB;KACvC;IAED,IAAI,CAACvB,eAAe,CAAC0C,IAAI,CAACL,MAAM,CAAC;IACjC,IAAI,CAACM,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACvB,eAAe,EAAE;IACtB,IAAI,CAACxB,SAAS,CAAC+C,KAAK,EAAE;EACxB;EAEA;EACA;EAEQC,KAAKA,CAAA;IACX,IAAI,CAAC3C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACvD,SAAS,GAAG,EAAE;EACrB;EAEQ0E,eAAeA,CAAA;IACrB,IAAI,CAACnB,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACvD,SAAS,CAACgF,OAAO,CAACmB,QAAQ,IAAG;MAChCA,QAAQ,CAAC/G,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAACoE,uBAAuB,CAAC4C,KAAK,EAAE;EACtC;;;uCAvMWrD,8BAA8B,EAAA7E,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAArI,EAAA,CAAAmI,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9B1D,8BAA8B;MAAA2D,SAAA;MAAAC,MAAA;QAAAxD,WAAA;QAAAC,aAAA;MAAA;MAAAwD,OAAA;QAAAtD,eAAA;MAAA;MAAAuD,UAAA;MAAAC,QAAA,GAAA5I,EAAA,CAAA6I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAf,QAAA,WAAAgB,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxCvClJ,EAFJ,CAAAC,cAAA,iBAAuC,wBACS,aACV;UAAAD,EAAA,CAAAE,MAAA,iDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC/CH,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAoB,UAAA,mBAAAgI,gEAAA;YAAA,OAASD,GAAA,CAAApB,KAAA,EAAO;UAAA,EAAC;UACxD/H,EAAA,CAAA+B,SAAA,iBAAwC;UAE5C/B,EADE,CAAAG,YAAA,EAAS,EACM;UAKbH,EAHJ,CAAAC,cAAA,sBAA0C,aAElB,aAKjB;UAAAD,EAAA,CAAAE,MAAA,kCAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChBH,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAE,MAAA,mCAAO;UACZF,EADY,CAAAG,YAAA,EAAM,EACZ;UA6CNH,EA1CA,CAAA4B,UAAA,KAAAyH,8CAAA,mBAAoD,KAAAC,8CAAA,mBA0CA;UAwEtDtJ,EAAA,CAAAG,YAAA,EAAe;UAIXH,EAFJ,CAAAC,cAAA,0BAA8C,eACjB,YACnB;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAChC;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBAC0B;UAAlBD,EAAA,CAAAoB,UAAA,mBAAAmI,iEAAA;YAAA,OAASJ,GAAA,CAAApB,KAAA,EAAO;UAAA,EAAC;UAAC/H,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI7DH,EAHA,CAAA4B,UAAA,KAAA4H,iDAAA,qBAAiF,KAAAC,iDAAA,qBAE1D,KAAAC,iDAAA,qBAEK;UAGlC1J,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;UA9ImBH,EAAA,CAAAO,SAAA,GAIrB;UAJqBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA2J,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAA9D,WAAA,QAAA8D,GAAA,CAAA9D,WAAA,MAAA8D,GAAA,CAAA9D,WAAA,MAIrB;UACqBrF,EAAA,CAAAO,SAAA,GAIrB;UAJqBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA2J,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAA9D,WAAA,QAAA8D,GAAA,CAAA9D,WAAA,MAAA8D,GAAA,CAAA9D,WAAA,MAIrB;UAIErF,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA+I,GAAA,CAAA9D,WAAA,OAAuB;UA0CvBrF,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA+I,GAAA,CAAA9D,WAAA,OAAuB;UA4ErBrF,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAyB,iBAAA,CAAA0H,GAAA,CAAA7B,eAAA,GAAuB;UAIpBtH,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA+I,GAAA,CAAA9D,WAAA,KAAqB;UACrBrF,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA+I,GAAA,CAAA9D,WAAA,KAAqB;UAErBrF,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA+I,GAAA,CAAA9D,WAAA,OAAuB;;;qBDtHlC/F,YAAY,EAAAuK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EACZ1K,WAAW,EAAA2K,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACX5K,YAAY,EAAA8I,EAAA,CAAA+B,eAAA,EAAA/B,EAAA,CAAAgC,mBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EAAAjC,EAAA,CAAAkC,qBAAA,EACZ/K,cAAc,EAAA6I,EAAA,CAAAmC,iBAAA,EACd/K,YAAY,EAAA4I,EAAA,CAAAoC,eAAA,EACZ/K,gBAAgB,EAAA2I,EAAA,CAAAqC,mBAAA,EAChB/K,cAAc,EAAA0I,EAAA,CAAAsC,iBAAA,EAAAtC,EAAA,CAAAuC,iBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}