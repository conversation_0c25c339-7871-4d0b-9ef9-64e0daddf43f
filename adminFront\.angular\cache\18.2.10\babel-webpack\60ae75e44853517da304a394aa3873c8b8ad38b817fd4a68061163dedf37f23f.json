{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule } from '@nebular/theme';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"nb-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const template_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r2.selected, $event) || (template_r2.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const template_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r2.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r2.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r2.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u72C0\\u614B: \", template_r2.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u985E\\u578B: \", template_r2.CTemplateType, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template, 11, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u66AB\\u7121\\u53EF\\u7528\\u7684\\u6A21\\u677F\\u9805\\u76EE\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 21);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_12_div_6_Template, 2, 1, \"div\", 23)(7, SpaceTemplateSelectorComponent_div_12_ng_template_7_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noTemplates_r4 = i0.ɵɵreference(8);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templates.length > 0)(\"ngIfElse\", noTemplates_r4);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r5.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"div\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template, 2, 1, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r5.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r5.CLocation);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template, 7, 3, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h5\", 43);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"span\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 46);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 47);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template, 6, 2, \"div\", 48)(11, SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const noDetails_r8 = i0.ɵɵreference(12);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r7.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r8);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"strong\");\n    i0.ɵɵelement(3, \"nb-icon\", 63);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 34)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 35);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_13_div_12_Template, 13, 5, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_div_13_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.CTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n    // 新增：模板類型選擇相關屬性\n    this.selectedTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\n  }\n  ngOnInit() {\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.CTemplateType,\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      templateDetails: new Map(this.selectedTemplateDetails),\n      // 傳遞已載入的模板明細\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        CTemplateType: \"CTemplateType\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 16,\n      consts: [[\"noTemplates\", \"\"], [\"noDetails\", \"\"], [1, \"space-template-dialog\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [1, \"template-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [1, \"no-templates\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"no-details\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [\"icon\", \"alert-triangle-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 7)(7, \"div\", 8)(8, \"div\", 9);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_Template, 9, 2, \"div\", 10)(13, SpaceTemplateSelectorComponent_div_13_Template, 14, 3, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 11)(15, \"div\", 12)(16, \"span\");\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_19_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SpaceTemplateSelectorComponent_button_21_Template, 2, 0, \"button\", 15)(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 1, \"button\", 16)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n.space-template-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  background-color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: #ADB5BD;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #2C3E50;\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-radius: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: #FFFFFF;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  margin: 0 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: 0.3s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  color: #ADB5BD;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #AE9B66;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  transition: 0.3s ease;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  border-color: #AE9B66;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  background-color: rgba(184, 166, 118, 0.03);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #A69660;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-id[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #28A745;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%]   .detail-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  border-radius: 50%;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%] {\\n  background-color: #FFF3CD;\\n  border: 1px solid #FFC107;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-top: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%] {\\n  color: #E0A800;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  color: #FFC107;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #E9ECEF;\\n  background-color: #F8F9FA;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #ADB5BD;\\n  font-size: 0.875rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #AE9B66;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  border-color: #28A745;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1E7E34;\\n  border-color: #1E7E34;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-color: #CDCDCD;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-color: #5A5A5A;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .space-template-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-top-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "TemplateGetListResponse", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "template_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "ɵɵtextInterpolate1", "CTemplateId", "CStatus", "CTemplateType", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template", "ɵɵproperty", "templates", "ɵɵelement", "SpaceTemplateSelectorComponent_div_12_div_6_Template", "SpaceTemplateSelectorComponent_div_12_ng_template_7_Template", "ɵɵtemplateRefExtractor", "length", "noTemplates_r4", "detail_r5", "CLocation", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template", "i_r6", "<PERSON>art", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template", "getTemplateDetails", "item_r7", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template", "SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template", "noDetails_r8", "getConflictCount", "SpaceTemplateSelectorComponent_div_13_div_12_Template", "SpaceTemplateSelectorComponent_div_13_div_13_Template", "getSelectedItems", "hasConflicts", "SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener", "_r9", "previousStep", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r10", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r11", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "SpaceTemplate", "templateApplied", "currentStep", "selectedTemplateDetails", "Map", "selectedTemplateType", "templateTypeOptions", "getTemplateTypeList", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "filter", "getSelectedTotalPrice", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "progressTexts", "config", "spaceId", "spaceName", "templateDetails", "totalPrice", "emit", "close", "resetSelections", "reset", "template", "clear", "ɵɵdirectiveInject", "i1", "TemplateService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_button_click_4_listener", "SpaceTemplateSelectorComponent_div_12_Template", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_Template_button_click_19_listener", "SpaceTemplateSelectorComponent_button_21_Template", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef,\r\n  NbSelectModule\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  // 新增：模板類型選擇相關屬性\r\n  selectedTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.CTemplateType,\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->\r\n<nb-card class=\"space-template-dialog\">\r\n  <nb-card-header class=\"space-template-header\">\r\n    <div class=\"space-template-title\">空間模板選擇器</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"space-template-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇模板</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認套用</div>\r\n    </div>\r\n\r\n    <!-- 步驟1: 選擇模板 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <div class=\"template-selection\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇模板項目\r\n        </div>\r\n        <div class=\"template-list\">\r\n          <div *ngIf=\"templates.length > 0; else noTemplates\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <nb-checkbox [(ngModel)]=\"template.selected\" (ngModelChange)=\"onTemplateItemChange()\">\r\n                <div class=\"template-info\">\r\n                  <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                  <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n                  <div class=\"item-status\">\r\n                    狀態: {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </div>\r\n                  <div class=\"item-type\">\r\n                    類型: {{ template.CTemplateType }}\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noTemplates>\r\n            <div class=\"no-templates\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              暫無可用的模板項目，請稍後再試或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認套用 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認套用詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選中的模板詳情展開 -->\r\n        <div class=\"selected-templates-details\">\r\n          <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n            <div class=\"template-detail-header\">\r\n              <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n              <div class=\"template-meta\">\r\n                <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"template-detail-content\">\r\n              <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                <div class=\"detail-items-header\">\r\n                  <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                </div>\r\n                <div class=\"detail-items-list\">\r\n                  <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\" class=\"detail-item\">\r\n                    <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                    <div class=\"detail-info\">\r\n                      <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                      <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                        位置: {{ detail.CLocation }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ng-template #noDetails>\r\n                <div class=\"no-details\">\r\n                  <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n                  此模板暫無明細項目\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n          <div class=\"warning-text\">\r\n            <strong><nb-icon icon=\"alert-triangle-outline\" class=\"mr-1\"></nb-icon>衝突檢測：</strong>\r\n            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"space-template-footer\">\r\n    <div class=\"progress-info\">\r\n      <span>{{ getProgressText() }}</span>\r\n    </div>\r\n    <div class=\"step-buttons\">\r\n      <button nbButton status=\"basic\" (click)=\"close()\">取消</button>\r\n      <button *ngIf=\"currentStep > 1\" nbButton status=\"basic\" (click)=\"previousStep()\">上一步</button>\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\"\r\n        (click)=\"nextStep()\">下一步</button>\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" [disabled]=\"getSelectedItems().length === 0\"\r\n        (click)=\"applyTemplate()\">確認套用</button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,cAAc,QACT,gBAAgB;AAEvB,SAA8BC,uBAAuB,QAAuD,yBAAyB;AACrI,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;;ICoBjFC,EADF,CAAAC,cAAA,cAA8D,sBAC0B;IAAzED,EAAA,CAAAE,gBAAA,2BAAAC,gGAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACJ,EAAA,CAAAY,UAAA,2BAAAT,gGAAA;MAAAH,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAAiBE,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEjFf,EADF,CAAAC,cAAA,cAA2B,cACF;IAAAD,EAAA,CAAAgB,MAAA,GAA4B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACzDjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAgB,MAAA,GAA8B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAC3DjB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAgB,MAAA,IACF;IAGNhB,EAHM,CAAAiB,YAAA,EAAM,EACF,EACM,EACV;;;;IAZSjB,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAmB,gBAAA,YAAAd,WAAA,CAAAK,QAAA,CAA+B;IAEjBV,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAoB,iBAAA,CAAAf,WAAA,CAAAgB,aAAA,CAA4B;IAC5BrB,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAsB,kBAAA,SAAAjB,WAAA,CAAAkB,WAAA,KAA8B;IAEnDvB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAmB,OAAA,8CACF;IAEExB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAoB,aAAA,MACF;;;;;IAXRzB,EAAA,CAAAC,cAAA,UAAoD;IAClDD,EAAA,CAAA0B,UAAA,IAAAC,0DAAA,mBAA8D;IAchE3B,EAAA,CAAAiB,YAAA,EAAM;;;;IAdsBjB,EAAA,CAAAkB,SAAA,EAAY;IAAZlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAgB,SAAA,CAAY;;;;;IAgBtC7B,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,uIACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IAxBVjB,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA8B,SAAA,kBAAsD;IAAA9B,EAAA,CAAAgB,MAAA,4CACxD;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA2B;IAiBzBD,EAhBA,CAAA0B,UAAA,IAAAK,oDAAA,kBAAoD,IAAAC,4DAAA,gCAAAhC,EAAA,CAAAiC,sBAAA,CAgB1B;IAQhCjC,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;;IAxBMjB,EAAA,CAAAkB,SAAA,GAA4B;IAAAlB,EAA5B,CAAA4B,UAAA,SAAAf,MAAA,CAAAgB,SAAA,CAAAK,MAAA,KAA4B,aAAAC,cAAA,CAAgB;;;;;IA4DtCnC,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;IADJjB,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAc,SAAA,CAAAC,SAAA,MACF;;;;;IALFrC,EADF,CAAAC,cAAA,cAAqG,cACzE;IAAAD,EAAA,CAAAgB,MAAA,GAAW;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAEzCjB,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACjDjB,EAAA,CAAA0B,UAAA,IAAAY,wEAAA,kBAAsD;IAI1DtC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAPsBjB,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAAoB,iBAAA,CAAAmB,IAAA,KAAW;IAEVvC,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAoB,iBAAA,CAAAgB,SAAA,CAAAI,KAAA,CAAkB;IACbxC,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAA4B,UAAA,SAAAQ,SAAA,CAAAC,SAAA,CAAsB;;;;;IAPxDrC,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAgB,MAAA,GAA4D;IACzFhB,EADyF,CAAAiB,YAAA,EAAO,EAC1F;IACNjB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA0B,UAAA,IAAAe,kEAAA,kBAAqG;IAUzGzC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAbyBjB,EAAA,CAAAkB,SAAA,GAA4D;IAA5DlB,EAAA,CAAAsB,kBAAA,kBAAAT,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAAAW,MAAA,0CAA4D;IAG/DlC,EAAA,CAAAkB,SAAA,GAA0C;IAA1ClB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAA0C;;;;;IAapEvB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,+DACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA7BRjB,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IAErDjB,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAgB,MAAA,GAA0B;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;IAC3DjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAgB,MAAA,GAAsC;IAExEhB,EAFwE,CAAAiB,YAAA,EAAO,EACvE,EACF;IAENjB,EAAA,CAAAC,cAAA,cAAqC;IAkBnCD,EAjBA,CAAA0B,UAAA,KAAAkB,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAA7C,EAAA,CAAAiC,sBAAA,CAiB3E;IAO5BjC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;;IAhCwBjB,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAoB,iBAAA,CAAAuB,OAAA,CAAAtB,aAAA,CAAwB;IAEtBrB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAsB,kBAAA,SAAAqB,OAAA,CAAApB,WAAA,KAA0B;IACtBvB,EAAA,CAAAkB,SAAA,GAAsC;IAAtClB,EAAA,CAAAoB,iBAAA,CAAAuB,OAAA,CAAAnB,OAAA,yCAAsC;IAKhExB,EAAA,CAAAkB,SAAA,GAAwD;IAAAlB,EAAxD,CAAA4B,UAAA,SAAAf,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAAAW,MAAA,KAAwD,aAAAY,YAAA,CAAc;;;;;IA6B9E9C,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA8B,SAAA,kBAA8D;IAAA9B,EAAA,CAAAgB,MAAA,qCAAK;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACpFjB,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,yBAAAT,MAAA,CAAAkC,gBAAA,+JACF;;;;;IArDF/C,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA8B,SAAA,kBAAgE;IAAA9B,EAAA,CAAAgB,MAAA,4CAClE;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAgB,MAAA,2BAAI;IAAAhB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAgB,MAAA,IAC3B;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;IAGNjB,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA0B,UAAA,KAAAsB,qDAAA,mBAA6E;IAmC/EhD,EAAA,CAAAiB,YAAA,EAAM;IAENjB,EAAA,CAAA0B,UAAA,KAAAuB,qDAAA,kBAAqD;IAOzDjD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAlD2BjB,EAAA,CAAAkB,SAAA,IAC3B;IAD2BlB,EAAA,CAAAsB,kBAAA,WAAAT,MAAA,CAAAqC,gBAAA,GAAAhB,MAAA,oCAC3B;IAKsBlC,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAqC,gBAAA,GAAqB;IAqCvClD,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA4B,UAAA,SAAAf,MAAA,CAAAsC,YAAA,GAAoB;;;;;;IAgB5BnD,EAAA,CAAAC,cAAA,iBAAiF;IAAzBD,EAAA,CAAAY,UAAA,mBAAAwC,0EAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA+C,GAAA;MAAA,MAAAxC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IAACtD,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC7FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAA2C,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,IAAA;MAAA,MAAA3C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IAACzD,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADuBjB,EAAA,CAAA4B,UAAA,cAAAf,MAAA,CAAA6C,UAAA,GAA0B;;;;;;IAEpF1D,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAA+C,0EAAA;MAAA3D,EAAA,CAAAM,aAAA,CAAAsD,IAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAgD,aAAA,EAAe;IAAA,EAAC;IAAC7D,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADmBjB,EAAA,CAAA4B,UAAA,aAAAf,MAAA,CAAAqC,gBAAA,GAAAhB,MAAA,OAA4C;;;ADrF9G,OAAM,MAAO4B,8BAA8B;EAazCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAdV,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAzC,aAAa,GAAW3B,gBAAgB,CAACqE,aAAa;IACrD,KAAAC,eAAe,GAAG,IAAI/E,YAAY,EAAuB;IAEnE,KAAAgF,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAxC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAyC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;IAExE;IACA,KAAAC,oBAAoB,GAAW1E,gBAAgB,CAACqE,aAAa;IAC7D,KAAAM,mBAAmB,GAAG1E,sBAAsB,CAAC2E,mBAAmB,EAAE;EAK9D;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CpD,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCqD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACf1D,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC2C,eAAe,CAACgB,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAACzD,SAAS,GAAGuD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACP9E,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACmB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACD4D,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAAC5D,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAd,oBAAoBA,CAAA;IAClB;EAAA;EAGFmC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACrB,SAAS,CAAC6D,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC9E,QAAQ,CAAC;EACrD;EAEAiF,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAjC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACW,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACnB,gBAAgB,EAAE,CAAChB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAuB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACuB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACvB,WAAW,EAAE;IACpB;EACF;EAEAuB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAAC3C,gBAAgB,EAAE;IAE7C2C,aAAa,CAACC,OAAO,CAACN,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAACjE,WAAW,IAAI,CAAC,IAAI,CAAC+C,uBAAuB,CAACyB,GAAG,CAACP,IAAI,CAACjE,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACyE,sBAAsB,CAACR,IAAI,CAACjE,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAyE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAACjC,eAAe,CAACmC,yCAAyC,CAAC;MAC7DlB,IAAI,EAAEiB;KACP,CAAC,CAAChB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAAChB,uBAAuB,CAAC8B,GAAG,CAACH,UAAU,EAAEb,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACnB,uBAAuB,CAAC8B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAvD,kBAAkBA,CAACuD,UAAkB;IACnC,OAAO,IAAI,CAAC3B,uBAAuB,CAAC+B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEA3C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAiC,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAClC,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAlB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAAChB,MAAM,GAAG,CAAC;EAC3C;EAEAa,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAM2C,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAE,MAAM;MACjBb,aAAa,EAAE,IAAI,CAAC3C,gBAAgB,EAAE;MACtCyD,eAAe,EAAE,IAAIpC,GAAG,CAAC,IAAI,CAACD,uBAAuB,CAAC;MAAE;MACxDsC,UAAU,EAAE,IAAI,CAACjB,qBAAqB;KACvC;IAED,IAAI,CAACvB,eAAe,CAACyC,IAAI,CAACL,MAAM,CAAC;IACjC,IAAI,CAACM,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAAC9C,SAAS,CAAC6C,KAAK,EAAE;EACxB;EAEA;EACA;EAEQE,KAAKA,CAAA;IACX,IAAI,CAAC3C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACxC,SAAS,GAAG,EAAE;EACrB;EAEQkF,eAAeA,CAAA;IACrB,IAAI,CAAC1C,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACxC,SAAS,CAACiE,OAAO,CAACmB,QAAQ,IAAG;MAChCA,QAAQ,CAACvG,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAAC4D,uBAAuB,CAAC4C,KAAK,EAAE;EACtC;;;uCAtLWpD,8BAA8B,EAAA9D,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAArH,EAAA,CAAAmH,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9BzD,8BAA8B;MAAA0D,SAAA;MAAAC,MAAA;QAAAvD,WAAA;QAAAzC,aAAA;MAAA;MAAAiG,OAAA;QAAAtD,eAAA;MAAA;MAAAuD,UAAA;MAAAC,QAAA,GAAA5H,EAAA,CAAA6H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAf,QAAA,WAAAgB,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxCvClI,EAFJ,CAAAC,cAAA,iBAAuC,wBACS,aACV;UAAAD,EAAA,CAAAgB,MAAA,iDAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAY,UAAA,mBAAAwH,gEAAA;YAAA,OAASD,GAAA,CAAArB,KAAA,EAAO;UAAA,EAAC;UACxD9G,EAAA,CAAA8B,SAAA,iBAAwC;UAE5C9B,EADE,CAAAiB,YAAA,EAAS,EACM;UAKbjB,EAHJ,CAAAC,cAAA,sBAA0C,aAElB,aAKjB;UAAAD,EAAA,CAAAgB,MAAA,kCAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAgB,MAAA,mCAAO;UACZhB,EADY,CAAAiB,YAAA,EAAM,EACZ;UAoCNjB,EAjCA,CAAA0B,UAAA,KAAA2G,8CAAA,kBAAoD,KAAAC,8CAAA,mBAiCA;UA2DtDtI,EAAA,CAAAiB,YAAA,EAAe;UAIXjB,EAFJ,CAAAC,cAAA,0BAA8C,eACjB,YACnB;UAAAD,EAAA,CAAAgB,MAAA,IAAuB;UAC/BhB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC0B;UAAlBD,EAAA,CAAAY,UAAA,mBAAA2H,iEAAA;YAAA,OAASJ,GAAA,CAAArB,KAAA,EAAO;UAAA,EAAC;UAAC9G,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAI7DjB,EAHA,CAAA0B,UAAA,KAAA8G,iDAAA,qBAAiF,KAAAC,iDAAA,qBAE1D,KAAAC,iDAAA,qBAEK;UAGlC1I,EAFI,CAAAiB,YAAA,EAAM,EACS,EACT;;;UAxHmBjB,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA2I,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAA9D,WAAA,QAAA8D,GAAA,CAAA9D,WAAA,MAAA8D,GAAA,CAAA9D,WAAA,MAIrB;UACqBrE,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA2I,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAA9D,WAAA,QAAA8D,GAAA,CAAA9D,WAAA,MAAA8D,GAAA,CAAA9D,WAAA,MAIrB;UAIErE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAuG,GAAA,CAAA9D,WAAA,OAAuB;UAiCvBrE,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAuG,GAAA,CAAA9D,WAAA,OAAuB;UA+DrBrE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAAoB,iBAAA,CAAA+G,GAAA,CAAA7B,eAAA,GAAuB;UAIpBtG,EAAA,CAAAkB,SAAA,GAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAAuG,GAAA,CAAA9D,WAAA,KAAqB;UACrBrE,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAAuG,GAAA,CAAA9D,WAAA,KAAqB;UAErBrE,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAuG,GAAA,CAAA9D,WAAA,OAAuB;;;qBDhGlC/E,YAAY,EAAAuJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZzJ,WAAW,EAAA0J,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACX3J,YAAY,EAAA8H,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,mBAAA,EAAA/B,EAAA,CAAAgC,qBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EACZ9J,cAAc,EAAA6H,EAAA,CAAAkC,iBAAA,EACd9J,YAAY,EAAA4H,EAAA,CAAAmC,eAAA,EACZ9J,gBAAgB,EAAA2H,EAAA,CAAAoC,mBAAA,EAChB9J,cAAc;MAAA+J,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}