.space-picker-container {
  .search-section {
    margin-bottom: 1rem;

    .search-form {
      border: 1px solid #e4e7ea;
      border-radius: 6px;
      background-color: #f8f9fa;

      .form-label {
        font-size: 0.75rem;
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 0.25rem;
      }

      nb-form-field {
        input {
          height: 32px;
          border-radius: 4px;
          border: 1px solid #dee2e6;
          background-color: #ffffff;
          font-size: 0.8rem;
          padding: 0.375rem 0.75rem;
          transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

          &:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.1);
          }

          &:disabled {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
          }

          &::placeholder {
            color: #adb5bd;
            font-size: 0.8rem;
          }
        }

        nb-icon[nbSuffix] {
          font-size: 0.9rem;
          opacity: 0.6;
          transition: opacity 0.2s ease;

          &:hover {
            opacity: 1;
          }
        }
      }

      .search-actions {
        button {
          height: 32px;
          border-radius: 4px;
          font-size: 0.8rem;
          padding: 0.375rem 0.75rem;
          transition: all 0.15s ease;

          &[ghost] {
            border: 1px solid #dee2e6;
            background-color: #ffffff;
            color: #6c757d;

            &:hover:not(:disabled) {
              border-color: #007bff;
              color: #007bff;
            }
          }

          &[status="primary"] {
            background-color: #007bff;
            border-color: #007bff;

            &:hover:not(:disabled) {
              background-color: #0056b3;
              border-color: #0056b3;
            }
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          nb-icon {
            font-size: 0.9rem;

            &.spinning {
              animation: spin 1s linear infinite;
            }
          }
        }
      }
    }
  }

  .space-list-section {
    min-height: 200px;

    .space-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;
      margin-bottom: 1rem;

      .space-item {
        cursor: pointer;
        transition: all 0.2s ease;

        .space-card {
          padding: 12px;
          border: 2px solid #e4e7ea;
          border-radius: 8px;
          background-color: #ffffff;
          text-align: center;
          transition: all 0.2s ease;

          .space-name {
            font-weight: 600;
            font-size: 0.9rem;
            color: #2c3e50;
            margin-bottom: 4px;
          }

          .space-location {
            font-size: 0.8rem;
            color: #7f8c8d;
          }
        }

        &:hover .space-card {
          border-color: #4a90e2;
          box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
          transform: translateY(-1px);
        }

        &.selected .space-card {
          border-color: #28a745;
          background-color: #f8fff9;
          box-shadow: 0 2px 12px rgba(40, 167, 69, 0.2);

          .space-name {
            color: #28a745;
          }
        }
      }
    }
  }

  .selected-summary {
    .selected-spaces-list {
      .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        background-color: #007bff;
        color: white;
        border-radius: 20px;

        .btn-close {
          background: none;
          border: none;
          color: white;
          opacity: 0.8;
          cursor: pointer;
          padding: 0;
          margin: 0;
          width: 12px;
          height: 12px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            opacity: 1;
          }

          &::before {
            content: '×';
            font-size: 12px;
            line-height: 1;
          }
        }
      }
    }
  }

  // 響應式設計
  @media (max-width: 1200px) {
    .space-list-section {
      .space-grid {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }

  @media (max-width: 992px) {
    .search-section {
      .search-form {
        .search-actions {
          button span {
            display: none;
          }
        }
      }
    }

    .space-list-section {
      .space-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  @media (max-width: 768px) {
    .search-section {
      .search-form {
        .row {
          .col-lg-4,
          .col-md-5 {
            margin-bottom: 0.5rem;
          }

          .col-md-2 {
            margin-bottom: 0;
          }
        }

        .search-actions {
          button {
            flex: 1;
          }
        }
      }
    }

    .space-list-section {
      .space-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
      }
    }
  }

  @media (max-width: 576px) {
    .search-section {
      .search-form {
        .search-actions {
          flex-direction: column;
          gap: 0.5rem !important;

          button {
            width: 100%;

            span {
              display: inline !important;
            }
          }
        }
      }
    }

    .space-list-section {
      .space-grid {
        grid-template-columns: 1fr;
      }
    }
  }


}

// 全域樣式覆蓋
:host {
  display: block;
  width: 100%;
}

// 旋轉動畫
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 工具類
.cursor-pointer {
  cursor: pointer !important;
}
