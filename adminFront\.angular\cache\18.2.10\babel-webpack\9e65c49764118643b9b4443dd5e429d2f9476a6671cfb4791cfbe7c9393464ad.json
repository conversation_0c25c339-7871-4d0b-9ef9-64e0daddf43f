{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/space.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction SpacePickerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function SpacePickerComponent_div_16_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.allSelected);\n  }\n}\nfunction SpacePickerComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_21_Template_div_click_0_listener() {\n      const space_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSpaceSelection(space_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r4 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r4.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r4.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r4.CLocation || \"-\");\n  }\n}\nfunction SpacePickerComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"nb-icon\", 30);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"nav\", 32)(2, \"ul\", 33)(3, \"li\", 34)(4, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_23_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange(1));\n    });\n    i0.ɵɵtext(5, \" \\u7B2C\\u4E00\\u9801 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 34)(7, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_23_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange(ctx_r1.pageIndex - 1));\n    });\n    i0.ɵɵtext(8, \" \\u4E0A\\u4E00\\u9801 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 36)(10, \"span\", 37);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\", 34)(13, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_23_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange(ctx_r1.pageIndex + 1));\n    });\n    i0.ɵɵtext(14, \" \\u4E0B\\u4E00\\u9801 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"li\", 34)(16, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_23_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange(ctx_r1.totalPages));\n    });\n    i0.ɵɵtext(17, \" \\u6700\\u5F8C\\u4E00\\u9801 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.pageIndex === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.pageIndex === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.pageIndex === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.pageIndex === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.pageIndex, \" / \", ctx_r1.totalPages, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.pageIndex === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.pageIndex === ctx_r1.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.pageIndex === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.pageIndex === ctx_r1.totalPages);\n  }\n}\nfunction SpacePickerComponent_div_24_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_24_span_5_Template_button_click_2_listener() {\n      const space_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeSelectedSpace(space_r7));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r7.CPart, \" \");\n  }\n}\nfunction SpacePickerComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"h6\", 39);\n    i0.ɵɵelement(2, \"nb-icon\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41);\n    i0.ɵɵtemplate(5, SpacePickerComponent_div_24_span_5_Template, 3, 1, \"span\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u7A7A\\u9593 (\", ctx_r1.selectedItems.length, \" \\u9805) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedItems);\n  }\n}\nexport class SpacePickerComponent {\n  constructor(spaceService) {\n    this.spaceService = spaceService;\n    this.selectedItems = [];\n    this.multiple = true;\n    this.placeholder = '請選擇空間';\n    this.selectionChange = new EventEmitter();\n    // 搜尋相關屬性\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    // 分頁相關屬性\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.totalRecords = 0;\n    // 資料相關屬性\n    this.availableSpaces = [];\n    this.allSelected = false;\n  }\n  ngOnInit() {\n    this.loadAvailableSpaces();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this.spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n        this.updateAllSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 切換空間選擇\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({\n            ...space\n          });\n        } else {\n          this.selectedItems = [{\n            ...space\n          }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 全選/取消全選\n  toggleAllSpaces() {\n    this.allSelected = !this.allSelected;\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({\n            ...space\n          });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 移除已選空間\n  removeSelectedSpace(space) {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 更新全選狀態\n  updateAllSelectedState() {\n    this.allSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 分頁變更\n  onPageChange(page) {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 計算總頁數\n  get totalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  static {\n    this.ɵfac = function SpacePickerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpacePickerComponent)(i0.ɵɵdirectiveInject(i1.SpaceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpacePickerComponent,\n      selectors: [[\"app-space-picker\"]],\n      inputs: {\n        selectedItems: \"selectedItems\",\n        multiple: \"multiple\",\n        placeholder: \"placeholder\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 25,\n      vars: 11,\n      consts: [[1, \"space-picker-container\"], [1, \"search-section\", \"mb-3\"], [1, \"row\"], [1, \"col-md-5\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [\"nbButton\", \"\", 1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"space-list-section\", \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"font-weight-bold\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"selected-summary mt-3 p-3 bg-light border rounded\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", \"id\", \"selectAllSpaces\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllSpaces\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"font-weight-bold\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [\"aria-label\", \"\\u7A7A\\u9593\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"page-item\", \"active\"], [1, \"page-link\"], [1, \"selected-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"mb-2\"], [\"icon\", \"checkmark-circle-outline\", 1, \"text-success\", \"me-2\"], [1, \"selected-spaces-list\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", \"aria-label\", \"\\u79FB\\u9664\", 1, \"btn-close\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"]],\n      template: function SpacePickerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"nb-form-field\")(5, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_5_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"nb-form-field\")(8, \"input\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_8_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_10_listener() {\n            return ctx.onReset();\n          });\n          i0.ɵɵelement(11, \"nb-icon\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(13, \"nb-icon\", 10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵtemplate(16, SpacePickerComponent_div_16_Template, 4, 1, \"div\", 13)(17, SpacePickerComponent_div_17_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementStart(18, \"small\", 15);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 16);\n          i0.ɵɵtemplate(21, SpacePickerComponent_div_21_Template, 6, 4, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SpacePickerComponent_div_22_Template, 3, 0, \"div\", 18)(23, SpacePickerComponent_div_23_Template, 18, 14, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, SpacePickerComponent_div_24_Template, 6, 2, \"div\", 20);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx.totalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx.pageIndex, \" / \", ctx.totalPages, \" \\u9801 \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableSpaces);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableSpaces.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbFormFieldModule, i4.NbFormFieldComponent, NbInputModule, i4.NbInputDirective, NbButtonModule, i4.NbButtonComponent, NbIconModule, i4.NbIconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 1rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: 2px solid #e4e7ea;\\n  border-radius: 8px;\\n  background-color: #ffffff;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #2c3e50;\\n  margin-bottom: 4px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #7f8c8d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #4a90e2;\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);\\n  transform: translateY(-1px);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #28a745;\\n  background-color: #f8fff9;\\n  box-shadow: 0 2px 12px rgba(40, 167, 69, 0.2);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.8rem;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 20px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: white;\\n  opacity: 0.8;\\n  cursor: pointer;\\n  padding: 0;\\n  margin: 0;\\n  width: 12px;\\n  height: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u00D7\\\";\\n  font-size: 12px;\\n  line-height: 1;\\n}\\n@media (max-width: 768px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n    gap: 8px;\\n  }\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  border-color: #dee2e6;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n  background-color: #e9ecef;\\n  border-color: #dee2e6;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  cursor: not-allowed;\\n}\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbFormFieldModule", "NbInputModule", "NbButtonModule", "NbIconModule", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpacePickerComponent_div_16_Template_input_change_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleAllSpaces", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "allSelected", "SpacePickerComponent_div_21_Template_div_click_0_listener", "space_r4", "_r3", "$implicit", "toggleSpaceSelection", "ɵɵclassProp", "selected", "ɵɵtextInterpolate", "<PERSON>art", "CLocation", "ɵɵelement", "SpacePickerComponent_div_23_Template_button_click_4_listener", "_r5", "onPageChange", "SpacePickerComponent_div_23_Template_button_click_7_listener", "pageIndex", "SpacePickerComponent_div_23_Template_button_click_13_listener", "SpacePickerComponent_div_23_Template_button_click_16_listener", "totalPages", "ɵɵtextInterpolate2", "SpacePickerComponent_div_24_span_5_Template_button_click_2_listener", "space_r7", "_r6", "removeSelectedSpace", "ɵɵtextInterpolate1", "ɵɵtemplate", "SpacePickerComponent_div_24_span_5_Template", "selectedItems", "length", "SpacePickerComponent", "constructor", "spaceService", "multiple", "placeholder", "selectionChange", "searchKeyword", "searchLocation", "pageSize", "totalRecords", "availableSpaces", "ngOnInit", "loadAvailableSpaces", "request", "CStatus", "PageIndex", "PageSize", "apiSpaceGetSpaceListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CSpaceID", "some", "s", "TotalItems", "updateAllSelectedState", "subscribe", "onSearch", "onReset", "space", "push", "for<PERSON>ach", "filter", "emit", "availableSpace", "find", "every", "page", "Math", "ceil", "ɵɵdirectiveInject", "i1", "SpaceService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpacePickerComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "SpacePickerComponent_Template_input_ngModelChange_5_listener", "$event", "ɵɵtwoWayBindingSet", "SpacePickerComponent_Template_input_keyup_enter_5_listener", "SpacePickerComponent_Template_input_ngModelChange_8_listener", "SpacePickerComponent_Template_input_keyup_enter_8_listener", "SpacePickerComponent_Template_button_click_10_listener", "SpacePickerComponent_Template_button_click_12_listener", "SpacePickerComponent_div_16_Template", "SpacePickerComponent_div_17_Template", "SpacePickerComponent_div_21_Template", "SpacePickerComponent_div_22_Template", "SpacePickerComponent_div_23_Template", "SpacePickerComponent_div_24_Template", "ɵɵtwoWayProperty", "ɵɵtextInterpolate3", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbFormFieldComponent", "NbInputDirective", "NbButtonComponent", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { SpaceService } from 'src/services/api/services/space.service';\nimport { GetSpaceListResponse } from 'src/services/api/models';\nimport { tap } from 'rxjs/operators';\n\nexport interface SpacePickerItem {\n  CSpaceID: number;\n  CPart: string;\n  CLocation?: string | null;\n  selected?: boolean;\n}\n\n@Component({\n  selector: 'app-space-picker',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbFormFieldModule,\n    NbInputModule,\n    NbButtonModule,\n    NbIconModule\n  ],\n  templateUrl: './space-picker.component.html',\n  styleUrls: ['./space-picker.component.scss']\n})\nexport class SpacePickerComponent implements OnInit {\n  @Input() selectedItems: SpacePickerItem[] = [];\n  @Input() multiple: boolean = true;\n  @Input() placeholder: string = '請選擇空間';\n  @Output() selectionChange = new EventEmitter<SpacePickerItem[]>();\n\n  // 搜尋相關屬性\n  searchKeyword: string = '';\n  searchLocation: string = '';\n  \n  // 分頁相關屬性\n  pageIndex = 1;\n  pageSize = 10;\n  totalRecords = 0;\n  \n  // 資料相關屬性\n  availableSpaces: SpacePickerItem[] = [];\n  allSelected = false;\n\n  constructor(private spaceService: SpaceService) {}\n\n  ngOnInit(): void {\n    this.loadAvailableSpaces();\n  }\n\n  // 載入可用空間列表\n  loadAvailableSpaces(): void {\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1, // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n\n    this.spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\n      tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID!,\n            CPart: item.CPart!,\n            CLocation: item.CLocation,\n            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n          this.updateAllSelectedState();\n        }\n      })\n    ).subscribe();\n  }\n\n  // 搜尋功能\n  onSearch(): void {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 重置搜尋\n  onReset(): void {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 切換空間選擇\n  toggleSpaceSelection(space: SpacePickerItem): void {\n    space.selected = !space.selected;\n\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({ ...space });\n        } else {\n          this.selectedItems = [{ ...space }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 全選/取消全選\n  toggleAllSpaces(): void {\n    this.allSelected = !this.allSelected;\n    \n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({ ...space });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 移除已選空間\n  removeSelectedSpace(space: SpacePickerItem): void {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    \n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    \n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 更新全選狀態\n  updateAllSelectedState(): void {\n    this.allSelected = this.availableSpaces.length > 0 && \n                      this.availableSpaces.every(space => space.selected);\n  }\n\n  // 分頁變更\n  onPageChange(page: number): void {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n\n  // 計算總頁數\n  get totalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n}\n", "<!-- 空間選擇器共用組件 -->\n<div class=\"space-picker-container\">\n  <!-- 搜尋區域 -->\n  <div class=\"search-section mb-3\">\n    <div class=\"row\">\n      <div class=\"col-md-5\">\n        <nb-form-field>\n          <input type=\"text\" \n                 nbInput \n                 class=\"form-control-sm\" \n                 placeholder=\"搜尋項目名稱...\"\n                 [(ngModel)]=\"searchKeyword\" \n                 (keyup.enter)=\"onSearch()\"\n                 style=\"height: 32px; border-radius: 4px;\" />\n        </nb-form-field>\n      </div>\n      <div class=\"col-md-5\">\n        <nb-form-field>\n          <input type=\"text\" \n                 nbInput \n                 class=\"form-control-sm\" \n                 placeholder=\"搜尋所屬區域...\"\n                 [(ngModel)]=\"searchLocation\" \n                 (keyup.enter)=\"onSearch()\"\n                 style=\"height: 32px; border-radius: 4px;\" />\n        </nb-form-field>\n      </div>\n      <div class=\"col-md-2\">\n        <button class=\"btn btn-sm btn-outline-secondary me-1\" \n                (click)=\"onReset()\" \n                nbButton \n                ghost>\n          <nb-icon icon=\"refresh-outline\"></nb-icon>\n        </button>\n        <button class=\"btn btn-sm btn-secondary\" \n                (click)=\"onSearch()\" \n                nbButton>\n          <nb-icon icon=\"search-outline\"></nb-icon>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 空間列表區域 -->\n  <div class=\"space-list-section border rounded p-3\" style=\"background-color: #f8f9fa;\">\n    <!-- 列表標題和統計 -->\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\n      <div class=\"d-flex align-items-center\" *ngIf=\"multiple\">\n        <input type=\"checkbox\" \n               id=\"selectAllSpaces\" \n               [checked]=\"allSelected\"\n               (change)=\"toggleAllSpaces()\" \n               class=\"me-2\">\n        <label for=\"selectAllSpaces\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\n      </div>\n      <div *ngIf=\"!multiple\" class=\"font-weight-bold\">\n        選擇空間\n      </div>\n      <small class=\"text-muted\">\n        共 {{ totalRecords }} 筆，第 {{ pageIndex }} / {{ totalPages }} 頁\n      </small>\n    </div>\n\n    <!-- 空間項目網格 -->\n    <div class=\"space-grid\">\n      <div class=\"space-item\" \n           *ngFor=\"let space of availableSpaces\"\n           [class.selected]=\"space.selected\" \n           (click)=\"toggleSpaceSelection(space)\">\n        <div class=\"space-card\">\n          <div class=\"space-name\">{{ space.CPart }}</div>\n          <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 空間列表為空時的提示 -->\n    <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\n      <nb-icon icon=\"info-outline\" class=\"me-2\"></nb-icon>\n      沒有符合條件的空間\n    </div>\n\n    <!-- 分頁控制 -->\n    <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"totalRecords > pageSize\">\n      <nav aria-label=\"空間分頁\">\n        <ul class=\"pagination pagination-sm\">\n          <li class=\"page-item\" [class.disabled]=\"pageIndex === 1\">\n            <button class=\"page-link\" (click)=\"onPageChange(1)\" [disabled]=\"pageIndex === 1\">\n              第一頁\n            </button>\n          </li>\n          <li class=\"page-item\" [class.disabled]=\"pageIndex === 1\">\n            <button class=\"page-link\" (click)=\"onPageChange(pageIndex - 1)\" [disabled]=\"pageIndex === 1\">\n              上一頁\n            </button>\n          </li>\n          <li class=\"page-item active\">\n            <span class=\"page-link\">{{ pageIndex }} / {{ totalPages }}</span>\n          </li>\n          <li class=\"page-item\" [class.disabled]=\"pageIndex === totalPages\">\n            <button class=\"page-link\" (click)=\"onPageChange(pageIndex + 1)\" [disabled]=\"pageIndex === totalPages\">\n              下一頁\n            </button>\n          </li>\n          <li class=\"page-item\" [class.disabled]=\"pageIndex === totalPages\">\n            <button class=\"page-link\" (click)=\"onPageChange(totalPages)\" [disabled]=\"pageIndex === totalPages\">\n              最後一頁\n            </button>\n          </li>\n        </ul>\n      </nav>\n    </div>\n  </div>\n\n  <!-- 已選空間摘要 -->\n  <div *ngIf=\"selectedItems.length > 0\" class=\"selected-summary mt-3 p-3 bg-light border rounded\">\n    <h6 class=\"mb-2\">\n      <nb-icon icon=\"checkmark-circle-outline\" class=\"text-success me-2\"></nb-icon>\n      已選空間 ({{ selectedItems.length }} 項)\n    </h6>\n    <div class=\"selected-spaces-list\">\n      <span *ngFor=\"let space of selectedItems\" \n            class=\"badge badge-primary me-1 mb-1\">\n        {{ space.CPart }}\n        <button type=\"button\" \n                class=\"btn-close ms-1\" \n                (click)=\"removeSelectedSpace(space)\"\n                style=\"font-size: 0.7rem;\"\n                aria-label=\"移除\">\n        </button>\n      </span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAG/F,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;IC0C5BC,EADF,CAAAC,cAAA,cAAwD,gBAKlC;IADbD,EAAA,CAAAE,UAAA,oBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAHnCT,EAAA,CAAAU,YAAA,EAIoB;IACpBV,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAW,MAAA,2CAAM;IACnEX,EADmE,CAAAU,YAAA,EAAQ,EACrE;;;;IAJGV,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAAa,UAAA,YAAAP,MAAA,CAAAQ,WAAA,CAAuB;;;;;IAKhCd,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAW,MAAA,iCACF;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;;;IAQNV,EAAA,CAAAC,cAAA,cAG2C;IAAtCD,EAAA,CAAAE,UAAA,mBAAAa,0DAAA;MAAA,MAAAC,QAAA,GAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,oBAAA,CAAAH,QAAA,CAA2B;IAAA,EAAC;IAEtChB,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAW,MAAA,GAAiB;IAAAX,EAAA,CAAAU,YAAA,EAAM;IAC/CV,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAW,MAAA,GAA4B;IAE5DX,EAF4D,CAAAU,YAAA,EAAM,EAC1D,EACF;;;;IANDV,EAAA,CAAAoB,WAAA,aAAAJ,QAAA,CAAAK,QAAA,CAAiC;IAGVrB,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAsB,iBAAA,CAAAN,QAAA,CAAAO,KAAA,CAAiB;IACbvB,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAAsB,iBAAA,CAAAN,QAAA,CAAAQ,SAAA,QAA4B;;;;;IAM9DxB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAyB,SAAA,kBAAoD;IACpDzB,EAAA,CAAAW,MAAA,+DACF;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;;;IAOEV,EAJR,CAAAC,cAAA,cAAgF,cACvD,aACgB,aACsB,iBAC0B;IAAvDD,EAAA,CAAAE,UAAA,mBAAAwB,6DAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,YAAA,CAAa,CAAC,CAAC;IAAA,EAAC;IACjD5B,EAAA,CAAAW,MAAA,2BACF;IACFX,EADE,CAAAU,YAAA,EAAS,EACN;IAEHV,EADF,CAAAC,cAAA,aAAyD,iBACsC;IAAnED,EAAA,CAAAE,UAAA,mBAAA2B,6DAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,YAAA,CAAAtB,MAAA,CAAAwB,SAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC7D9B,EAAA,CAAAW,MAAA,2BACF;IACFX,EADE,CAAAU,YAAA,EAAS,EACN;IAEHV,EADF,CAAAC,cAAA,aAA6B,gBACH;IAAAD,EAAA,CAAAW,MAAA,IAAkC;IAC5DX,EAD4D,CAAAU,YAAA,EAAO,EAC9D;IAEHV,EADF,CAAAC,cAAA,cAAkE,kBACsC;IAA5ED,EAAA,CAAAE,UAAA,mBAAA6B,8DAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,YAAA,CAAAtB,MAAA,CAAAwB,SAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC7D9B,EAAA,CAAAW,MAAA,4BACF;IACFX,EADE,CAAAU,YAAA,EAAS,EACN;IAEHV,EADF,CAAAC,cAAA,cAAkE,kBACmC;IAAzED,EAAA,CAAAE,UAAA,mBAAA8B,8DAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,YAAA,CAAAtB,MAAA,CAAA2B,UAAA,CAAwB;IAAA,EAAC;IAC1DjC,EAAA,CAAAW,MAAA,kCACF;IAIRX,EAJQ,CAAAU,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAzBsBV,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAoB,WAAA,aAAAd,MAAA,CAAAwB,SAAA,OAAkC;IACF9B,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAAa,UAAA,aAAAP,MAAA,CAAAwB,SAAA,OAA4B;IAI5D9B,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAoB,WAAA,aAAAd,MAAA,CAAAwB,SAAA,OAAkC;IACU9B,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAAa,UAAA,aAAAP,MAAA,CAAAwB,SAAA,OAA4B;IAKpE9B,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAkC,kBAAA,KAAA5B,MAAA,CAAAwB,SAAA,SAAAxB,MAAA,CAAA2B,UAAA,KAAkC;IAEtCjC,EAAA,CAAAY,SAAA,EAA2C;IAA3CZ,EAAA,CAAAoB,WAAA,aAAAd,MAAA,CAAAwB,SAAA,KAAAxB,MAAA,CAAA2B,UAAA,CAA2C;IACCjC,EAAA,CAAAY,SAAA,EAAqC;IAArCZ,EAAA,CAAAa,UAAA,aAAAP,MAAA,CAAAwB,SAAA,KAAAxB,MAAA,CAAA2B,UAAA,CAAqC;IAIjFjC,EAAA,CAAAY,SAAA,GAA2C;IAA3CZ,EAAA,CAAAoB,WAAA,aAAAd,MAAA,CAAAwB,SAAA,KAAAxB,MAAA,CAAA2B,UAAA,CAA2C;IACFjC,EAAA,CAAAY,SAAA,EAAqC;IAArCZ,EAAA,CAAAa,UAAA,aAAAP,MAAA,CAAAwB,SAAA,KAAAxB,MAAA,CAAA2B,UAAA,CAAqC;;;;;;IAgBxGjC,EAAA,CAAAC,cAAA,eAC4C;IAC1CD,EAAA,CAAAW,MAAA,GACA;IAAAX,EAAA,CAAAC,cAAA,iBAIwB;IAFhBD,EAAA,CAAAE,UAAA,mBAAAiC,oEAAA;MAAA,MAAAC,QAAA,GAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA,EAAAnB,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgC,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAI9CpC,EADE,CAAAU,YAAA,EAAS,EACJ;;;;IAPLV,EAAA,CAAAY,SAAA,EACA;IADAZ,EAAA,CAAAuC,kBAAA,MAAAH,QAAA,CAAAb,KAAA,MACA;;;;;IARJvB,EADF,CAAAC,cAAA,cAAgG,aAC7E;IACfD,EAAA,CAAAyB,SAAA,kBAA6E;IAC7EzB,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAwC,UAAA,IAAAC,2CAAA,mBAC4C;IAUhDzC,EADE,CAAAU,YAAA,EAAM,EACF;;;;IAdFV,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAuC,kBAAA,gCAAAjC,MAAA,CAAAoC,aAAA,CAAAC,MAAA,cACF;IAE0B3C,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAa,UAAA,YAAAP,MAAA,CAAAoC,aAAA,CAAgB;;;AD5F9C,OAAM,MAAOE,oBAAoB;EAmB/BC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAlBvB,KAAAJ,aAAa,GAAsB,EAAE;IACrC,KAAAK,QAAQ,GAAY,IAAI;IACxB,KAAAC,WAAW,GAAW,OAAO;IAC5B,KAAAC,eAAe,GAAG,IAAIzD,YAAY,EAAqB;IAEjE;IACA,KAAA0D,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAE3B;IACA,KAAArB,SAAS,GAAG,CAAC;IACb,KAAAsB,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,CAAC;IAEhB;IACA,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAxC,WAAW,GAAG,KAAK;EAE8B;EAEjDyC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACdlC,KAAK,EAAE,IAAI,CAAC2B,aAAa,IAAI,IAAI;MACjC1B,SAAS,EAAE,IAAI,CAAC2B,cAAc,IAAI,IAAI;MACtCO,OAAO,EAAE,CAAC;MAAE;MACZC,SAAS,EAAE,IAAI,CAAC7B,SAAS;MACzB8B,QAAQ,EAAE,IAAI,CAACR;KAChB;IAED,IAAI,CAACN,YAAY,CAACe,6BAA6B,CAAC;MAAEC,IAAI,EAAEL;IAAO,CAAE,CAAC,CAACM,IAAI,CACrEhE,GAAG,CAACiE,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACX,eAAe,GAAGU,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDC,QAAQ,EAAED,IAAI,CAACC,QAAS;UACxB9C,KAAK,EAAE6C,IAAI,CAAC7C,KAAM;UAClBC,SAAS,EAAE4C,IAAI,CAAC5C,SAAS;UACzBH,QAAQ,EAAE,IAAI,CAACqB,aAAa,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKD,IAAI,CAACC,QAAQ;SACpE,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAChB,YAAY,GAAGW,QAAQ,CAACQ,UAAU,IAAI,CAAC;QAC5C,IAAI,CAACC,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,CAAC7C,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC0B,mBAAmB,EAAE;EAC5B;EAEA;EACAoB,OAAOA,CAAA;IACL,IAAI,CAAC1B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACrB,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC0B,mBAAmB,EAAE;EAC5B;EAEA;EACArC,oBAAoBA,CAAC0D,KAAsB;IACzCA,KAAK,CAACxD,QAAQ,GAAG,CAACwD,KAAK,CAACxD,QAAQ;IAEhC,IAAIwD,KAAK,CAACxD,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACqB,aAAa,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC,EAAE;QAChE,IAAI,IAAI,CAACtB,QAAQ,EAAE;UACjB,IAAI,CAACL,aAAa,CAACoC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAACnC,aAAa,GAAG,CAAC;YAAE,GAAGmC;UAAK,CAAE,CAAC;UACnC;UACA,IAAI,CAACvB,eAAe,CAACyB,OAAO,CAACR,CAAC,IAAG;YAC/B,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,EAAE;cACjCE,CAAC,CAAClD,QAAQ,GAAG,KAAK;YACpB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACsC,MAAM,CAACT,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;IACpF;IAEA,IAAI,CAACI,sBAAsB,EAAE;IAC7B,IAAI,CAACxB,eAAe,CAACgC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACvC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAjC,eAAeA,CAAA;IACb,IAAI,CAACK,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACwC,eAAe,CAACyB,OAAO,CAACF,KAAK,IAAG;MACnC,IAAI,IAAI,CAAC/D,WAAW,IAAI,CAAC+D,KAAK,CAACxD,QAAQ,EAAE;QACvCwD,KAAK,CAACxD,QAAQ,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,CAACqB,aAAa,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC,EAAE;UAChE,IAAI,CAAC3B,aAAa,CAACoC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC/D,WAAW,IAAI+D,KAAK,CAACxD,QAAQ,EAAE;QAC9CwD,KAAK,CAACxD,QAAQ,GAAG,KAAK;QACtB,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACsC,MAAM,CAACT,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;MACpF;IACF,CAAC,CAAC;IAEF,IAAI,CAACpB,eAAe,CAACgC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACvC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAJ,mBAAmBA,CAACuC,KAAsB;IACxC,IAAI,CAACnC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACsC,MAAM,CAACT,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;IAElF;IACA,MAAMa,cAAc,GAAG,IAAI,CAAC5B,eAAe,CAAC6B,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;IACpF,IAAIa,cAAc,EAAE;MAClBA,cAAc,CAAC7D,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACoD,sBAAsB,EAAE;IAC7B,IAAI,CAACxB,eAAe,CAACgC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACvC,aAAa,CAAC,CAAC;EACpD;EAEA;EACA+B,sBAAsBA,CAAA;IACpB,IAAI,CAAC3D,WAAW,GAAG,IAAI,CAACwC,eAAe,CAACX,MAAM,GAAG,CAAC,IAChC,IAAI,CAACW,eAAe,CAAC8B,KAAK,CAACP,KAAK,IAAIA,KAAK,CAACxD,QAAQ,CAAC;EACvE;EAEA;EACAO,YAAYA,CAACyD,IAAY;IACvB,IAAI,CAACvD,SAAS,GAAGuD,IAAI;IACrB,IAAI,CAAC7B,mBAAmB,EAAE;EAC5B;EAEA;EACA,IAAIvB,UAAUA,CAAA;IACZ,OAAOqD,IAAI,CAACC,IAAI,CAAC,IAAI,CAAClC,YAAY,GAAG,IAAI,CAACD,QAAQ,CAAC;EACrD;;;uCA3IWR,oBAAoB,EAAA5C,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApB9C,oBAAoB;MAAA+C,SAAA;MAAAC,MAAA;QAAAlD,aAAA;QAAAK,QAAA;QAAAC,WAAA;MAAA;MAAA6C,OAAA;QAAA5C,eAAA;MAAA;MAAA6C,UAAA;MAAAC,QAAA,GAAA/F,EAAA,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBvBtG,EANV,CAAAC,cAAA,aAAoC,aAED,aACd,aACO,oBACL,eAOsC;UAF5CD,EAAA,CAAAwG,gBAAA,2BAAAC,6DAAAC,MAAA;YAAA1G,EAAA,CAAA2G,kBAAA,CAAAJ,GAAA,CAAArD,aAAA,EAAAwD,MAAA,MAAAH,GAAA,CAAArD,aAAA,GAAAwD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAC3B1G,EAAA,CAAAE,UAAA,yBAAA0G,2DAAA;YAAA,OAAeL,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAGrC3E,EARI,CAAAU,YAAA,EAMmD,EACrC,EACZ;UAGFV,EAFJ,CAAAC,cAAA,aAAsB,oBACL,eAOsC;UAF5CD,EAAA,CAAAwG,gBAAA,2BAAAK,6DAAAH,MAAA;YAAA1G,EAAA,CAAA2G,kBAAA,CAAAJ,GAAA,CAAApD,cAAA,EAAAuD,MAAA,MAAAH,GAAA,CAAApD,cAAA,GAAAuD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAC5B1G,EAAA,CAAAE,UAAA,yBAAA4G,2DAAA;YAAA,OAAeP,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAGrC3E,EARI,CAAAU,YAAA,EAMmD,EACrC,EACZ;UAEJV,EADF,CAAAC,cAAA,aAAsB,iBAIN;UAFND,EAAA,CAAAE,UAAA,mBAAA6G,uDAAA;YAAA,OAASR,GAAA,CAAA3B,OAAA,EAAS;UAAA,EAAC;UAGzB5E,EAAA,CAAAyB,SAAA,kBAA0C;UAC5CzB,EAAA,CAAAU,YAAA,EAAS;UACTV,EAAA,CAAAC,cAAA,iBAEiB;UADTD,EAAA,CAAAE,UAAA,mBAAA8G,uDAAA;YAAA,OAAST,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAE1B3E,EAAA,CAAAyB,SAAA,mBAAyC;UAIjDzB,EAHM,CAAAU,YAAA,EAAS,EACL,EACF,EACF;UAKJV,EAFF,CAAAC,cAAA,eAAsF,eAEhB;UASlED,EARA,CAAAwC,UAAA,KAAAyE,oCAAA,kBAAwD,KAAAC,oCAAA,kBAQR;UAGhDlH,EAAA,CAAAC,cAAA,iBAA0B;UACxBD,EAAA,CAAAW,MAAA,IACF;UACFX,EADE,CAAAU,YAAA,EAAQ,EACJ;UAGNV,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAwC,UAAA,KAAA2E,oCAAA,kBAG2C;UAM7CnH,EAAA,CAAAU,YAAA,EAAM;UASNV,EANA,CAAAwC,UAAA,KAAA4E,oCAAA,kBAA8E,KAAAC,oCAAA,oBAME;UA6BlFrH,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAwC,UAAA,KAAA8E,oCAAA,kBAAgG;UAkBlGtH,EAAA,CAAAU,YAAA,EAAM;;;UA1HWV,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAuH,gBAAA,YAAAhB,GAAA,CAAArD,aAAA,CAA2B;UAW3BlD,EAAA,CAAAY,SAAA,GAA4B;UAA5BZ,EAAA,CAAAuH,gBAAA,YAAAhB,GAAA,CAAApD,cAAA,CAA4B;UAyBCnD,EAAA,CAAAY,SAAA,GAAc;UAAdZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAAxD,QAAA,CAAc;UAQhD/C,EAAA,CAAAY,SAAA,EAAe;UAAfZ,EAAA,CAAAa,UAAA,UAAA0F,GAAA,CAAAxD,QAAA,CAAe;UAInB/C,EAAA,CAAAY,SAAA,GACF;UADEZ,EAAA,CAAAwH,kBAAA,aAAAjB,GAAA,CAAAlD,YAAA,0BAAAkD,GAAA,CAAAzE,SAAA,SAAAyE,GAAA,CAAAtE,UAAA,aACF;UAMuBjC,EAAA,CAAAY,SAAA,GAAkB;UAAlBZ,EAAA,CAAAa,UAAA,YAAA0F,GAAA,CAAAjD,eAAA,CAAkB;UAWrCtD,EAAA,CAAAY,SAAA,EAAkC;UAAlCZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAAjD,eAAA,CAAAX,MAAA,OAAkC;UAMS3C,EAAA,CAAAY,SAAA,EAA6B;UAA7BZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAAlD,YAAA,GAAAkD,GAAA,CAAAnD,QAAA,CAA6B;UAgC1EpD,EAAA,CAAAY,SAAA,EAA8B;UAA9BZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAA7D,aAAA,CAAAC,MAAA,KAA8B;;;qBDhGlClD,YAAY,EAAAgI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjI,WAAW,EAAAkI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXpI,iBAAiB,EAAAqI,EAAA,CAAAC,oBAAA,EACjBrI,aAAa,EAAAoI,EAAA,CAAAE,gBAAA,EACbrI,cAAc,EAAAmI,EAAA,CAAAG,iBAAA,EACdrI,YAAY,EAAAkI,EAAA,CAAAI,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}