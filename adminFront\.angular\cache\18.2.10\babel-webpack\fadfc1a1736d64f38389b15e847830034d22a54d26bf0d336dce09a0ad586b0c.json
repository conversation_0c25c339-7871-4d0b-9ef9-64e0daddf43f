{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { SpacePickerComponent } from 'src/app/shared/components/space-picker/space-picker.component';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateModal_r4 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(templateModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateModal_r4 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(templateModal_r4, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 40)(14, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_Template_button_click_14_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(77);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(15, \"i\", 42);\n    i0.ɵɵtext(16, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TemplateComponent_tr_70_button_17_Template, 3, 0, \"button\", 43)(18, TemplateComponent_tr_70_button_18_Template, 3, 0, \"button\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CTemplateType === 1 ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : template_r6.CTemplateType === 2 ? \"\\u9805\\u76EE\\u6A21\\u677F\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : \"\\u9805\\u76EE\\u6A21\\u677F\");\n  }\n}\nfunction TemplateComponent_ng_template_74_nb_form_field_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-form-field\", 71)(1, \"nb-select\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_nb_form_field_23_Template_nb_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateType, $event) || (ctx_r2.templateDetail.CTemplateType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"nb-option\", 15)(3, \"span\", 4);\n    i0.ɵɵelement(4, \"i\", 84);\n    i0.ɵɵtext(5, \"\\u7A7A\\u9593\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"nb-option\", 15)(7, \"span\", 4);\n    i0.ɵɵelement(8, \"i\", 85);\n    i0.ɵɵtext(9, \"\\u9805\\u76EE\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r2.EnumTemplateType.ItemTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_div_10_div_10_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵtext(1, \" \\u55AE\\u50F9\\u5FC5\\u9808\\u5927\\u65BC0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_div_10_div_10_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵtext(1, \" \\u8ACB\\u8F38\\u5165\\u5177\\u9AD4\\u55AE\\u4F4D\\uFF08\\u4E0D\\u80FD\\u70BA\\u7A7A\\u6216\\\"\\u5F0F\\\"\\uFF09 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_div_10_div_10_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126);\n    i0.ɵɵelement(1, \"i\", 127);\n    i0.ɵɵtext(2, \"\\u8A2D\\u5B9A\\u5B8C\\u6210 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_div_10_div_10_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128);\n    i0.ɵɵelement(1, \"i\", 129);\n    i0.ɵɵtext(2, \"\\u8ACB\\u5B8C\\u6210\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_div_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"div\", 105)(2, \"div\", 106)(3, \"div\", 107)(4, \"div\", 108);\n    i0.ɵɵelement(5, \"i\", 109);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 110);\n    i0.ɵɵelement(8, \"i\", 111);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 112)(11, \"label\", 113);\n    i0.ɵɵtext(12, \"\\u55AE\\u50F9 \");\n    i0.ɵɵelementStart(13, \"span\", 114);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"nb-form-field\")(16, \"input\", 115);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_div_39_div_10_div_10_Template_input_ngModelChange_16_listener($event) {\n      const item_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r15.CUnitPrice, $event) || (item_r15.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function TemplateComponent_ng_template_74_div_39_div_10_div_10_Template_input_ngModelChange_16_listener($event) {\n      const item_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onPriceChange(item_r15, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"nb-icon\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, TemplateComponent_ng_template_74_div_39_div_10_div_10_div_18_Template, 2, 0, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 112)(20, \"label\", 113);\n    i0.ɵɵtext(21, \"\\u55AE\\u4F4D \");\n    i0.ɵɵelementStart(22, \"span\", 114);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"nb-form-field\")(25, \"input\", 118);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_div_39_div_10_div_10_Template_input_ngModelChange_25_listener($event) {\n      const item_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r15.CUnit, $event) || (item_r15.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"nb-icon\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, TemplateComponent_ng_template_74_div_39_div_10_div_10_div_27_Template, 2, 0, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 120)(29, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_39_div_10_div_10_Template_button_click_29_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedItem(item_r15));\n    });\n    i0.ɵɵelement(30, \"i\", 48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 122);\n    i0.ɵɵtemplate(32, TemplateComponent_ng_template_74_div_39_div_10_div_10_div_32_Template, 3, 0, \"div\", 123)(33, TemplateComponent_ng_template_74_div_39_div_10_div_10_div_33_Template, 3, 0, \"div\", 124);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"valid\", ctx_r2.isItemValid(item_r15))(\"invalid\", !ctx_r2.isItemValid(item_r15));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", item_r15.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", item_r15.CLocation || \"-\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r15.CUnitPrice || item_r15.CUnitPrice <= 0);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r15.CUnitPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r15.CUnitPrice || item_r15.CUnitPrice <= 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r15.CUnit || item_r15.CUnit.trim() === \"\" || item_r15.CUnit.trim() === \"\\u5F0F\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r15.CUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r15.CUnit || item_r15.CUnit.trim() === \"\" || item_r15.CUnit.trim() === \"\\u5F0F\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemValid(item_r15));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isItemValid(item_r15));\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 92)(2, \"h6\", 53);\n    i0.ɵɵelement(3, \"i\", 93);\n    i0.ɵɵtext(4, \"\\u9805\\u76EE\\u55AE\\u50F9\\u55AE\\u4F4D\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(5, \"span\", 94);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"small\", 95);\n    i0.ɵɵtext(8, \"\\u8ACB\\u70BA\\u6BCF\\u500B\\u9078\\u4E2D\\u7684\\u9805\\u76EE\\u8A2D\\u5B9A\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 96);\n    i0.ɵɵtemplate(10, TemplateComponent_ng_template_74_div_39_div_10_div_10_Template, 34, 16, \"div\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 98)(12, \"div\", 58)(13, \"div\", 8)(14, \"div\", 99)(15, \"span\", 95);\n    i0.ɵɵtext(16, \"\\u5DF2\\u9078\\u64C7\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 100);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 99)(21, \"span\", 95);\n    i0.ɵɵtext(22, \"\\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 101);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 102);\n    i0.ɵɵelement(26, \"div\", 103);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getValidItemsCount(), \"/\", ctx_r2.selectedItemsForTemplate.length, \" \\u5DF2\\u5B8C\\u6210\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedItemsForTemplate);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.selectedItemsForTemplate.length, \" \\u500B\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"text-success\", ctx_r2.getValidItemsCount() === ctx_r2.selectedItemsForTemplate.length)(\"text-warning\", ctx_r2.getValidItemsCount() < ctx_r2.selectedItemsForTemplate.length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getValidItemsCount(), \" \\u500B \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.selectedItemsForTemplate.length > 0 ? ctx_r2.getValidItemsCount() / ctx_r2.selectedItemsForTemplate.length * 100 : 0, \"%\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 58)(3, \"div\", 86)(4, \"label\", 87);\n    i0.ɵɵtext(5, \"\\u660E\\u7D30\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 64)(7, \"div\", 88)(8, \"app-space-picker\", 89);\n    i0.ɵɵlistener(\"selectionChange\", function TemplateComponent_ng_template_74_div_39_Template_app_space_picker_selectionChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSelectionChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 18);\n    i0.ɵɵtemplate(10, TemplateComponent_ng_template_74_div_39_div_10_Template, 27, 11, \"div\", 90);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"selectedItems\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate ? ctx_r2.selectedSpacesForTemplate : ctx_r2.selectedItemsForTemplate)(\"multiple\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate && ctx_r2.selectedItemsForTemplate.length > 0);\n  }\n}\nfunction TemplateComponent_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 51)(1, \"nb-card-header\", 52)(2, \"h5\", 53);\n    i0.ɵɵelement(3, \"i\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_5_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵelement(6, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 57)(8, \"div\", 58)(9, \"div\", 59)(10, \"div\", 60)(11, \"div\", 61)(12, \"div\", 8)(13, \"div\", 62)(14, \"label\", 63);\n    i0.ɵɵtext(15, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 64)(17, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_74_Template_input_keydown_control_enter_17_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r11));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 4)(19, \"label\", 66);\n    i0.ɵɵtext(20, \"\\u6A21\\u677F\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64);\n    i0.ɵɵtemplate(22, TemplateComponent_ng_template_74_div_22_Template, 4, 1, \"div\", 67)(23, TemplateComponent_ng_template_74_nb_form_field_23_Template, 10, 3, \"nb-form-field\", 68);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 8)(25, \"div\", 69)(26, \"label\", 70);\n    i0.ɵɵtext(27, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 64)(29, \"nb-form-field\", 71)(30, \"nb-select\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 15)(32, \"span\", 4);\n    i0.ɵɵelement(33, \"i\", 73);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 15)(36, \"span\", 4);\n    i0.ɵɵelement(37, \"i\", 74);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()()();\n    i0.ɵɵtemplate(39, TemplateComponent_ng_template_74_div_39_Template, 11, 3, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-card-footer\", 76)(41, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_41_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵelement(42, \"i\", 78);\n    i0.ɵɵtext(43, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_44_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r11));\n    });\n    i0.ɵɵelement(45, \"i\", 80);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", ctx_r2.modalWidth);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-plus-circle\", !ctx_r2.isEditMode)(\"fa-edit\", ctx_r2.isEditMode)(\"text-success\", !ctx_r2.isEditMode)(\"text-warning\", ctx_r2.isEditMode);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"\\u7DE8\\u8F2F\\u6A21\\u677F\" : \"\\u65B0\\u589E\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.isEditMode ? \"\\u66F4\\u65B0\\u6A21\\u677F\" : \"\\u5EFA\\u7ACB\\u6A21\\u677F\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_76_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵelement(1, \"i\", 154);\n    i0.ɵɵelementStart(2, \"span\", 95);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 162);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 163);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r18 = ctx.$implicit;\n    const i_r19 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r19 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r18.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r18.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157)(1, \"table\", 158)(2, \"thead\")(3, \"tr\")(4, \"th\", 159);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 160);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 161);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_76_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_76_div_70_div_1_Template, 12, 1, \"div\", 155)(2, TemplateComponent_ng_template_76_div_70_div_2_Template, 3, 0, \"div\", 156);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 130)(1, \"nb-card-header\", 52)(2, \"h5\", 53);\n    i0.ɵɵelement(3, \"i\", 131);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_5_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r17));\n    });\n    i0.ɵɵelement(6, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 57)(8, \"div\", 132)(9, \"div\", 133)(10, \"h6\", 134);\n    i0.ɵɵelement(11, \"i\", 135);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 136)(14, \"div\", 58)(15, \"div\", 8)(16, \"div\", 137)(17, \"label\", 138);\n    i0.ɵɵelement(18, \"i\", 139);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 140);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 137)(24, \"label\", 138);\n    i0.ɵɵelement(25, \"i\", 141);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 140)(28, \"span\", 39);\n    i0.ɵɵelement(29, \"i\", 142);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 8)(32, \"div\", 137)(33, \"label\", 138);\n    i0.ɵɵelement(34, \"i\", 143);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 140);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 8)(40, \"div\", 137)(41, \"label\", 138);\n    i0.ɵɵelement(42, \"i\", 144);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 140);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 8)(47, \"div\", 137)(48, \"label\", 138);\n    i0.ɵɵelement(49, \"i\", 145);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 140);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 8)(55, \"div\", 137)(56, \"label\", 138);\n    i0.ɵɵelement(57, \"i\", 146);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 140);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 147)(62, \"div\", 148)(63, \"h6\", 134);\n    i0.ɵɵelement(64, \"i\", 149);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 150);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 136);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_76_div_69_Template, 4, 0, \"div\", 151)(70, TemplateComponent_ng_template_76_div_70_Template, 3, 2, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 76)(72, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_72_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r17));\n    });\n    i0.ɵɵelement(73, \"i\", 78);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport let TemplateComponent = /*#__PURE__*/(() => {\n  class TemplateComponent extends BaseComponent {\n    constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n      super(allow);\n      this.allow = allow;\n      this.dialogService = dialogService;\n      this._templateService = _templateService;\n      this._spaceService = _spaceService;\n      this.message = message;\n      this.valid = valid;\n      this.Math = Math; // 讓模板可以使用 Math 函數\n      this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n      this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      // 模板相關屬性\n      this.templateList = [];\n      this.templateDetail = {};\n      this.searchKeyword = '';\n      this.searchStatus = null;\n      this.searchTemplateType = null;\n      // 空間選擇相關屬性\n      this.availableSpaces = [];\n      this.selectedSpacesForTemplate = [];\n      this.spaceSearchKeyword = '';\n      this.spaceSearchLocation = '';\n      this.spacePageIndex = 1;\n      this.spacePageSize = 10;\n      this.spaceTotalRecords = 0;\n      this.allSpacesSelected = false;\n      // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\n      this.availableItemsForTemplate = [];\n      this.selectedItemsForTemplate = [];\n      this.itemSearchKeyword = '';\n      this.itemSearchLocation = '';\n      this.itemPageIndex = 1;\n      this.itemPageSize = 10;\n      this.itemTotalRecords = 0;\n      this.allItemsSelected = false;\n      // 模板明細相關屬性\n      this.selectedTemplateDetail = null;\n      this.templateDetailSpaces = [];\n      this.isLoadingTemplateDetail = false;\n      // 模態框模式控制\n      this.isEditMode = false;\n    }\n    ngOnInit() {\n      this.loadTemplateList();\n      this.loadAvailableSpaces();\n    }\n    // 載入模板列表\n    loadTemplateList() {\n      const request = {\n        CTemplateName: this.searchKeyword || null,\n        CStatus: this.searchStatus,\n        CTemplateType: this.searchTemplateType,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      this._templateService.apiTemplateGetTemplateListPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.templateList = response.Entries?.map(item => ({\n            CTemplateId: item.CTemplateId,\n            CTemplateName: item.CTemplateName,\n            CTemplateType: item.CTemplateType,\n            // 新增模板類型\n            CCreateDt: item.CCreateDt,\n            CUpdateDt: item.CUpdateDt,\n            CCreator: item.CCreator,\n            CUpdator: item.CUpdator,\n            CStatus: item.CStatus\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n        } else {\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n        }\n      })).subscribe();\n    }\n    // 載入可用空間列表\n    loadAvailableSpaces() {\n      const request = {\n        CPart: this.spaceSearchKeyword || null,\n        CLocation: this.spaceSearchLocation || null,\n        CStatus: 1,\n        // 只顯示啟用的空間\n        PageIndex: this.spacePageIndex,\n        PageSize: this.spacePageSize\n      };\n      this._spaceService.apiSpaceGetSpaceListPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID,\n            CPart: item.CPart,\n            CLocation: item.CLocation,\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.spaceTotalRecords = response.TotalItems || 0;\n          this.updateAllSpacesSelectedState();\n        }\n      })).subscribe();\n    }\n    // 搜尋功能\n    onSearch() {\n      this.pageIndex = 1;\n      this.loadTemplateList();\n    }\n    onReset() {\n      this.searchKeyword = '';\n      this.searchStatus = null;\n      this.pageIndex = 1;\n      this.loadTemplateList();\n    }\n    // 載入項目模板可用項目（使用空間列表作為基礎）\n    loadAvailableItemsForTemplate() {\n      const request = {\n        CPart: this.itemSearchKeyword || null,\n        CLocation: this.itemSearchLocation || null,\n        CStatus: 1,\n        // 只顯示啟用的空間\n        PageIndex: this.itemPageIndex,\n        PageSize: this.itemPageSize\n      };\n      this._spaceService.apiSpaceGetSpaceListPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableItemsForTemplate = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID,\n            CPart: item.CPart,\n            CLocation: item.CLocation,\n            selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\n            CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\n            CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\n          })) || [];\n          this.itemTotalRecords = response.TotalItems || 0;\n          this.updateAllItemsSelectedState();\n        }\n      })).subscribe();\n    }\n    // 空間搜尋功能\n    onSpaceSearch() {\n      this.spacePageIndex = 1;\n      this.loadAvailableSpaces();\n    }\n    onSpaceReset() {\n      this.spaceSearchKeyword = '';\n      this.spaceSearchLocation = '';\n      this.spacePageIndex = 1;\n      this.loadAvailableSpaces();\n    }\n    // 項目搜尋功能\n    onItemSearch() {\n      this.itemPageIndex = 1;\n      this.loadAvailableItemsForTemplate();\n    }\n    onItemReset() {\n      this.itemSearchKeyword = '';\n      this.itemSearchLocation = '';\n      this.itemPageIndex = 1;\n      this.loadAvailableItemsForTemplate();\n    }\n    // 分頁功能\n    pageChanged(page) {\n      this.pageIndex = page;\n      this.loadTemplateList();\n    }\n    spacePageChanged(page) {\n      this.spacePageIndex = page;\n      this.loadAvailableSpaces();\n    }\n    itemPageChanged(page) {\n      this.itemPageIndex = page;\n      this.loadAvailableItemsForTemplate();\n    }\n    // 模態框操作\n    openCreateModal(modal) {\n      this.isEditMode = false;\n      this.templateDetail = {\n        CStatus: 1,\n        CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n      };\n      this.selectedSpacesForTemplate = [];\n      this.selectedItemsForTemplate = [];\n      this.loadAvailableSpaces();\n      this.loadAvailableItemsForTemplate();\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n    }\n    openEditModal(modal, template) {\n      this.isEditMode = true;\n      this.templateDetail = {\n        CTemplateId: template.CTemplateId,\n        CTemplateName: template.CTemplateName,\n        CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n        CStatus: template.CStatus || 1\n      };\n      // 編輯模式下不需要載入選擇器數據\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    // 計算模態框寬度\n    get modalWidth() {\n      return this.isEditMode ? '550px' : '800px';\n    }\n    onSubmit(ref) {\n      if (!this.validateTemplateForm()) {\n        return;\n      }\n      if (this.templateDetail.CTemplateId) {\n        this.updateTemplate(ref);\n      } else {\n        this.createTemplate(ref);\n      }\n    }\n    // 驗證表單\n    validateTemplateForm() {\n      if (!this.templateDetail.CTemplateName?.trim()) {\n        this.message.showErrorMSG('請輸入模板名稱');\n        return false;\n      }\n      if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n        this.message.showErrorMSG('請選擇模板類型');\n        return false;\n      }\n      if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n        this.message.showErrorMSG('請選擇模板狀態');\n        return false;\n      }\n      if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n        this.message.showErrorMSG('空間模板請至少選擇一個空間');\n        return false;\n      }\n      if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\n        this.message.showErrorMSG('項目模板請至少選擇一個項目');\n        return false;\n      }\n      // 驗證項目模板的單價和單位\n      if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n        for (const item of this.selectedItemsForTemplate) {\n          // 檢核單價\n          if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n            this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\n            return false;\n          }\n          // 檢核單價是否為有效數字\n          if (isNaN(item.CUnitPrice)) {\n            this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\n            return false;\n          }\n          // 檢核單位\n          if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n            this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\n            return false;\n          }\n          // 檢核單位長度\n          if (item.CUnit.trim().length > 10) {\n            this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\n            return false;\n          }\n        }\n      }\n      return true;\n    }\n    // 建立模板\n    createTemplate(ref) {\n      let details = [];\n      if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n        // 空間模板的詳細資料\n        details = this.selectedSpacesForTemplate.map(space => ({\n          CTemplateDetailId: null,\n          CReleateId: space.CSpaceID,\n          CPart: space.CPart,\n          CLocation: space.CLocation\n        }));\n      } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n        // 項目模板的詳細資料，包含單價和單位\n        details = this.selectedItemsForTemplate.map(item => ({\n          CTemplateDetailId: null,\n          CReleateId: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          CUnitPrice: item.CUnitPrice,\n          CUnit: item.CUnit\n        }));\n      }\n      const templateData = {\n        CTemplateName: this.templateDetail.CTemplateName,\n        CTemplateType: this.templateDetail.CTemplateType,\n        CStatus: this.templateDetail.CStatus,\n        Details: details.length > 0 ? details : undefined\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('建立模板成功');\n          ref.close();\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\n        }\n      })).subscribe();\n    }\n    // 更新模板\n    updateTemplate(ref) {\n      const templateData = {\n        CTemplateId: this.templateDetail.CTemplateId,\n        CTemplateName: this.templateDetail.CTemplateName,\n        CTemplateType: this.templateDetail.CTemplateType,\n        CStatus: this.templateDetail.CStatus\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('更新模板成功');\n          ref.close();\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\n        }\n      })).subscribe();\n    }\n    // 刪除模板\n    deleteTemplate(template) {\n      if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n        this._templateService.apiTemplateDeleteTemplatePost$Json({\n          body: {\n            CTemplateId: template.CTemplateId\n          }\n        }).pipe(tap(response => {\n          if (response.StatusCode === 0) {\n            this.message.showSucessMSG('刪除模板成功');\n            this.loadTemplateList();\n          } else {\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\n          }\n        })).subscribe();\n      }\n    }\n    // 查看模板明細\n    viewTemplateDetail(template, modal) {\n      this.selectedTemplateDetail = template;\n      this.isLoadingTemplateDetail = true;\n      this.templateDetailSpaces = [];\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n      const request = {\n        templateId: template.CTemplateId\n      };\n      this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        this.isLoadingTemplateDetail = false;\n        if (response.StatusCode === 0) {\n          this.templateDetailSpaces = response.Entries?.map(item => ({\n            CReleateId: item.CReleateId,\n            CPart: item.CPart,\n            CLocation: item.CLocation\n          })) || [];\n        } else {\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n        }\n      })).subscribe();\n    }\n    // 空間選擇相關方法\n    toggleSpaceSelection(space) {\n      space.selected = !space.selected;\n      if (space.selected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n      this.updateAllSpacesSelectedState();\n    }\n    toggleAllSpaces() {\n      this.allSpacesSelected = !this.allSpacesSelected;\n      this.availableSpaces.forEach(space => {\n        space.selected = this.allSpacesSelected;\n        if (this.allSpacesSelected) {\n          if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n            this.selectedSpacesForTemplate.push({\n              ...space\n            });\n          }\n        } else {\n          this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n        }\n      });\n    }\n    removeSelectedSpace(space) {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n      if (availableSpace) {\n        availableSpace.selected = false;\n      }\n      this.updateAllSpacesSelectedState();\n    }\n    updateAllSpacesSelectedState() {\n      this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n    }\n    // 項目選擇相關方法\n    toggleItemSelection(item) {\n      item.selected = !item.selected;\n      if (item.selected) {\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n          const newItem = {\n            ...item,\n            CUnitPrice: 0,\n            // 設為0，強制用戶輸入\n            CUnit: '' // 設為空，強制用戶輸入\n          };\n          this.selectedItemsForTemplate.push(newItem);\n        }\n      } else {\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n      }\n      this.updateAllItemsSelectedState();\n    }\n    toggleAllItems() {\n      this.allItemsSelected = !this.allItemsSelected;\n      this.availableItemsForTemplate.forEach(item => {\n        item.selected = this.allItemsSelected;\n        if (this.allItemsSelected) {\n          if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n            // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n            const newItem = {\n              ...item,\n              CUnitPrice: 0,\n              // 設為0，強制用戶輸入\n              CUnit: '' // 設為空，強制用戶輸入\n            };\n            this.selectedItemsForTemplate.push(newItem);\n          }\n        } else {\n          this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n        }\n      });\n    }\n    removeSelectedItem(item) {\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n      const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n      if (availableItem) {\n        availableItem.selected = false;\n      }\n      this.updateAllItemsSelectedState();\n    }\n    updateAllItemsSelectedState() {\n      this.allItemsSelected = this.availableItemsForTemplate.length > 0 && this.availableItemsForTemplate.every(item => item.selected);\n    }\n    // 更新選中項目的單價和單位\n    updateItemPrice(item, price) {\n      // 確保價格為有效數字且大於0\n      if (isNaN(price) || price < 0) {\n        price = 0;\n      }\n      item.CUnitPrice = price;\n      // 同步更新 availableItemsForTemplate 中對應項目的單價\n      const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n      if (availableItem) {\n        availableItem.CUnitPrice = price;\n      }\n    }\n    // 處理單價變更事件\n    onPriceChange(item, value) {\n      // 確保值為數字類型\n      const numericValue = typeof value === 'string' ? parseFloat(value) : value;\n      item.CUnitPrice = isNaN(numericValue) ? 0 : numericValue;\n    }\n    updateItemUnit(item, unit) {\n      // 清理單位字串\n      unit = unit ? unit.trim() : '';\n      item.CUnit = unit;\n      // 同步更新 availableItemsForTemplate 中對應項目的單位\n      const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n      if (availableItem) {\n        availableItem.CUnit = unit;\n      }\n    }\n    // 處理空間選擇變更\n    onSpaceSelectionChange(selectedSpaces) {\n      this.selectedSpacesForTemplate = selectedSpaces.map(space => ({\n        CSpaceID: space.CSpaceID,\n        CPart: space.CPart,\n        CLocation: space.CLocation,\n        selected: true\n      }));\n    }\n    // 處理項目選擇變更\n    onItemSelectionChange(selectedItems) {\n      this.selectedItemsForTemplate = selectedItems.map(item => ({\n        CSpaceID: item.CSpaceID,\n        CPart: item.CPart,\n        CLocation: item.CLocation,\n        selected: true,\n        CUnitPrice: 0,\n        // 預設為0，強制用戶輸入\n        CUnit: '' // 預設為空，強制用戶輸入\n      }));\n    }\n    // 統一的選擇變更處理方法\n    onSelectionChange(selectedItems) {\n      if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n        this.onSpaceSelectionChange(selectedItems);\n      } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n        this.onItemSelectionChange(selectedItems);\n      }\n    }\n    // 檢查項目是否有效（用於UI顯示）\n    isItemValid(item) {\n      // 檢核單價\n      if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n        return false;\n      }\n      // 檢核單價是否為有效數字\n      if (isNaN(item.CUnitPrice)) {\n        return false;\n      }\n      // 檢核單位\n      if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n        return false;\n      }\n      // 檢核單位長度\n      if (item.CUnit.trim().length > 10) {\n        return false;\n      }\n      return true;\n    }\n    // 獲取已完成設定的項目數量\n    getValidItemsCount() {\n      return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\n    }\n    static {\n      this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TemplateComponent,\n        selectors: [[\"ngx-template\"]],\n        viewQuery: function TemplateComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 78,\n        vars: 15,\n        consts: [[\"templateModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"templateType\", 1, \"label\", \"col-3\"], [\"id\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 2, \"width\", \"120px\"], [\"scope\", \"col\", 2, \"width\", \"200px\"], [\"scope\", \"col\", 2, \"width\", \"100px\"], [\"scope\", \"col\", 2, \"width\", \"180px\"], [\"scope\", \"col\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"row\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ms-2\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [\"for\", \"templateType\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"class\", \"form-control d-flex align-items-center\", \"style\", \"height: 42px; border-radius: 6px; background-color: #f8f9fa;\", 4, \"ngIf\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\", 2, \"height\", \"42px\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"form-control\", \"d-flex\", \"align-items-center\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"background-color\", \"#f8f9fa\"], [1, \"fas\", \"fa-info-circle\", \"text-info\", \"me-2\"], [\"id\", \"templateType\", \"name\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u985E\\u578B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-home\", \"text-primary\", \"me-2\"], [1, \"fas\", \"fa-list\", \"text-success\", \"me-2\"], [1, \"col-md-12\", \"d-flex\", \"align-items-start\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"mb-3\"], [3, \"selectionChange\", \"selectedItems\", \"multiple\"], [\"class\", \"selected-items-config\", 4, \"ngIf\"], [1, \"selected-items-config\"], [1, \"config-header\", \"mb-3\"], [1, \"fas\", \"fa-cog\", \"me-2\"], [1, \"badge\", \"badge-info\", \"ms-2\"], [1, \"text-muted\"], [1, \"config-items\"], [\"class\", \"config-item mb-3 p-3 border rounded\", 3, \"valid\", \"invalid\", 4, \"ngFor\", \"ngForOf\"], [1, \"config-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"summary-item\"], [1, \"font-weight-bold\", \"text-primary\"], [1, \"font-weight-bold\"], [1, \"progress\", \"mt-2\", 2, \"height\", \"6px\"], [1, \"progress-bar\", \"bg-success\"], [1, \"config-item\", \"mb-3\", \"p-3\", \"border\", \"rounded\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-4\"], [1, \"item-info\"], [1, \"item-name\", \"font-weight-bold\"], [1, \"fas\", \"fa-cube\", \"me-2\", \"text-primary\"], [1, \"item-location\", \"text-muted\", \"small\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-1\"], [1, \"col-md-3\"], [1, \"form-label\", \"small\", \"text-muted\", \"mb-1\"], [1, \"text-danger\"], [\"type\", \"number\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", \"min\", \"0\", \"step\", \"0.01\", 2, \"text-align\", \"right\", 3, \"ngModelChange\", \"ngModel\"], [\"nbSuffix\", \"\", \"icon\", \"dollar-sign-outline\", 1, \"text-success\"], [\"class\", \"invalid-feedback small\", 4, \"ngIf\"], [\"type\", \"text\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u4F4D\", \"maxlength\", \"10\", 3, \"ngModelChange\", \"ngModel\"], [\"nbSuffix\", \"\", \"icon\", \"tag-outline\", 1, \"text-info\"], [1, \"col-md-2\", \"text-end\"], [\"type\", \"button\", \"title\", \"\\u79FB\\u9664\\u6B64\\u9805\\u76EE\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"validation-status\", \"mt-2\"], [\"class\", \"text-success small\", 4, \"ngIf\"], [\"class\", \"text-warning small\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"small\"], [1, \"text-success\", \"small\"], [1, \"fas\", \"fa-check-circle\", \"me-1\"], [1, \"text-warning\", \"small\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"text-muted\", \"py-4\"]],\n        template: function TemplateComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 3)(5, \"div\", 4);\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵelementStart(7, \"div\")(8, \"p\", 6);\n            i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n            i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"nb-form-field\", 11)(16, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9)(19, \"label\", 13);\n            i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"nb-form-field\", 11)(22, \"nb-select\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementStart(23, \"nb-option\", 15);\n            i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"nb-option\", 15);\n            i0.ɵɵtext(26, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"nb-option\", 15);\n            i0.ɵɵtext(28, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(29, \"div\", 8)(30, \"div\", 9)(31, \"label\", 16);\n            i0.ɵɵtext(32, \"\\u6A21\\u677F\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"nb-form-field\", 11)(34, \"nb-select\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchTemplateType, $event) || (ctx.searchTemplateType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_34_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementStart(35, \"nb-option\", 15);\n            i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"nb-option\", 15);\n            i0.ɵɵtext(38, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"nb-option\", 15);\n            i0.ɵɵtext(40, \"\\u9805\\u76EE\\u6A21\\u677F\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(41, \"div\", 8);\n            i0.ɵɵelementStart(42, \"div\", 18)(43, \"div\", 19)(44, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_44_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onReset());\n            });\n            i0.ɵɵelement(45, \"i\", 21);\n            i0.ɵɵtext(46, \"\\u91CD\\u7F6E \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_47_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelement(48, \"i\", 23);\n            i0.ɵɵtext(49, \"\\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(50, \"div\", 18)(51, \"div\", 24);\n            i0.ɵɵtemplate(52, TemplateComponent_button_52_Template, 3, 0, \"button\", 25);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 26)(54, \"table\", 27)(55, \"thead\")(56, \"tr\")(57, \"th\", 28);\n            i0.ɵɵtext(58, \"\\u6A21\\u677F\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"th\", 29);\n            i0.ɵɵtext(60, \"\\u6A21\\u677F\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"th\", 30);\n            i0.ɵɵtext(62, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"th\", 31);\n            i0.ɵɵtext(64, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"th\", 28);\n            i0.ɵɵtext(66, \"\\u5EFA\\u7ACB\\u8005\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"th\", 32);\n            i0.ɵɵtext(68, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(69, \"tbody\");\n            i0.ɵɵtemplate(70, TemplateComponent_tr_70_Template, 19, 11, \"tr\", 33)(71, TemplateComponent_tr_71_Template, 4, 0, \"tr\", 34);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(72, \"nb-card-footer\", 35)(73, \"ngx-pagination\", 36);\n            i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(74, TemplateComponent_ng_template_74_Template, 47, 19, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(76, TemplateComponent_ng_template_76_Template, 75, 18, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTemplateType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NumberValueAccessor, i7.NgControlStatus, i7.MaxLengthValidator, i7.MinValidator, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbSuffixDirective, i2.NbIconComponent, i8.BreadcrumbComponent, i9.PaginationComponent, SpacePickerComponent],\n        styles: [\"@charset \\\"UTF-8\\\";.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:.25rem}.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child{margin-right:0}.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-top-left-radius:0;border-bottom-left-radius:0}nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:var(--color-fg-heading)}.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:.25rem}.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child{margin-right:0}.badge.badge-success[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.badge.badge-secondary[_ngcontent-%COMP%]{background-color:#6c757d;color:#fff}.badge.badge-info[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}.badge.badge-primary[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.required-field[_ngcontent-%COMP%]:after{content:\\\" *\\\";color:#dc3545}.alert[_ngcontent-%COMP%]{border-radius:.375rem}.modal-content[_ngcontent-%COMP%]{border-radius:.5rem;box-shadow:0 .5rem 1rem #00000026}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff}.btn-close-white[_ngcontent-%COMP%]{filter:invert(1) grayscale(100%) brightness(200%)}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset{border-bottom:2px solid #f1f3f4;margin-bottom:0}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab{padding:0;margin-right:8px}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link{padding:14px 24px;border:none;border-radius:8px 8px 0 0;background-color:transparent;color:#6c757d;font-weight:500;font-size:.95rem;transition:all .3s ease;position:relative}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link:hover{background-color:#f8f9fa;color:#495057}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active{background-color:#007bff;color:#fff;font-weight:600;box-shadow:0 2px 8px #007bff33}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;right:0;height:2px;background-color:#007bff}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content{padding:0;border:none;background:transparent}.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content .tab-pane.active{display:block}.space-grid[_ngcontent-%COMP%]{display:grid!important;grid-template-columns:repeat(5,1fr);gap:16px;margin-bottom:20px;width:100%;border:1px dashed #ccc}.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]{cursor:pointer;transition:all .3s ease;display:block;width:100%}.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]{padding:16px;border:2px solid #e9ecef!important;border-radius:12px;background-color:#fff!important;transition:all .3s ease;min-height:80px;display:flex!important;flex-direction:column;justify-content:center;box-shadow:0 2px 4px #0000000d;position:relative;width:100%;box-sizing:border-box}.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50!important;font-size:.95rem;line-height:1.4;margin-bottom:4px;text-align:center;display:block}.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d!important;line-height:1.3;font-weight:400;text-align:center;display:block}.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%]{border-color:#007bff;background-color:#f8f9ff;transform:translateY(-2px);box-shadow:0 4px 12px #007bff26}.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]{border-color:#007bff;background:linear-gradient(135deg,#e7f3ff,#cce7ff)}.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%]{color:#0056b3;font-weight:700}.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%]{color:#495057}.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]:after{content:\\\"\\\\2713\\\";position:absolute;top:8px;right:8px;width:20px;height:20px;background-color:#007bff;color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700}@media (max-width: 1200px){.space-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(4,1fr)}}@media (max-width: 992px){.space-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}}@media (max-width: 768px){.space-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:8px}}@media (max-width: 576px){.space-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]{border-bottom:1px solid #e9ecef;padding-bottom:12px;margin-bottom:20px}.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-size:1.1rem}.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;padding:4px 8px}.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.85rem;line-height:1.4}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #e9ecef!important;border-radius:8px;transition:all .3s ease;position:relative}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.valid[_ngcontent-%COMP%]{border-color:#28a745!important;background-color:#f8fff9}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.valid[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:0;bottom:0;width:4px;background-color:#28a745;border-radius:4px 0 0 4px}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.invalid[_ngcontent-%COMP%]{border-color:#ffc107!important;background-color:#fffbf0}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.invalid[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:0;bottom:0;width:4px;background-color:#ffc107;border-radius:4px 0 0 4px}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%]{font-size:.95rem;color:#2c3e50;margin-bottom:4px}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-location[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-location[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-weight:600;color:#495057;margin-bottom:6px}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%]{font-weight:700}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545;box-shadow:0 0 0 .2rem #dc354540}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%]{color:#dc3545;font-size:.8rem;margin-top:4px;display:block}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]{font-size:.8rem;font-weight:500}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%]{color:#28a745!important}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   .text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]{background-color:#f8f9fa!important;border:1px solid #e9ecef!important;border-radius:8px}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]{font-size:.9rem}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold[_ngcontent-%COMP%]{font-weight:600}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold.text-primary[_ngcontent-%COMP%]{color:#007bff!important}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold.text-success[_ngcontent-%COMP%]{color:#28a745!important}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%]{background-color:#e9ecef;border-radius:3px;overflow:hidden}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{transition:width .3s ease}@media (max-width: 768px){.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%], .selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], .selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-2[_ngcontent-%COMP%]{margin-bottom:12px}.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%]{margin-bottom:8px}}\"]\n      });\n    }\n  }\n  return TemplateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}