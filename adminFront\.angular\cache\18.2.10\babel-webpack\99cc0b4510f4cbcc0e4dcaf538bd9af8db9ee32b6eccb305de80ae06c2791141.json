{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref, spaceTemplateSelectorService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    // 批次編輯相關屬性\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.isBatchEditMode = false;\n    // 批次編輯時的項目資料副本\n    this.batchEditItems = [];\n    // 批次編輯配置\n    this.batchEditConfig = this.getDefaultBatchEditConfig();\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CLocation = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 取得預設批次編輯配置\n  getDefaultBatchEditConfig() {\n    return {\n      title: '批次編輯建案需求',\n      noticeText: '您可以個別修改每個項目的欄位',\n      noticeItems: ['工程項目、類型、排序、狀態、單價、單位為必填欄位', '排序和單價不能為負數', '區域最多20個字，工程項目最多50個字，備註說明最多100個字'],\n      confirmButtonText: '確定批次更新',\n      cancelButtonText: '取消'\n    };\n  }\n  // 取得模板新增批次編輯配置\n  getTemplateAddBatchEditConfig() {\n    return {\n      title: '模板新增批次編輯',\n      noticeText: '從模板載入的項目，您可以個別修改每個項目的欄位',\n      noticeItems: ['工程項目、類型、排序、狀態、單價、單位為必填欄位', '排序和單價不能為負數', '區域最多20個字，工程項目最多50個字，備註說明最多100個字', '模板項目已自動填入預設值，請檢查並調整各項目設定'],\n      confirmButtonText: '確定新增項目',\n      cancelButtonText: '取消'\n    };\n  }\n  // 建案切換事件處理\n  onBuildCaseChange(newBuildCaseId) {\n    // 如果在批次編輯模式下切換建案，給予警告\n    if (this.isBatchEditMode || this.selectedItems.length > 0) {\n      const confirmMessage = this.isBatchEditMode ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？' : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\n      if (!confirm(confirmMessage)) {\n        // 使用者取消，恢復原來的建案選擇\n        setTimeout(() => {\n          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\n        }, 0);\n        return;\n      }\n    }\n    // 重置批次選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 更新當前建案並重新載入資料\n    this.currentBuildCase = newBuildCaseId;\n    this.getList();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 清除選擇狀態和批次編輯相關狀態\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 建案頁面需要驗證建案名稱\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 數值範圍驗證\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\n      this.valid.errorMessages.push('[排序] 不能為負數');\n    }\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\n      this.valid.errorMessages.push('[單價] 不能為負數');\n    }\n    // 長度驗證\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 如果有建案時才查詢\n      if (this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 建案頁面的邏輯\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n          // 清理已選擇的項目，移除不存在於新列表中的項目\n          const originalSelectedCount = this.selectedItems.length;\n          this.selectedItems = this.selectedItems.filter(selectedItem => this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID));\n          // 如果選擇的項目數量有變化，清理批次編輯狀態\n          if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\n            this.batchEditItems = [];\n            this.isBatchEditMode = false;\n          }\n          // 更新選擇狀態\n          this.updateSelectAllState();\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CLocation = res.Entries.CLocation;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  // 空間模板相關方法\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\n    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\n  }\n  onTemplateError(errorMessage) {\n    this.message.showErrorMSG(errorMessage);\n  }\n  // 需求選擇相關方法\n  onRequirementSelectionConfirmed(config) {\n    console.log('確認需求選擇配置:', config);\n    // 將選擇的需求項目加入到批次編輯\n    this.convertRequirementsToSelection(config.selectedItems);\n    this.message.showSucessMSG(`已選擇 ${config.selectedItems.length} 個需求項目`);\n  }\n  onRequirementSelectionCancelled() {\n    console.log('取消需求選擇');\n  }\n  onRequirementSelectionError(errorMessage) {\n    this.message.showErrorMSG(errorMessage);\n  }\n  // 將選擇的需求項目轉換為選擇狀態\n  convertRequirementsToSelection(selectedRequirements) {\n    if (!selectedRequirements || selectedRequirements.length === 0) {\n      return;\n    }\n    // 清除當前選擇\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    // 將選擇的需求項目加入到當前選擇列表\n    selectedRequirements.forEach(requirement => {\n      const existingItem = this.requirementList.find(item => item.CRequirementID === requirement.CRequirementID);\n      if (existingItem) {\n        this.selectedItems.push(existingItem);\n      }\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 將模板項目轉換為批次編輯項目\n  convertTemplatesToBatchEdit(selectedTemplates, templateDetails) {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('建案 ID 不存在');\n      return;\n    }\n    // 清除當前選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    // 取得預設排序值 - 從列表最大排序值開始\n    const maxSort = this.requirementList.length > 0 ? Math.max(...this.requirementList.map(item => item.CSort || 0)) : 0;\n    let currentSortIndex = 0;\n    const allBatchEditItems = [];\n    // 處理每個模板的明細項目\n    selectedTemplates.forEach(template => {\n      const details = templateDetails.get(template.CTemplateId) || [];\n      if (details && details.length > 0) {\n        // 將每個明細項目轉換為批次編輯項目\n        details.forEach((detail, detailIndex) => {\n          const batchEditItem = {\n            CRequirementID: undefined,\n            // 新項目沒有 ID\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n            CLocation: detail.CLocation || template.CLocation || '',\n            // 優先使用明細的位置，其次使用模板位置\n            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`,\n            // 使用明細名稱作為工程項目\n            CHouseType: template.CHouseType || [],\n            // 從模板獲取房屋類型，預設為空陣列\n            CSort: maxSort + currentSortIndex + 1,\n            // 從最大排序值往上遞增\n            CStatus: 1,\n            // 預設啟用\n            CUnitPrice: detail.CUnitPrice || template.CUnitPrice || 0,\n            // 優先使用明細單價，其次使用模板單價\n            CUnit: detail.CUnit || template.CUnit || '式',\n            // 優先使用明細單位，其次使用模板單位\n            CSpaceId: detail.CReleateId || template.cRelateID || null,\n            // 從明細獲取關聯空間 ID\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n            // 預設不顯示在預約需求\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n            // 預設不是簡易客變\n            CRemark: detail.CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\n          };\n          allBatchEditItems.push(batchEditItem);\n          currentSortIndex++;\n        });\n      } else {\n        // 如果模板沒有明細，則將模板本身作為一個項目\n        const batchEditItem = {\n          CRequirementID: undefined,\n          // 新項目沒有 ID\n          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n          CLocation: template.CLocation || '',\n          // 從模板獲取區域資訊\n          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`,\n          // 使用模板名稱作為工程項目\n          CHouseType: template.CHouseType || [],\n          // 從模板獲取房屋類型，預設為空陣列\n          CSort: maxSort + currentSortIndex + 1,\n          // 從最大排序值往上遞增\n          CStatus: 1,\n          // 預設啟用\n          CUnitPrice: template.CUnitPrice || 0,\n          // 從模板獲取單價，預設為 0\n          CUnit: template.CUnit || '式',\n          // 從模板獲取單位，預設為 '式'\n          CSpaceId: template.cRelateID || null,\n          // 從模板獲取關聯空間 ID\n          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n          // 預設不顯示在預約需求\n          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n          // 預設不是簡易客變\n          CRemark: template.CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\n        };\n        allBatchEditItems.push(batchEditItem);\n        currentSortIndex++;\n      }\n    });\n    // 設定批次編輯項目\n    this.batchEditItems = allBatchEditItems;\n    // 設定為模板新增批次編輯模式，使用專用配置\n    this.batchEditConfig = this.getTemplateAddBatchEditConfig();\n    this.isBatchEditMode = true;\n    // 計算總明細數量\n    const totalDetailCount = selectedTemplates.reduce((sum, template) => {\n      const details = templateDetails.get(template.CTemplateId) || [];\n      return sum + (details.length || 1);\n    }, 0);\n    // 直接開啟批次編輯對話框\n    setTimeout(() => {\n      this.dialogService.open(this.batchEditDialog);\n    }, 100);\n  } // 批次編輯相關方法\n  // 切換單一項目選擇狀態\n  toggleItemSelection(item) {\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n    } else {\n      this.selectedItems.push(item);\n    }\n    this.updateSelectAllState();\n  }\n  // 切換全選狀態\n  toggleSelectAll(newValue) {\n    if (this.requirementList.length === 0) {\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      return;\n    }\n    // 更新 isAllSelected 狀態\n    this.isAllSelected = newValue;\n    // 根據新值更新 selectedItems\n    if (this.isAllSelected) {\n      this.selectedItems = [...this.requirementList];\n    } else {\n      this.selectedItems = [];\n    }\n  }\n  // 更新全選狀態\n  updateSelectAllState() {\n    if (this.requirementList.length === 0) {\n      this.isAllSelected = false;\n    } else {\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\n    }\n  }\n  // 檢查項目是否被選中\n  isItemSelected(item) {\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\n  }\n  // 開啟批次編輯對話框\n  openBatchEdit(dialog, config) {\n    if (this.selectedItems.length === 0) {\n      this.message.showErrorMSG('請先選擇要編輯的項目');\n      return;\n    }\n    // 設定批次編輯配置\n    this.batchEditConfig = config || this.getDefaultBatchEditConfig();\n    this.isBatchEditMode = true;\n    // 初始化批次編輯項目資料\n    this.batchEditItems = this.selectedItems.map(item => ({\n      CRequirementID: item.CRequirementID,\n      CBuildCaseID: item.CBuildCaseID,\n      CLocation: item.CLocation,\n      CRequirement: item.CRequirement,\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\n      CSort: item.CSort,\n      CStatus: item.CStatus,\n      CUnitPrice: item.CUnitPrice || 0,\n      CUnit: item.CUnit,\n      CSpaceId: item.CSpaceId || null,\n      CIsShow: item.CIsShow || false,\n      CIsSimple: item.CIsSimple || false,\n      CRemark: item.CRemark\n    }));\n    this.dialogService.open(dialog);\n  }\n  // 批次驗證方法\n  batchValidation() {\n    const errorMessages = [];\n    this.batchEditItems.forEach((item, index) => {\n      const itemNum = index + 1;\n      // 必填欄位檢核\n      if (!item.CBuildCaseID) {\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\n      }\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\n      }\n      if (!item.CHouseType || item.CHouseType.length === 0) {\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\n      }\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\n      }\n      if (item.CStatus === null || item.CStatus === undefined) {\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\n      }\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\n      }\n      if (!item.CUnit || item.CUnit.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\n      }\n      // 長度驗證\n      if (item.CLocation && item.CLocation.length > 20) {\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\n      }\n      if (item.CRequirement && item.CRequirement.length > 50) {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\n      }\n      if (item.CRemark && item.CRemark.length > 100) {\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\n      }\n    });\n    return errorMessages;\n  }\n  // 批次保存\n  batchSave(ref) {\n    if (this.batchEditItems.length === 0) {\n      this.message.showErrorMSG('沒有要更新的項目');\n      return;\n    }\n    // 執行批次驗證\n    const validationErrors = this.batchValidation();\n    if (validationErrors.length > 0) {\n      this.message.showErrorMSGs(validationErrors);\n      return;\n    }\n    // 分離新增項目和更新項目\n    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\n    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\n    // 建立請求陣列\n    const requests = [];\n    // 新增項目的請求\n    newItems.forEach(item => {\n      const newItemData = {\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType,\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId,\n        CIsShow: item.CIsShow,\n        CIsSimple: item.CIsSimple,\n        CRemark: item.CRemark\n      };\n      requests.push(this.requirementService.apiRequirementSaveDataPost$Json({\n        body: newItemData\n      }).toPromise());\n    });\n    // 更新項目的請求（如果有的話）\n    if (updateItems.length > 0) {\n      const updateData = updateItems.map(item => ({\n        CRequirementID: item.CRequirementID,\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType,\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId,\n        CIsShow: item.CIsShow,\n        CIsSimple: item.CIsSimple,\n        CRemark: item.CRemark\n      }));\n      requests.push(this.requirementService.apiRequirementBatchSaveDataPost$Json({\n        body: updateData\n      }).toPromise());\n    }\n    // 執行所有請求\n    Promise.all(requests).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      const totalItems = this.batchEditItems.length;\n      if (successCount === responses.length) {\n        this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\n      } else {\n        this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\n      }\n      // 清理狀態並重新載入資料\n      this.selectedItems = [];\n      this.batchEditItems = [];\n      this.isBatchEditMode = false;\n      this.updateSelectAllState();\n      this.getList();\n      ref.close();\n    }).catch(error => {\n      console.error('批次保存失敗:', error);\n      this.message.showErrorMSG('批次保存時發生錯誤');\n    });\n  }\n  // 重置批次編輯中的單一項目到原始狀態\n  resetBatchEditItem(index) {\n    const originalItem = this.selectedItems[index];\n    if (originalItem) {\n      this.batchEditItems[index] = {\n        CRequirementID: originalItem.CRequirementID,\n        CBuildCaseID: originalItem.CBuildCaseID,\n        CLocation: originalItem.CLocation,\n        CRequirement: originalItem.CRequirement,\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\n        CSort: originalItem.CSort,\n        CStatus: originalItem.CStatus,\n        CUnitPrice: originalItem.CUnitPrice || 0,\n        CUnit: originalItem.CUnit,\n        CSpaceId: originalItem.CSpaceId || null,\n        CIsShow: originalItem.CIsShow || false,\n        CIsSimple: originalItem.CIsSimple || false,\n        CRemark: originalItem.CRemark\n      };\n    }\n  }\n  // 取消批次編輯\n  cancelBatchEdit(ref) {\n    this.isBatchEditMode = false;\n    this.batchEditItems = [];\n    ref.close();\n  }\n  // 備用：如果需要手動建立需求項目的方法\n  batchCreateRequirements(requirements) {\n    const batchRequests = requirements.map(requirement => this.requirementService.apiRequirementSaveDataPost$Json({\n      body: requirement\n    }));\n    Promise.all(batchRequests.map(req => req.toPromise())).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\n      this.getList();\n    }).catch(error => {\n      console.error('批次建立需求失敗:', error);\n      this.message.showErrorMSG('批次建立需求時發生錯誤');\n    });\n  }\n  // 導航到模板管理頁面\n  navigateToTemplate() {\n    this.router.navigate(['/pages/template']);\n  }\n};\n__decorate([ViewChild('batchEditDialog', {\n  static: false\n})], RequirementManagementComponent.prototype, \"batchEditDialog\", void 0);\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, SpaceTemplateSelectorButtonComponent, RequirementTemplateSelectorButtonComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "SpaceTemplateSelectorButtonComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "spaceTemplateSelectorService", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "saveRequirement", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "selectedItems", "isAllSelected", "isBatchEditMode", "batchEditItems", "batchEditConfig", "getDefaultBatchEditConfig", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CIsSimple", "CRequirement", "CLocation", "map", "type", "title", "noticeText", "noticeItems", "confirmButtonText", "cancelButtonText", "getTemplateAddBatchEditConfig", "onBuildCaseChange", "newBuildCaseId", "length", "confirmMessage", "confirm", "setTimeout", "CBuildCaseID", "getList", "resetSearch", "cID", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "CSort", "CUnitPrice", "CUnit", "undefined", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "originalSelectedCount", "filter", "selectedItem", "some", "listItem", "updateSelectAllState", "apiRequirementGetDataPost$Json", "CSpaceId", "onHouseTypeChange", "checked", "includes", "v", "getCIsShowText", "getCIsSimpleText", "onSpaceTemplateApplied", "config", "convertTemplatesToBatchEdit", "templateDetails", "onTemplateError", "errorMessage", "onRequirementSelectionConfirmed", "convertRequirementsToSelection", "onRequirementSelectionCancelled", "onRequirementSelectionError", "selectedRequirements", "requirement", "existingItem", "item", "selectedTemplates", "maxSort", "Math", "max", "currentSortIndex", "allBatchEditItems", "template", "details", "get", "CTemplateId", "detail", "detailIndex", "batchEditItem", "<PERSON>art", "CTemplateName", "CReleateId", "cRelateID", "totalDetailCount", "reduce", "sum", "batchEditDialog", "toggleItemSelection", "index", "findIndex", "selected", "splice", "toggleSelectAll", "newValue", "isItemSelected", "openBatchEdit", "batchValidation", "itemNum", "trim", "batchSave", "validationErrors", "newItems", "updateItems", "requests", "newItemData", "to<PERSON>romise", "updateData", "apiRequirementBatchSaveDataPost$Json", "Promise", "all", "then", "responses", "successCount", "totalItems", "catch", "resetBatchEditItem", "originalItem", "cancelBatchEdit", "batchCreateRequirements", "requirements", "batchRequests", "req", "navigateToTemplate", "navigate", "__decorate", "static", "selector", "standalone", "imports", "RequirementTemplateSelectorButtonComponent", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';\r\nimport { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\r\n\r\n// 批次編輯配置介面\r\nexport interface BatchEditConfig {\r\n  title: string;\r\n  noticeText: string;\r\n  noticeItems: string[];\r\n  confirmButtonText: string;\r\n  cancelButtonText: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    SpaceTemplateSelectorButtonComponent,\r\n    RequirementTemplateSelectorButtonComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('batchEditDialog', { static: false }) batchEditDialog!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef,\r\n    private spaceTemplateSelectorService: SpaceTemplateSelectorService\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  // 批次編輯相關屬性\r\n  selectedItems: GetRequirement[] = [];\r\n  isAllSelected = false;\r\n  isBatchEditMode = false;\r\n  // 批次編輯時的項目資料副本\r\n  batchEditItems: (SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean })[] = [];\r\n  // 批次編輯配置\r\n  batchEditConfig: BatchEditConfig = this.getDefaultBatchEditConfig();\r\n\r\n  override ngOnInit(): void { }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CLocation = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 取得預設批次編輯配置\r\n  getDefaultBatchEditConfig(): BatchEditConfig {\r\n    return {\r\n      title: '批次編輯建案需求',\r\n      noticeText: '您可以個別修改每個項目的欄位',\r\n      noticeItems: [\r\n        '工程項目、類型、排序、狀態、單價、單位為必填欄位',\r\n        '排序和單價不能為負數',\r\n        '區域最多20個字，工程項目最多50個字，備註說明最多100個字'\r\n      ],\r\n      confirmButtonText: '確定批次更新',\r\n      cancelButtonText: '取消'\r\n    };\r\n  }\r\n\r\n  // 取得模板新增批次編輯配置\r\n  getTemplateAddBatchEditConfig(): BatchEditConfig {\r\n    return {\r\n      title: '模板新增批次編輯',\r\n      noticeText: '從模板載入的項目，您可以個別修改每個項目的欄位',\r\n      noticeItems: [\r\n        '工程項目、類型、排序、狀態、單價、單位為必填欄位',\r\n        '排序和單價不能為負數',\r\n        '區域最多20個字，工程項目最多50個字，備註說明最多100個字',\r\n        '模板項目已自動填入預設值，請檢查並調整各項目設定'\r\n      ],\r\n      confirmButtonText: '確定新增項目',\r\n      cancelButtonText: '取消'\r\n    };\r\n  }\r\n\r\n  // 建案切換事件處理\r\n  onBuildCaseChange(newBuildCaseId: any) {\r\n    // 如果在批次編輯模式下切換建案，給予警告\r\n    if (this.isBatchEditMode || this.selectedItems.length > 0) {\r\n      const confirmMessage = this.isBatchEditMode\r\n        ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？'\r\n        : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\r\n\r\n      if (!confirm(confirmMessage)) {\r\n        // 使用者取消，恢復原來的建案選擇\r\n        setTimeout(() => {\r\n          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\r\n        }, 0);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 重置批次選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n\r\n    // 更新當前建案並重新載入資料\r\n    this.currentBuildCase = newBuildCaseId;\r\n    this.getList();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 清除選擇狀態和批次編輯相關狀態\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n\r\n    // 建案頁面需要驗證建案名稱\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 數值範圍驗證\r\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\r\n      this.valid.errorMessages.push('[排序] 不能為負數');\r\n    }\r\n\r\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\r\n      this.valid.errorMessages.push('[單價] 不能為負數');\r\n    }\r\n\r\n    // 長度驗證\r\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\r\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 如果有建案時才查詢\r\n        if (this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n\r\n    // 建案頁面的邏輯\r\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n\r\n            // 清理已選擇的項目，移除不存在於新列表中的項目\r\n            const originalSelectedCount = this.selectedItems.length;\r\n            this.selectedItems = this.selectedItems.filter(selectedItem =>\r\n              this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID)\r\n            );\r\n\r\n            // 如果選擇的項目數量有變化，清理批次編輯狀態\r\n            if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\r\n              this.batchEditItems = [];\r\n              this.isBatchEditMode = false;\r\n            }\r\n\r\n            // 更新選擇狀態\r\n            this.updateSelectAllState();\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CLocation = res.Entries.CLocation;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\r\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\r\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // 空間模板相關方法\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\r\n    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\r\n  }\r\n\r\n  onTemplateError(errorMessage: string) {\r\n    this.message.showErrorMSG(errorMessage);\r\n  }\r\n\r\n  // 需求選擇相關方法\r\n  onRequirementSelectionConfirmed(config: RequirementSelectionConfig) {\r\n    console.log('確認需求選擇配置:', config);\r\n\r\n    // 將選擇的需求項目加入到批次編輯\r\n    this.convertRequirementsToSelection(config.selectedItems);\r\n\r\n    this.message.showSucessMSG(`已選擇 ${config.selectedItems.length} 個需求項目`);\r\n  }\r\n\r\n  onRequirementSelectionCancelled() {\r\n    console.log('取消需求選擇');\r\n  }\r\n\r\n  onRequirementSelectionError(errorMessage: string) {\r\n    this.message.showErrorMSG(errorMessage);\r\n  }\r\n\r\n  // 將選擇的需求項目轉換為選擇狀態\r\n  private convertRequirementsToSelection(selectedRequirements: any[]) {\r\n    if (!selectedRequirements || selectedRequirements.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // 清除當前選擇\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n\r\n    // 將選擇的需求項目加入到當前選擇列表\r\n    selectedRequirements.forEach(requirement => {\r\n      const existingItem = this.requirementList.find(item =>\r\n        item.CRequirementID === requirement.CRequirementID\r\n      );\r\n\r\n      if (existingItem) {\r\n        this.selectedItems.push(existingItem);\r\n      }\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 將模板項目轉換為批次編輯項目\r\n  private convertTemplatesToBatchEdit(selectedTemplates: any[], templateDetails: Map<number, any[]>) {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('建案 ID 不存在');\r\n      return;\r\n    }\r\n\r\n    // 清除當前選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n\r\n    // 取得預設排序值 - 從列表最大排序值開始\r\n    const maxSort = this.requirementList.length > 0\r\n      ? Math.max(...this.requirementList.map(item => item.CSort || 0))\r\n      : 0;\r\n\r\n    let currentSortIndex = 0;\r\n    const allBatchEditItems: any[] = [];\r\n\r\n    // 處理每個模板的明細項目\r\n    selectedTemplates.forEach(template => {\r\n      const details = templateDetails.get(template.CTemplateId) || [];\r\n\r\n      if (details && details.length > 0) {\r\n        // 將每個明細項目轉換為批次編輯項目\r\n        details.forEach((detail, detailIndex) => {\r\n          const batchEditItem = {\r\n            CRequirementID: undefined, // 新項目沒有 ID\r\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n            CLocation: detail.CLocation || template.CLocation || '', // 優先使用明細的位置，其次使用模板位置\r\n            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`, // 使用明細名稱作為工程項目\r\n            CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列\r\n            CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增\r\n            CStatus: 1, // 預設啟用\r\n            CUnitPrice: (detail as any).CUnitPrice || (template as any).CUnitPrice || 0, // 優先使用明細單價，其次使用模板單價\r\n            CUnit: (detail as any).CUnit || (template as any).CUnit || '式', // 優先使用明細單位，其次使用模板單位\r\n            CSpaceId: detail.CReleateId || (template as any).cRelateID || null, // 從明細獲取關聯空間 ID\r\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求\r\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變\r\n            CRemark: (detail as any).CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\r\n          };\r\n\r\n          allBatchEditItems.push(batchEditItem);\r\n          currentSortIndex++;\r\n        });\r\n      } else {\r\n        // 如果模板沒有明細，則將模板本身作為一個項目\r\n        const batchEditItem = {\r\n          CRequirementID: undefined, // 新項目沒有 ID\r\n          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n          CLocation: template.CLocation || '', // 從模板獲取區域資訊\r\n          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`, // 使用模板名稱作為工程項目\r\n          CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列\r\n          CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增\r\n          CStatus: 1, // 預設啟用\r\n          CUnitPrice: (template as any).CUnitPrice || 0, // 從模板獲取單價，預設為 0\r\n          CUnit: (template as any).CUnit || '式', // 從模板獲取單位，預設為 '式'\r\n          CSpaceId: (template as any).cRelateID || null, // 從模板獲取關聯空間 ID\r\n          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求\r\n          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變\r\n          CRemark: (template as any).CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\r\n        };\r\n\r\n        allBatchEditItems.push(batchEditItem);\r\n        currentSortIndex++;\r\n      }\r\n    });\r\n\r\n    // 設定批次編輯項目\r\n    this.batchEditItems = allBatchEditItems;\r\n\r\n    // 設定為模板新增批次編輯模式，使用專用配置\r\n    this.batchEditConfig = this.getTemplateAddBatchEditConfig();\r\n    this.isBatchEditMode = true;\r\n\r\n    // 計算總明細數量\r\n    const totalDetailCount = selectedTemplates.reduce((sum, template) => {\r\n      const details = templateDetails.get(template.CTemplateId) || [];\r\n      return sum + (details.length || 1);\r\n    }, 0);\r\n\r\n    // 直接開啟批次編輯對話框\r\n    setTimeout(() => {\r\n      this.dialogService.open(this.batchEditDialog);\r\n    }, 100);\r\n  }  // 批次編輯相關方法\r\n\r\n  // 切換單一項目選擇狀態\r\n  toggleItemSelection(item: GetRequirement) {\r\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\r\n    if (index > -1) {\r\n      this.selectedItems.splice(index, 1);\r\n    } else {\r\n      this.selectedItems.push(item);\r\n    }\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(newValue: boolean) {\r\n    if (this.requirementList.length === 0) {\r\n      this.selectedItems = [];\r\n      this.isAllSelected = false;\r\n      return;\r\n    }\r\n\r\n    // 更新 isAllSelected 狀態\r\n    this.isAllSelected = newValue;\r\n\r\n    // 根據新值更新 selectedItems\r\n    if (this.isAllSelected) {\r\n      this.selectedItems = [...this.requirementList];\r\n    } else {\r\n      this.selectedItems = [];\r\n    }\r\n  }\r\n\r\n  // 更新全選狀態\r\n  updateSelectAllState() {\r\n    if (this.requirementList.length === 0) {\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否被選中\r\n  isItemSelected(item: GetRequirement): boolean {\r\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\r\n  }\r\n\r\n  // 開啟批次編輯對話框\r\n  openBatchEdit(dialog: TemplateRef<any>, config?: BatchEditConfig) {\r\n    if (this.selectedItems.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要編輯的項目');\r\n      return;\r\n    }\r\n\r\n    // 設定批次編輯配置\r\n    this.batchEditConfig = config || this.getDefaultBatchEditConfig();\r\n    this.isBatchEditMode = true;\r\n\r\n    // 初始化批次編輯項目資料\r\n    this.batchEditItems = this.selectedItems.map(item => ({\r\n      CRequirementID: item.CRequirementID,\r\n      CBuildCaseID: item.CBuildCaseID,\r\n      CLocation: item.CLocation,\r\n      CRequirement: item.CRequirement,\r\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\r\n      CSort: item.CSort,\r\n      CStatus: item.CStatus,\r\n      CUnitPrice: item.CUnitPrice || 0,\r\n      CUnit: item.CUnit,\r\n      CSpaceId: item.CSpaceId || null,\r\n      CIsShow: item.CIsShow || false,\r\n      CIsSimple: item.CIsSimple || false,\r\n      CRemark: item.CRemark\r\n    }));\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 批次驗證方法\r\n  batchValidation(): string[] {\r\n    const errorMessages: string[] = [];\r\n\r\n    this.batchEditItems.forEach((item, index) => {\r\n      const itemNum = index + 1;\r\n\r\n      // 必填欄位檢核\r\n      if (!item.CBuildCaseID) {\r\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\r\n      }\r\n\r\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\r\n      }\r\n\r\n      if (!item.CHouseType || item.CHouseType.length === 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\r\n      }\r\n\r\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\r\n      }\r\n\r\n      if (item.CStatus === null || item.CStatus === undefined) {\r\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\r\n      }\r\n\r\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\r\n      }\r\n\r\n      if (!item.CUnit || item.CUnit.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\r\n      }\r\n\r\n      // 長度驗證\r\n      if (item.CLocation && item.CLocation.length > 20) {\r\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\r\n      }\r\n\r\n      if (item.CRequirement && item.CRequirement.length > 50) {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\r\n      }\r\n\r\n      if (item.CRemark && item.CRemark.length > 100) {\r\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\r\n      }\r\n    });\r\n\r\n    return errorMessages;\r\n  }\r\n\r\n  // 批次保存\r\n  batchSave(ref: any) {\r\n    if (this.batchEditItems.length === 0) {\r\n      this.message.showErrorMSG('沒有要更新的項目');\r\n      return;\r\n    }\r\n\r\n    // 執行批次驗證\r\n    const validationErrors = this.batchValidation();\r\n    if (validationErrors.length > 0) {\r\n      this.message.showErrorMSGs(validationErrors);\r\n      return;\r\n    }\r\n\r\n    // 分離新增項目和更新項目\r\n    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\r\n    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\r\n\r\n    // 建立請求陣列\r\n    const requests: Promise<any>[] = [];\r\n\r\n    // 新增項目的請求\r\n    newItems.forEach(item => {\r\n      const newItemData: SaveDataRequirement = {\r\n        CBuildCaseID: item.CBuildCaseID,\r\n        CLocation: item.CLocation,\r\n        CRequirement: item.CRequirement,\r\n        CHouseType: item.CHouseType,\r\n        CSort: item.CSort,\r\n        CStatus: item.CStatus,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit,\r\n        CSpaceId: item.CSpaceId,\r\n        CIsShow: item.CIsShow,\r\n        CIsSimple: item.CIsSimple,\r\n        CRemark: item.CRemark\r\n      };\r\n\r\n      requests.push(\r\n        this.requirementService.apiRequirementSaveDataPost$Json({\r\n          body: newItemData\r\n        }).toPromise()\r\n      );\r\n    });\r\n\r\n    // 更新項目的請求（如果有的話）\r\n    if (updateItems.length > 0) {\r\n      const updateData: SaveDataRequirement[] = updateItems.map(item => ({\r\n        CRequirementID: item.CRequirementID,\r\n        CBuildCaseID: item.CBuildCaseID,\r\n        CLocation: item.CLocation,\r\n        CRequirement: item.CRequirement,\r\n        CHouseType: item.CHouseType,\r\n        CSort: item.CSort,\r\n        CStatus: item.CStatus,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit,\r\n        CSpaceId: item.CSpaceId,\r\n        CIsShow: item.CIsShow,\r\n        CIsSimple: item.CIsSimple,\r\n        CRemark: item.CRemark\r\n      }));\r\n\r\n      requests.push(\r\n        this.requirementService.apiRequirementBatchSaveDataPost$Json({\r\n          body: updateData\r\n        }).toPromise()\r\n      );\r\n    }\r\n\r\n    // 執行所有請求\r\n    Promise.all(requests)\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        const totalItems = this.batchEditItems.length;\r\n\r\n        if (successCount === responses.length) {\r\n          this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\r\n        } else {\r\n          this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\r\n        }\r\n\r\n        // 清理狀態並重新載入資料\r\n        this.selectedItems = [];\r\n        this.batchEditItems = [];\r\n        this.isBatchEditMode = false;\r\n        this.updateSelectAllState();\r\n        this.getList();\r\n        ref.close();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次保存失敗:', error);\r\n        this.message.showErrorMSG('批次保存時發生錯誤');\r\n      });\r\n  }\r\n\r\n  // 重置批次編輯中的單一項目到原始狀態\r\n  resetBatchEditItem(index: number) {\r\n    const originalItem = this.selectedItems[index];\r\n    if (originalItem) {\r\n      this.batchEditItems[index] = {\r\n        CRequirementID: originalItem.CRequirementID,\r\n        CBuildCaseID: originalItem.CBuildCaseID,\r\n        CLocation: originalItem.CLocation,\r\n        CRequirement: originalItem.CRequirement,\r\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\r\n        CSort: originalItem.CSort,\r\n        CStatus: originalItem.CStatus,\r\n        CUnitPrice: originalItem.CUnitPrice || 0,\r\n        CUnit: originalItem.CUnit,\r\n        CSpaceId: originalItem.CSpaceId || null,\r\n        CIsShow: originalItem.CIsShow || false,\r\n        CIsSimple: originalItem.CIsSimple || false,\r\n        CRemark: originalItem.CRemark\r\n      };\r\n    }\r\n  }\r\n\r\n  // 取消批次編輯\r\n  cancelBatchEdit(ref: any) {\r\n    this.isBatchEditMode = false;\r\n    this.batchEditItems = [];\r\n    ref.close();\r\n  }\r\n\r\n  // 備用：如果需要手動建立需求項目的方法\r\n  private batchCreateRequirements(requirements: SaveDataRequirement[]) {\r\n    const batchRequests = requirements.map(requirement =>\r\n      this.requirementService.apiRequirementSaveDataPost$Json({\r\n        body: requirement\r\n      })\r\n    );\r\n\r\n    Promise.all(batchRequests.map(req => req.toPromise()))\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\r\n        this.getList();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次建立需求失敗:', error);\r\n        this.message.showErrorMSG('批次建立需求時發生錯誤');\r\n      });\r\n  }\r\n\r\n  // 導航到模板管理頁面\r\n  navigateToTemplate(): void {\r\n    this.router.navigate(['/pages/template']);\r\n  }\r\n\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAmCC,SAAS,QAAQ,eAAe;AAErF,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/H,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AAGjE,SAASC,oCAAoC,QAAQ,4FAA4F;AAkC1I,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQZ,aAAa;EAG/Da,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB,EACtBC,4BAA0D;IAElE,KAAK,CAACV,MAAM,CAAC;IAZL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAOtC;IACA,KAAAC,yBAAyB,GAAG,EAA0F;IACtH,KAAAC,qBAAqB,GAA8B,EAAE;IAErD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,eAAe,GAAqE;MAAEC,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAACnB,UAAU,CAACoB,cAAc,CAACzB,aAAa,CAAC;IACzD,KAAA0B,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAC,aAAa,GAAqB,EAAE;IACpC,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB;IACA,KAAAC,cAAc,GAAyE,EAAE;IACzF;IACA,KAAAC,eAAe,GAAoB,IAAI,CAACC,yBAAyB,EAAE;IA5BjE,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EA4BSC,QAAQA,CAAA,GAAW;EAE5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACnB,yBAAyB,CAACsB,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACtB,yBAAyB,CAACuB,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACvB,yBAAyB,CAACwB,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACxB,yBAAyB,CAACyB,YAAY,GAAG,EAAE;IAChD,IAAI,CAACzB,yBAAyB,CAAC0B,SAAS,GAAG,EAAE;IAC7C;IACA,IAAI,CAAC1B,yBAAyB,CAACK,UAAU,GAAG,IAAI,CAACI,SAAS,CAACkB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACrB,KAAK,CAAC;EACpF;EAEA;EACAW,yBAAyBA,CAAA;IACvB,OAAO;MACLW,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,gBAAgB;MAC5BC,WAAW,EAAE,CACX,0BAA0B,EAC1B,YAAY,EACZ,iCAAiC,CAClC;MACDC,iBAAiB,EAAE,QAAQ;MAC3BC,gBAAgB,EAAE;KACnB;EACH;EAEA;EACAC,6BAA6BA,CAAA;IAC3B,OAAO;MACLL,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,yBAAyB;MACrCC,WAAW,EAAE,CACX,0BAA0B,EAC1B,YAAY,EACZ,iCAAiC,EACjC,0BAA0B,CAC3B;MACDC,iBAAiB,EAAE,QAAQ;MAC3BC,gBAAgB,EAAE;KACnB;EACH;EAEA;EACAE,iBAAiBA,CAACC,cAAmB;IACnC;IACA,IAAI,IAAI,CAACrB,eAAe,IAAI,IAAI,CAACF,aAAa,CAACwB,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMC,cAAc,GAAG,IAAI,CAACvB,eAAe,GACvC,iCAAiC,GACjC,gBAAgB,IAAI,CAACF,aAAa,CAACwB,MAAM,YAAY;MAEzD,IAAI,CAACE,OAAO,CAACD,cAAc,CAAC,EAAE;QAC5B;QACAE,UAAU,CAAC,MAAK;UACd,IAAI,CAACxC,yBAAyB,CAACyC,YAAY,GAAG,IAAI,CAAC7B,gBAAgB;QACrE,CAAC,EAAE,CAAC,CAAC;QACL;MACF;IACF;IAEA;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACD,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAACH,gBAAgB,GAAGwB,cAAc;IACtC,IAAI,CAACM,OAAO,EAAE;EAChB;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACxB,oBAAoB,EAAE;IAC3B;IACA,IAAI,CAACN,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACD,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,IAAI,CAACb,aAAa,IAAI,IAAI,CAACA,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;MACvDG,UAAU,CAAC,MAAK;QACd,IAAI,CAACxC,yBAAyB,CAACyC,YAAY,GAAG,IAAI,CAACvC,aAAa,CAAC,CAAC,CAAC,CAAC0C,GAAG;QACvE,IAAI,CAACF,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAG,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAAC3C,SAAS,CAAC4C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAI4C,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC5C,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOyC,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAChE,KAAK,CAACiE,KAAK,EAAE;IAElB;IACA,IAAI,CAACjE,KAAK,CAACkE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvD,eAAe,CAACqC,YAAY,CAAC;IAChE,IAAI,CAAChD,KAAK,CAACkE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvD,eAAe,CAACqB,YAAY,CAAC;IAC9D,IAAI,CAAChC,KAAK,CAACkE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACvD,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACZ,KAAK,CAACkE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvD,eAAe,CAACwD,KAAK,CAAC;IACvD,IAAI,CAACnE,KAAK,CAACkE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvD,eAAe,CAACkB,OAAO,CAAC;IACzD,IAAI,CAAC7B,KAAK,CAACkE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvD,eAAe,CAACyD,UAAU,CAAC;IAC5D,IAAI,CAACpE,KAAK,CAACkE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvD,eAAe,CAAC0D,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC1D,eAAe,CAACwD,KAAK,KAAK,IAAI,IAAI,IAAI,CAACxD,eAAe,CAACwD,KAAK,KAAKG,SAAS,IAAI,IAAI,CAAC3D,eAAe,CAACwD,KAAK,GAAG,CAAC,EAAE;MACrH,IAAI,CAACnE,KAAK,CAACuE,aAAa,CAACT,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA,IAAI,IAAI,CAACnD,eAAe,CAACyD,UAAU,KAAK,IAAI,IAAI,IAAI,CAACzD,eAAe,CAACyD,UAAU,KAAKE,SAAS,IAAI,IAAI,CAAC3D,eAAe,CAACyD,UAAU,GAAG,CAAC,EAAE;MACpI,IAAI,CAACpE,KAAK,CAACuE,aAAa,CAACT,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA;IACA,IAAI,IAAI,CAACnD,eAAe,CAACsB,SAAS,IAAI,IAAI,CAACtB,eAAe,CAACsB,SAAS,CAACW,MAAM,GAAG,EAAE,EAAE;MAChF,IAAI,CAAC5C,KAAK,CAACuE,aAAa,CAACT,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA,IAAI,IAAI,CAACnD,eAAe,CAACqB,YAAY,IAAI,IAAI,CAACrB,eAAe,CAACqB,YAAY,CAACY,MAAM,GAAG,EAAE,EAAE;MACtF,IAAI,CAAC5C,KAAK,CAACuE,aAAa,CAACT,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACnD,eAAe,CAAC6D,OAAO,IAAI,IAAI,CAAC7D,eAAe,CAAC6D,OAAO,CAAC5B,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC5C,KAAK,CAACuE,aAAa,CAACT,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAW,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAACxD,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEkB,OAAO,EAAE,KAAK;MAAEC,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACpB,eAAe,CAACkB,OAAO,GAAG,CAAC;IAChC,IAAI,CAAClB,eAAe,CAACyD,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACjD,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACR,eAAe,CAACqC,YAAY,GAAG,IAAI,CAAC7B,gBAAgB;IAC3D,CAAC,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACA,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACjC,eAAe,CAACqC,YAAY,GAAG,IAAI,CAACvC,aAAa,CAAC,CAAC,CAAC,CAAC0C,GAAG;IAC/D;IAEA,IAAI,CAACrD,aAAa,CAAC6E,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAAoB,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAACtE,qBAAqB,CAACwE,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAC5D,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM4D,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAAChF,aAAa,CAAC6E,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACtB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChE,KAAK,CAACuE,aAAa,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC7C,OAAO,CAACwF,aAAa,CAAC,IAAI,CAACvF,KAAK,CAACuE,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACrE,kBAAkB,CAACsF,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAC9E;KACZ,CAAC,CAAC+E,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC7F,OAAO,CAAC8F,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC5C,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAClD,OAAO,CAAC+F,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAAoB;IAC3B,IAAI,CAAClE,eAAe,CAACqE,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAAC9D,KAAK,GAAG,KAAK;IAClB,IAAIgF,MAAM,CAACpD,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACqD,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACjG,kBAAkB,CAACkG,iCAAiC,CAAC;MACxDX,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAACrE,eAAe,CAACqE;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAC5F,OAAO,CAAC8F,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAAC5C,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAtB,gBAAgBA,CAAA;IACd,IAAI,CAAC1B,gBAAgB,CAACoG,qCAAqC,CAAC;MAAEZ,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEa,IAAI,CAACvH,kBAAkB,CAAC,IAAI,CAACsB,UAAU,CAAC,CAAC,CAACqF,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAClF,aAAa,GAAGkF,GAAG,CAACY,OAAQ;MACjC;MACA,IAAI,IAAI,CAAC9F,aAAa,CAACmC,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAACrC,yBAAyB,CAACyC,YAAY,GAAG,IAAI,CAACvC,aAAa,CAAC,CAAC,CAAC,CAAC0C,GAAG;QACvE,IAAI,CAACF,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC1C,yBAAyB,CAACiG,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAClG,yBAAyB,CAACmG,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAACjG,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAACkG,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,IAAI,CAACrG,yBAAyB,CAACyC,YAAY,IAAI,IAAI,CAACzC,yBAAyB,CAACyC,YAAY,IAAI,CAAC,EAAE;MACnG,IAAI,CAAC7B,gBAAgB,GAAG,IAAI,CAACZ,yBAAyB,CAACyC,YAAY;IACrE;IAEA,IAAI,CAAC9C,kBAAkB,CAAC2G,8BAA8B,CAAC;MAAEpB,IAAI,EAAE,IAAI,CAAClF;IAAyB,CAAE,CAAC,CAC7F+F,IAAI,EAAE,CACNZ,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACY,OAAO,EAAE;UACf,IAAI,CAAC7F,eAAe,GAAGiF,GAAG,CAACY,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGjB,GAAG,CAACmB,UAAW;UAEnC;UACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC3F,aAAa,CAACwB,MAAM;UACvD,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4F,MAAM,CAACC,YAAY,IACzD,IAAI,CAACvG,eAAe,CAACwG,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACnC,cAAc,KAAKiC,YAAY,CAACjC,cAAc,CAAC,CAC/F;UAED;UACA,IAAI+B,qBAAqB,KAAK,IAAI,CAAC3F,aAAa,CAACwB,MAAM,IAAI,IAAI,CAACxB,aAAa,CAACwB,MAAM,KAAK,CAAC,EAAE;YAC1F,IAAI,CAACrB,cAAc,GAAG,EAAE;YACxB,IAAI,CAACD,eAAe,GAAG,KAAK;UAC9B;UAEA;UACA,IAAI,CAAC8F,oBAAoB,EAAE;QAC7B;MACF;IACF,CAAC,CAAC;EACN;EAEAnC,OAAOA,CAAA;IACL,IAAI,CAAC/E,kBAAkB,CAACmH,8BAA8B,CAAC;MAAE5B,IAAI,EAAE,IAAI,CAACjF;IAAqB,CAAE,CAAC,CACzF8F,IAAI,EAAE,CACNZ,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACY,OAAO,EAAE;UACf,IAAI,CAAC5F,eAAe,GAAG;YAAEC,UAAU,EAAE,EAAE;YAAEkB,OAAO,EAAE,KAAK;YAAEC,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAACpB,eAAe,CAACqC,YAAY,GAAG2C,GAAG,CAACY,OAAO,CAACvD,YAAY;UAC5D,IAAI,CAACrC,eAAe,CAACsB,SAAS,GAAG0D,GAAG,CAACY,OAAO,CAACtE,SAAS;UACtD,IAAI,CAACtB,eAAe,CAACC,UAAU,GAAG+E,GAAG,CAACY,OAAO,CAAC3F,UAAU,GAAI0C,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACY,OAAO,CAAC3F,UAAU,CAAC,GAAG+E,GAAG,CAACY,OAAO,CAAC3F,UAAU,GAAG,CAAC+E,GAAG,CAACY,OAAO,CAAC3F,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACD,eAAe,CAAC6D,OAAO,GAAGmB,GAAG,CAACY,OAAO,CAAC/B,OAAO;UAClD,IAAI,CAAC7D,eAAe,CAACqB,YAAY,GAAG2D,GAAG,CAACY,OAAO,CAACvE,YAAY;UAC5D,IAAI,CAACrB,eAAe,CAACqE,cAAc,GAAGW,GAAG,CAACY,OAAO,CAACvB,cAAc;UAChE,IAAI,CAACrE,eAAe,CAACwD,KAAK,GAAGwB,GAAG,CAACY,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAACxD,eAAe,CAACkB,OAAO,GAAG8D,GAAG,CAACY,OAAO,CAAC1E,OAAO;UAClD,IAAI,CAAClB,eAAe,CAACyD,UAAU,GAAGuB,GAAG,CAACY,OAAO,CAACnC,UAAU,IAAI,CAAC;UAC7D,IAAI,CAACzD,eAAe,CAAC0D,KAAK,GAAGsB,GAAG,CAACY,OAAO,CAAClC,KAAK;UAC9C,IAAI,CAAC1D,eAAe,CAAC2G,QAAQ,GAAG3B,GAAG,CAACY,OAAO,CAACe,QAAQ,IAAI,IAAI;UAC5D,IAAI,CAAC3G,eAAe,CAACmB,OAAO,GAAG6D,GAAG,CAACY,OAAO,CAACzE,OAAO,IAAI,KAAK;UAC3D,IAAI,CAACnB,eAAe,CAACoB,SAAS,GAAG4D,GAAG,CAACY,OAAO,CAACxE,SAAS,IAAI,KAAK;QACjE;MACF;IACF,CAAC,CAAC;EACN;EAEAwF,iBAAiBA,CAACzG,KAAa,EAAE0G,OAAY;IAC3CrC,OAAO,CAACC,GAAG,CAACoC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC7G,eAAe,CAACC,UAAU,EAAE6G,QAAQ,CAAC3G,KAAK,CAAC,EAAE;QACrD,IAAI,CAACH,eAAe,CAACC,UAAU,EAAEkD,IAAI,CAAChD,KAAK,CAAC;MAC9C;MACAqE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzE,eAAe,CAACC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,CAACC,UAAU,GAAG,IAAI,CAACD,eAAe,CAACC,UAAU,EAAEoG,MAAM,CAACU,CAAC,IAAIA,CAAC,KAAK5G,KAAK,CAAC;IAC7F;EACF;EAEA6G,cAAcA,CAAC9C,IAAS;IACtB,OAAOA,IAAI,CAAC/C,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEA8F,gBAAgBA,CAAC/C,IAAS;IACxB,OAAOA,IAAI,CAAC9C,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAEA;EACA8F,sBAAsBA,CAACC,MAA2B;IAChD3C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0C,MAAM,CAAC;IAEhC;IACA,IAAI,CAACC,2BAA2B,CAACD,MAAM,CAAC1G,aAAa,EAAE0G,MAAM,CAACE,eAAe,CAAC;EAChF;EAEAC,eAAeA,CAACC,YAAoB;IAClC,IAAI,CAACnI,OAAO,CAAC+F,YAAY,CAACoC,YAAY,CAAC;EACzC;EAEA;EACAC,+BAA+BA,CAACL,MAAkC;IAChE3C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0C,MAAM,CAAC;IAEhC;IACA,IAAI,CAACM,8BAA8B,CAACN,MAAM,CAAC1G,aAAa,CAAC;IAEzD,IAAI,CAACrB,OAAO,CAAC8F,aAAa,CAAC,OAAOiC,MAAM,CAAC1G,aAAa,CAACwB,MAAM,QAAQ,CAAC;EACxE;EAEAyF,+BAA+BA,CAAA;IAC7BlD,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EACvB;EAEAkD,2BAA2BA,CAACJ,YAAoB;IAC9C,IAAI,CAACnI,OAAO,CAAC+F,YAAY,CAACoC,YAAY,CAAC;EACzC;EAEA;EACQE,8BAA8BA,CAACG,oBAA2B;IAChE,IAAI,CAACA,oBAAoB,IAAIA,oBAAoB,CAAC3F,MAAM,KAAK,CAAC,EAAE;MAC9D;IACF;IAEA;IACA,IAAI,CAACxB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAE1B;IACAkH,oBAAoB,CAAC9E,OAAO,CAAC+E,WAAW,IAAG;MACzC,MAAMC,YAAY,GAAG,IAAI,CAAC/H,eAAe,CAACkD,IAAI,CAAC8E,IAAI,IACjDA,IAAI,CAAC1D,cAAc,KAAKwD,WAAW,CAACxD,cAAc,CACnD;MAED,IAAIyD,YAAY,EAAE;QAChB,IAAI,CAACrH,aAAa,CAAC0C,IAAI,CAAC2E,YAAY,CAAC;MACvC;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACrB,oBAAoB,EAAE;EAC7B;EAEA;EACQW,2BAA2BA,CAACY,iBAAwB,EAAEX,eAAmC;IAC/F,IAAI,CAAC,IAAI,CAACzH,yBAAyB,CAACyC,YAAY,EAAE;MAChD,IAAI,CAACjD,OAAO,CAAC+F,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA;IACA,IAAI,CAAC1E,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAE1B;IACA,MAAMuH,OAAO,GAAG,IAAI,CAAClI,eAAe,CAACkC,MAAM,GAAG,CAAC,GAC3CiG,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACpI,eAAe,CAACwB,GAAG,CAACwG,IAAI,IAAIA,IAAI,CAACvE,KAAK,IAAI,CAAC,CAAC,CAAC,GAC9D,CAAC;IAEL,IAAI4E,gBAAgB,GAAG,CAAC;IACxB,MAAMC,iBAAiB,GAAU,EAAE;IAEnC;IACAL,iBAAiB,CAAClF,OAAO,CAACwF,QAAQ,IAAG;MACnC,MAAMC,OAAO,GAAGlB,eAAe,CAACmB,GAAG,CAACF,QAAQ,CAACG,WAAW,CAAC,IAAI,EAAE;MAE/D,IAAIF,OAAO,IAAIA,OAAO,CAACtG,MAAM,GAAG,CAAC,EAAE;QACjC;QACAsG,OAAO,CAACzF,OAAO,CAAC,CAAC4F,MAAM,EAAEC,WAAW,KAAI;UACtC,MAAMC,aAAa,GAAG;YACpBvE,cAAc,EAAEV,SAAS;YAAE;YAC3BtB,YAAY,EAAE,IAAI,CAACzC,yBAAyB,CAACyC,YAAY;YACzDf,SAAS,EAAEoH,MAAM,CAACpH,SAAS,IAAIgH,QAAQ,CAAChH,SAAS,IAAI,EAAE;YAAE;YACzDD,YAAY,EAAEqH,MAAM,CAACG,KAAK,IAAI,GAAGP,QAAQ,CAACQ,aAAa,SAASH,WAAW,GAAG,CAAC,EAAE;YAAE;YACnF1I,UAAU,EAAEqI,QAAQ,CAACrI,UAAU,IAAI,EAAE;YAAE;YACvCuD,KAAK,EAAEyE,OAAO,GAAGG,gBAAgB,GAAG,CAAC;YAAE;YACvClH,OAAO,EAAE,CAAC;YAAE;YACZuC,UAAU,EAAGiF,MAAc,CAACjF,UAAU,IAAK6E,QAAgB,CAAC7E,UAAU,IAAI,CAAC;YAAE;YAC7EC,KAAK,EAAGgF,MAAc,CAAChF,KAAK,IAAK4E,QAAgB,CAAC5E,KAAK,IAAI,GAAG;YAAE;YAChEiD,QAAQ,EAAE+B,MAAM,CAACK,UAAU,IAAKT,QAAgB,CAACU,SAAS,IAAI,IAAI;YAAE;YACpE7H,OAAO,EAAEmH,QAAQ,CAACnH,OAAO,KAAKwC,SAAS,GAAG2E,QAAQ,CAACnH,OAAO,GAAG,KAAK;YAAE;YACpEC,SAAS,EAAEkH,QAAQ,CAAClH,SAAS,KAAKuC,SAAS,GAAG2E,QAAQ,CAAClH,SAAS,GAAG,KAAK;YAAE;YAC1EyC,OAAO,EAAG6E,MAAc,CAAC7E,OAAO,IAAI,OAAOyE,QAAQ,CAACQ,aAAa,UAAUJ,MAAM,CAACG,KAAK,KAAK,CAAC;WAC9F;UAEDR,iBAAiB,CAAClF,IAAI,CAACyF,aAAa,CAAC;UACrCR,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMQ,aAAa,GAAG;UACpBvE,cAAc,EAAEV,SAAS;UAAE;UAC3BtB,YAAY,EAAE,IAAI,CAACzC,yBAAyB,CAACyC,YAAY;UACzDf,SAAS,EAAEgH,QAAQ,CAAChH,SAAS,IAAI,EAAE;UAAE;UACrCD,YAAY,EAAEiH,QAAQ,CAACQ,aAAa,IAAI,QAAQV,gBAAgB,GAAG,CAAC,EAAE;UAAE;UACxEnI,UAAU,EAAEqI,QAAQ,CAACrI,UAAU,IAAI,EAAE;UAAE;UACvCuD,KAAK,EAAEyE,OAAO,GAAGG,gBAAgB,GAAG,CAAC;UAAE;UACvClH,OAAO,EAAE,CAAC;UAAE;UACZuC,UAAU,EAAG6E,QAAgB,CAAC7E,UAAU,IAAI,CAAC;UAAE;UAC/CC,KAAK,EAAG4E,QAAgB,CAAC5E,KAAK,IAAI,GAAG;UAAE;UACvCiD,QAAQ,EAAG2B,QAAgB,CAACU,SAAS,IAAI,IAAI;UAAE;UAC/C7H,OAAO,EAAEmH,QAAQ,CAACnH,OAAO,KAAKwC,SAAS,GAAG2E,QAAQ,CAACnH,OAAO,GAAG,KAAK;UAAE;UACpEC,SAAS,EAAEkH,QAAQ,CAAClH,SAAS,KAAKuC,SAAS,GAAG2E,QAAQ,CAAClH,SAAS,GAAG,KAAK;UAAE;UAC1EyC,OAAO,EAAGyE,QAAgB,CAACzE,OAAO,IAAI,OAAOyE,QAAQ,CAACQ,aAAa,KAAK,CAAC;SAC1E;QAEDT,iBAAiB,CAAClF,IAAI,CAACyF,aAAa,CAAC;QACrCR,gBAAgB,EAAE;MACpB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACxH,cAAc,GAAGyH,iBAAiB;IAEvC;IACA,IAAI,CAACxH,eAAe,GAAG,IAAI,CAACiB,6BAA6B,EAAE;IAC3D,IAAI,CAACnB,eAAe,GAAG,IAAI;IAE3B;IACA,MAAMsI,gBAAgB,GAAGjB,iBAAiB,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEb,QAAQ,KAAI;MAClE,MAAMC,OAAO,GAAGlB,eAAe,CAACmB,GAAG,CAACF,QAAQ,CAACG,WAAW,CAAC,IAAI,EAAE;MAC/D,OAAOU,GAAG,IAAIZ,OAAO,CAACtG,MAAM,IAAI,CAAC,CAAC;IACpC,CAAC,EAAE,CAAC,CAAC;IAEL;IACAG,UAAU,CAAC,MAAK;MACd,IAAI,CAACjD,aAAa,CAAC6E,IAAI,CAAC,IAAI,CAACoF,eAAe,CAAC;IAC/C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAE;EAEH;EACAC,mBAAmBA,CAACtB,IAAoB;IACtC,MAAMuB,KAAK,GAAG,IAAI,CAAC7I,aAAa,CAAC8I,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACnF,cAAc,KAAK0D,IAAI,CAAC1D,cAAc,CAAC;IACvG,IAAIiF,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC7I,aAAa,CAACgJ,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAAC7I,aAAa,CAAC0C,IAAI,CAAC4E,IAAI,CAAC;IAC/B;IACA,IAAI,CAACtB,oBAAoB,EAAE;EAC7B;EAEA;EACAiD,eAAeA,CAACC,QAAiB;IAC/B,IAAI,IAAI,CAAC5J,eAAe,CAACkC,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACxB,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;IACF;IAEA;IACA,IAAI,CAACA,aAAa,GAAGiJ,QAAQ;IAE7B;IACA,IAAI,IAAI,CAACjJ,aAAa,EAAE;MACtB,IAAI,CAACD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACV,eAAe,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACU,aAAa,GAAG,EAAE;IACzB;EACF;EAEA;EACAgG,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC1G,eAAe,CAACkC,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACvB,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,IAAI,CAACD,aAAa,CAACwB,MAAM,KAAK,IAAI,CAAClC,eAAe,CAACkC,MAAM;IAChF;EACF;EAEA;EACA2H,cAAcA,CAAC7B,IAAoB;IACjC,OAAO,IAAI,CAACtH,aAAa,CAAC8F,IAAI,CAACiD,QAAQ,IAAIA,QAAQ,CAACnF,cAAc,KAAK0D,IAAI,CAAC1D,cAAc,CAAC;EAC7F;EAEA;EACAwF,aAAaA,CAAC9F,MAAwB,EAAEoD,MAAwB;IAC9D,IAAI,IAAI,CAAC1G,aAAa,CAACwB,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC7C,OAAO,CAAC+F,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA;IACA,IAAI,CAACtE,eAAe,GAAGsG,MAAM,IAAI,IAAI,CAACrG,yBAAyB,EAAE;IACjE,IAAI,CAACH,eAAe,GAAG,IAAI;IAE3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,aAAa,CAACc,GAAG,CAACwG,IAAI,KAAK;MACpD1D,cAAc,EAAE0D,IAAI,CAAC1D,cAAc;MACnChC,YAAY,EAAE0F,IAAI,CAAC1F,YAAY;MAC/Bf,SAAS,EAAEyG,IAAI,CAACzG,SAAS;MACzBD,YAAY,EAAE0G,IAAI,CAAC1G,YAAY;MAC/BpB,UAAU,EAAE8H,IAAI,CAAC9H,UAAU,GAAG,CAAC,GAAG8H,IAAI,CAAC9H,UAAU,CAAC,GAAG,EAAE;MACvDuD,KAAK,EAAEuE,IAAI,CAACvE,KAAK;MACjBtC,OAAO,EAAE6G,IAAI,CAAC7G,OAAO;MACrBuC,UAAU,EAAEsE,IAAI,CAACtE,UAAU,IAAI,CAAC;MAChCC,KAAK,EAAEqE,IAAI,CAACrE,KAAK;MACjBiD,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,IAAI,IAAI;MAC/BxF,OAAO,EAAE4G,IAAI,CAAC5G,OAAO,IAAI,KAAK;MAC9BC,SAAS,EAAE2G,IAAI,CAAC3G,SAAS,IAAI,KAAK;MAClCyC,OAAO,EAAEkE,IAAI,CAAClE;KACf,CAAC,CAAC;IAEH,IAAI,CAAC1E,aAAa,CAAC6E,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACA+F,eAAeA,CAAA;IACb,MAAMlG,aAAa,GAAa,EAAE;IAElC,IAAI,CAAChD,cAAc,CAACkC,OAAO,CAAC,CAACiF,IAAI,EAAEuB,KAAK,KAAI;MAC1C,MAAMS,OAAO,GAAGT,KAAK,GAAG,CAAC;MAEzB;MACA,IAAI,CAACvB,IAAI,CAAC1F,YAAY,EAAE;QACtBuB,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAAChC,IAAI,CAAC1G,YAAY,IAAI0G,IAAI,CAAC1G,YAAY,CAAC2I,IAAI,EAAE,KAAK,EAAE,EAAE;QACzDpG,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAAChC,IAAI,CAAC9H,UAAU,IAAI8H,IAAI,CAAC9H,UAAU,CAACgC,MAAM,KAAK,CAAC,EAAE;QACpD2B,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAIhC,IAAI,CAACvE,KAAK,KAAK,IAAI,IAAIuE,IAAI,CAACvE,KAAK,KAAKG,SAAS,IAAIoE,IAAI,CAACvE,KAAK,GAAG,CAAC,EAAE;QACrEI,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAIhC,IAAI,CAAC7G,OAAO,KAAK,IAAI,IAAI6G,IAAI,CAAC7G,OAAO,KAAKyC,SAAS,EAAE;QACvDC,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAIhC,IAAI,CAACtE,UAAU,KAAK,IAAI,IAAIsE,IAAI,CAACtE,UAAU,KAAKE,SAAS,IAAIoE,IAAI,CAACtE,UAAU,GAAG,CAAC,EAAE;QACpFG,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAI,CAAChC,IAAI,CAACrE,KAAK,IAAIqE,IAAI,CAACrE,KAAK,CAACsG,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3CpG,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,WAAW,CAAC;MAC/C;MAEA;MACA,IAAIhC,IAAI,CAACzG,SAAS,IAAIyG,IAAI,CAACzG,SAAS,CAACW,MAAM,GAAG,EAAE,EAAE;QAChD2B,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,cAAc,CAAC;MAClD;MAEA,IAAIhC,IAAI,CAAC1G,YAAY,IAAI0G,IAAI,CAAC1G,YAAY,CAACY,MAAM,GAAG,EAAE,EAAE;QACtD2B,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,gBAAgB,CAAC;MACpD;MAEA,IAAIhC,IAAI,CAAClE,OAAO,IAAIkE,IAAI,CAAClE,OAAO,CAAC5B,MAAM,GAAG,GAAG,EAAE;QAC7C2B,aAAa,CAACT,IAAI,CAAC,OAAO4G,OAAO,iBAAiB,CAAC;MACrD;IACF,CAAC,CAAC;IAEF,OAAOnG,aAAa;EACtB;EAEA;EACAqG,SAASA,CAACtF,GAAQ;IAChB,IAAI,IAAI,CAAC/D,cAAc,CAACqB,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAAC7C,OAAO,CAAC+F,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA;IACA,MAAM+E,gBAAgB,GAAG,IAAI,CAACJ,eAAe,EAAE;IAC/C,IAAII,gBAAgB,CAACjI,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC7C,OAAO,CAACwF,aAAa,CAACsF,gBAAgB,CAAC;MAC5C;IACF;IAEA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACvJ,cAAc,CAACyF,MAAM,CAAC0B,IAAI,IAAI,CAACA,IAAI,CAAC1D,cAAc,CAAC;IACzE,MAAM+F,WAAW,GAAG,IAAI,CAACxJ,cAAc,CAACyF,MAAM,CAAC0B,IAAI,IAAIA,IAAI,CAAC1D,cAAc,CAAC;IAE3E;IACA,MAAMgG,QAAQ,GAAmB,EAAE;IAEnC;IACAF,QAAQ,CAACrH,OAAO,CAACiF,IAAI,IAAG;MACtB,MAAMuC,WAAW,GAAwB;QACvCjI,YAAY,EAAE0F,IAAI,CAAC1F,YAAY;QAC/Bf,SAAS,EAAEyG,IAAI,CAACzG,SAAS;QACzBD,YAAY,EAAE0G,IAAI,CAAC1G,YAAY;QAC/BpB,UAAU,EAAE8H,IAAI,CAAC9H,UAAU;QAC3BuD,KAAK,EAAEuE,IAAI,CAACvE,KAAK;QACjBtC,OAAO,EAAE6G,IAAI,CAAC7G,OAAO;QACrBuC,UAAU,EAAEsE,IAAI,CAACtE,UAAU;QAC3BC,KAAK,EAAEqE,IAAI,CAACrE,KAAK;QACjBiD,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ;QACvBxF,OAAO,EAAE4G,IAAI,CAAC5G,OAAO;QACrBC,SAAS,EAAE2G,IAAI,CAAC3G,SAAS;QACzByC,OAAO,EAAEkE,IAAI,CAAClE;OACf;MAEDwG,QAAQ,CAAClH,IAAI,CACX,IAAI,CAAC5D,kBAAkB,CAACsF,+BAA+B,CAAC;QACtDC,IAAI,EAAEwF;OACP,CAAC,CAACC,SAAS,EAAE,CACf;IACH,CAAC,CAAC;IAEF;IACA,IAAIH,WAAW,CAACnI,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMuI,UAAU,GAA0BJ,WAAW,CAAC7I,GAAG,CAACwG,IAAI,KAAK;QACjE1D,cAAc,EAAE0D,IAAI,CAAC1D,cAAc;QACnChC,YAAY,EAAE0F,IAAI,CAAC1F,YAAY;QAC/Bf,SAAS,EAAEyG,IAAI,CAACzG,SAAS;QACzBD,YAAY,EAAE0G,IAAI,CAAC1G,YAAY;QAC/BpB,UAAU,EAAE8H,IAAI,CAAC9H,UAAU;QAC3BuD,KAAK,EAAEuE,IAAI,CAACvE,KAAK;QACjBtC,OAAO,EAAE6G,IAAI,CAAC7G,OAAO;QACrBuC,UAAU,EAAEsE,IAAI,CAACtE,UAAU;QAC3BC,KAAK,EAAEqE,IAAI,CAACrE,KAAK;QACjBiD,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ;QACvBxF,OAAO,EAAE4G,IAAI,CAAC5G,OAAO;QACrBC,SAAS,EAAE2G,IAAI,CAAC3G,SAAS;QACzByC,OAAO,EAAEkE,IAAI,CAAClE;OACf,CAAC,CAAC;MAEHwG,QAAQ,CAAClH,IAAI,CACX,IAAI,CAAC5D,kBAAkB,CAACkL,oCAAoC,CAAC;QAC3D3F,IAAI,EAAE0F;OACP,CAAC,CAACD,SAAS,EAAE,CACf;IACH;IAEA;IACAG,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC,CAClBO,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAACxE,MAAM,CAACrB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAAChD,MAAM;MAC1E,MAAM8I,UAAU,GAAG,IAAI,CAACnK,cAAc,CAACqB,MAAM;MAE7C,IAAI6I,YAAY,KAAKD,SAAS,CAAC5I,MAAM,EAAE;QACrC,IAAI,CAAC7C,OAAO,CAAC8F,aAAa,CAAC,QAAQ6F,UAAU,aAAaZ,QAAQ,CAAClI,MAAM,SAASmI,WAAW,CAACnI,MAAM,GAAG,CAAC;MAC1G,CAAC,MAAM;QACL,IAAI,CAAC7C,OAAO,CAAC8F,aAAa,CAAC,QAAQ4F,YAAY,QAAQD,SAAS,CAAC5I,MAAM,GAAG6I,YAAY,MAAM,CAAC;MAC/F;MAEA;MACA,IAAI,CAACrK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACG,cAAc,GAAG,EAAE;MACxB,IAAI,CAACD,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC8F,oBAAoB,EAAE;MAC3B,IAAI,CAACnE,OAAO,EAAE;MACdqC,GAAG,CAACU,KAAK,EAAE;IACb,CAAC,CAAC,CACD2F,KAAK,CAACzG,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,CAACnF,OAAO,CAAC+F,YAAY,CAAC,WAAW,CAAC;IACxC,CAAC,CAAC;EACN;EAEA;EACA8F,kBAAkBA,CAAC3B,KAAa;IAC9B,MAAM4B,YAAY,GAAG,IAAI,CAACzK,aAAa,CAAC6I,KAAK,CAAC;IAC9C,IAAI4B,YAAY,EAAE;MAChB,IAAI,CAACtK,cAAc,CAAC0I,KAAK,CAAC,GAAG;QAC3BjF,cAAc,EAAE6G,YAAY,CAAC7G,cAAc;QAC3ChC,YAAY,EAAE6I,YAAY,CAAC7I,YAAY;QACvCf,SAAS,EAAE4J,YAAY,CAAC5J,SAAS;QACjCD,YAAY,EAAE6J,YAAY,CAAC7J,YAAY;QACvCpB,UAAU,EAAEiL,YAAY,CAACjL,UAAU,GAAG,CAAC,GAAGiL,YAAY,CAACjL,UAAU,CAAC,GAAG,EAAE;QACvEuD,KAAK,EAAE0H,YAAY,CAAC1H,KAAK;QACzBtC,OAAO,EAAEgK,YAAY,CAAChK,OAAO;QAC7BuC,UAAU,EAAEyH,YAAY,CAACzH,UAAU,IAAI,CAAC;QACxCC,KAAK,EAAEwH,YAAY,CAACxH,KAAK;QACzBiD,QAAQ,EAAEuE,YAAY,CAACvE,QAAQ,IAAI,IAAI;QACvCxF,OAAO,EAAE+J,YAAY,CAAC/J,OAAO,IAAI,KAAK;QACtCC,SAAS,EAAE8J,YAAY,CAAC9J,SAAS,IAAI,KAAK;QAC1CyC,OAAO,EAAEqH,YAAY,CAACrH;OACvB;IACH;EACF;EAEA;EACAsH,eAAeA,CAACxG,GAAQ;IACtB,IAAI,CAAChE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB+D,GAAG,CAACU,KAAK,EAAE;EACb;EAEA;EACQ+F,uBAAuBA,CAACC,YAAmC;IACjE,MAAMC,aAAa,GAAGD,YAAY,CAAC9J,GAAG,CAACsG,WAAW,IAChD,IAAI,CAACtI,kBAAkB,CAACsF,+BAA+B,CAAC;MACtDC,IAAI,EAAE+C;KACP,CAAC,CACH;IAED6C,OAAO,CAACC,GAAG,CAACW,aAAa,CAAC/J,GAAG,CAACgK,GAAG,IAAIA,GAAG,CAAChB,SAAS,EAAE,CAAC,CAAC,CACnDK,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAACxE,MAAM,CAACrB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAAChD,MAAM;MAC1E,IAAI,CAAC7C,OAAO,CAAC8F,aAAa,CAAC,QAAQ4F,YAAY,QAAQ,CAAC;MACxD,IAAI,CAACxI,OAAO,EAAE;IAChB,CAAC,CAAC,CACD0I,KAAK,CAACzG,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAACnF,OAAO,CAAC+F,YAAY,CAAC,aAAa,CAAC;IAC1C,CAAC,CAAC;EACN;EAEA;EACAqG,kBAAkBA,CAAA;IAChB,IAAI,CAAC/L,MAAM,CAACgM,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;CAED;AAzwBkDC,UAAA,EAAhD7N,SAAS,CAAC,iBAAiB,EAAE;EAAE8N,MAAM,EAAE;AAAK,CAAE,CAAC,C,sEAAoC;AADzE5M,8BAA8B,GAAA2M,UAAA,EAvB1C9N,SAAS,CAAC;EACTgO,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhO,YAAY,EACZO,mBAAmB,EACnBL,aAAa,EACbM,WAAW,EACXJ,cAAc,EACdD,cAAc,EACdO,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVX,gBAAgB,EAChBY,kBAAkB,EAClBC,oBAAoB,EACpBE,oCAAoC,EACpCiN,0CAA0C,CAC3C;EACDC,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACWlN,8BAA8B,CA0wB1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}