{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/space.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction SpacePickerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function SpacePickerComponent_div_16_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.allSelected);\n  }\n}\nfunction SpacePickerComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_21_Template_div_click_0_listener() {\n      const space_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSpaceSelection(space_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r4 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r4.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r4.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r4.CLocation || \"-\");\n  }\n}\nfunction SpacePickerComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"nb-icon\", 30);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"ngx-pagination\", 32);\n    i0.ɵɵtwoWayListener(\"PageChange\", function SpacePickerComponent_div_23_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pageIndex, $event) || (ctx_r1.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function SpacePickerComponent_div_23_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r1.pageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r1.pageSize)(\"CollectionSize\", ctx_r1.totalRecords);\n  }\n}\nfunction SpacePickerComponent_div_24_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_24_span_5_Template_button_click_2_listener() {\n      const space_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeSelectedSpace(space_r7));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r7.CPart, \" \");\n  }\n}\nfunction SpacePickerComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h6\", 34);\n    i0.ɵɵelement(2, \"nb-icon\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36);\n    i0.ɵɵtemplate(5, SpacePickerComponent_div_24_span_5_Template, 3, 1, \"span\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u7A7A\\u9593 (\", ctx_r1.selectedItems.length, \" \\u9805) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedItems);\n  }\n}\nexport class SpacePickerComponent {\n  constructor(spaceService) {\n    this.spaceService = spaceService;\n    this.selectedItems = [];\n    this.multiple = true;\n    this.placeholder = '請選擇空間';\n    this.selectionChange = new EventEmitter();\n    // 搜尋相關屬性\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    // 分頁相關屬性\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.totalRecords = 0;\n    // 資料相關屬性\n    this.availableSpaces = [];\n    this.allSelected = false;\n  }\n  ngOnInit() {\n    this.loadAvailableSpaces();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this.spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n        this.updateAllSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 切換空間選擇\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({\n            ...space\n          });\n        } else {\n          this.selectedItems = [{\n            ...space\n          }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 全選/取消全選\n  toggleAllSpaces() {\n    this.allSelected = !this.allSelected;\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({\n            ...space\n          });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 移除已選空間\n  removeSelectedSpace(space) {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 更新全選狀態\n  updateAllSelectedState() {\n    this.allSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page) {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 計算總頁數\n  get totalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  static {\n    this.ɵfac = function SpacePickerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpacePickerComponent)(i0.ɵɵdirectiveInject(i1.SpaceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpacePickerComponent,\n      selectors: [[\"app-space-picker\"]],\n      inputs: {\n        selectedItems: \"selectedItems\",\n        multiple: \"multiple\",\n        placeholder: \"placeholder\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 25,\n      vars: 11,\n      consts: [[1, \"space-picker-container\"], [1, \"search-section\", \"mb-3\"], [1, \"row\"], [1, \"col-md-5\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [\"nbButton\", \"\", 1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"space-list-section\", \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"font-weight-bold\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"selected-summary mt-3 p-3 bg-light border rounded\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", \"id\", \"selectAllSpaces\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllSpaces\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"font-weight-bold\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"selected-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"mb-2\"], [\"icon\", \"checkmark-circle-outline\", 1, \"text-success\", \"me-2\"], [1, \"selected-spaces-list\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", \"aria-label\", \"\\u79FB\\u9664\", 1, \"btn-close\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"]],\n      template: function SpacePickerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"nb-form-field\")(5, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_5_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_5_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"nb-form-field\")(8, \"input\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_8_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_10_listener() {\n            return ctx.onReset();\n          });\n          i0.ɵɵelement(11, \"nb-icon\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(13, \"nb-icon\", 10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵtemplate(16, SpacePickerComponent_div_16_Template, 4, 1, \"div\", 13)(17, SpacePickerComponent_div_17_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementStart(18, \"small\", 15);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 16);\n          i0.ɵɵtemplate(21, SpacePickerComponent_div_21_Template, 6, 4, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SpacePickerComponent_div_22_Template, 3, 0, \"div\", 18)(23, SpacePickerComponent_div_23_Template, 2, 3, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, SpacePickerComponent_div_24_Template, 6, 2, \"div\", 20);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx.totalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx.pageIndex, \" / \", ctx.totalPages, \" \\u9801 \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableSpaces);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableSpaces.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbFormFieldModule, i4.NbFormFieldComponent, NbInputModule, i4.NbInputDirective, NbButtonModule, i4.NbButtonComponent, NbIconModule, i4.NbIconComponent, PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 12px;\\n  margin-bottom: 1rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: 2px solid #e4e7ea;\\n  border-radius: 8px;\\n  background-color: #ffffff;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #2c3e50;\\n  margin-bottom: 4px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #7f8c8d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #4a90e2;\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);\\n  transform: translateY(-1px);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #28a745;\\n  background-color: #f8fff9;\\n  box-shadow: 0 2px 12px rgba(40, 167, 69, 0.2);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.8rem;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 20px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: white;\\n  opacity: 0.8;\\n  cursor: pointer;\\n  padding: 0;\\n  margin: 0;\\n  width: 12px;\\n  height: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u00D7\\\";\\n  font-size: 12px;\\n  line-height: 1;\\n}\\n@media (max-width: 768px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n    gap: 8px;\\n  }\\n}\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbFormFieldModule", "NbInputModule", "NbButtonModule", "NbIconModule", "PaginationComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpacePickerComponent_div_16_Template_input_change_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleAllSpaces", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "allSelected", "SpacePickerComponent_div_21_Template_div_click_0_listener", "space_r4", "_r3", "$implicit", "toggleSpaceSelection", "ɵɵclassProp", "selected", "ɵɵtextInterpolate", "<PERSON>art", "CLocation", "ɵɵelement", "ɵɵtwoWayListener", "SpacePickerComponent_div_23_Template_ngx_pagination_PageChange_1_listener", "$event", "_r5", "ɵɵtwoWayBindingSet", "pageIndex", "onPageChange", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "SpacePickerComponent_div_24_span_5_Template_button_click_2_listener", "space_r7", "_r6", "removeSelectedSpace", "ɵɵtextInterpolate1", "ɵɵtemplate", "SpacePickerComponent_div_24_span_5_Template", "selectedItems", "length", "SpacePickerComponent", "constructor", "spaceService", "multiple", "placeholder", "selectionChange", "searchKeyword", "searchLocation", "availableSpaces", "ngOnInit", "loadAvailableSpaces", "request", "CStatus", "PageIndex", "PageSize", "apiSpaceGetSpaceListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CSpaceID", "some", "s", "TotalItems", "updateAllSelectedState", "subscribe", "onSearch", "onReset", "space", "push", "for<PERSON>ach", "filter", "emit", "availableSpace", "find", "every", "page", "totalPages", "Math", "ceil", "ɵɵdirectiveInject", "i1", "SpaceService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpacePickerComponent_Template", "rf", "ctx", "SpacePickerComponent_Template_input_ngModelChange_5_listener", "SpacePickerComponent_Template_input_keyup_enter_5_listener", "SpacePickerComponent_Template_input_ngModelChange_8_listener", "SpacePickerComponent_Template_input_keyup_enter_8_listener", "SpacePickerComponent_Template_button_click_10_listener", "SpacePickerComponent_Template_button_click_12_listener", "SpacePickerComponent_div_16_Template", "SpacePickerComponent_div_17_Template", "SpacePickerComponent_div_21_Template", "SpacePickerComponent_div_22_Template", "SpacePickerComponent_div_23_Template", "SpacePickerComponent_div_24_Template", "ɵɵtextInterpolate3", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbFormFieldComponent", "NbInputDirective", "NbButtonComponent", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { SpaceService } from 'src/services/api/services/space.service';\nimport { GetSpaceListResponse } from 'src/services/api/models';\nimport { tap } from 'rxjs/operators';\n\nexport interface SpacePickerItem {\n  CSpaceID: number;\n  CPart: string;\n  CLocation?: string | null;\n  selected?: boolean;\n}\n\n@Component({\n  selector: 'app-space-picker',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbFormFieldModule,\n    NbInputModule,\n    NbButtonModule,\n    NbIconModule,\n    PaginationComponent\n  ],\n  templateUrl: './space-picker.component.html',\n  styleUrls: ['./space-picker.component.scss']\n})\nexport class SpacePickerComponent implements OnInit {\n  @Input() selectedItems: SpacePickerItem[] = [];\n  @Input() multiple: boolean = true;\n  @Input() placeholder: string = '請選擇空間';\n  @Output() selectionChange = new EventEmitter<SpacePickerItem[]>();\n\n  // 搜尋相關屬性\n  searchKeyword: string = '';\n  searchLocation: string = '';\n\n  // 分頁相關屬性\n  pageIndex = 1;\n  pageSize = 10;\n  totalRecords = 0;\n\n  // 資料相關屬性\n  availableSpaces: SpacePickerItem[] = [];\n  allSelected = false;\n\n  constructor(private spaceService: SpaceService) { }\n\n  ngOnInit(): void {\n    this.loadAvailableSpaces();\n  }\n\n  // 載入可用空間列表\n  loadAvailableSpaces(): void {\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1, // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n\n    this.spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\n      tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID!,\n            CPart: item.CPart!,\n            CLocation: item.CLocation,\n            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n          this.updateAllSelectedState();\n        }\n      })\n    ).subscribe();\n  }\n\n  // 搜尋功能\n  onSearch(): void {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 重置搜尋\n  onReset(): void {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 切換空間選擇\n  toggleSpaceSelection(space: SpacePickerItem): void {\n    space.selected = !space.selected;\n\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({ ...space });\n        } else {\n          this.selectedItems = [{ ...space }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 全選/取消全選\n  toggleAllSpaces(): void {\n    this.allSelected = !this.allSelected;\n\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({ ...space });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 移除已選空間\n  removeSelectedSpace(space: SpacePickerItem): void {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 更新全選狀態\n  updateAllSelectedState(): void {\n    this.allSelected = this.availableSpaces.length > 0 &&\n      this.availableSpaces.every(space => space.selected);\n  }\n\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page: number): void {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n\n  // 計算總頁數\n  get totalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n}\n", "<!-- 空間選擇器共用組件 -->\n<div class=\"space-picker-container\">\n  <!-- 搜尋區域 -->\n  <div class=\"search-section mb-3\">\n    <div class=\"row\">\n      <div class=\"col-md-5\">\n        <nb-form-field>\n          <input type=\"text\" nbInput class=\"form-control-sm\" placeholder=\"搜尋項目名稱...\" [(ngModel)]=\"searchKeyword\"\n            (keyup.enter)=\"onSearch()\" style=\"height: 32px; border-radius: 4px;\" />\n        </nb-form-field>\n      </div>\n      <div class=\"col-md-5\">\n        <nb-form-field>\n          <input type=\"text\" nbInput class=\"form-control-sm\" placeholder=\"搜尋所屬區域...\" [(ngModel)]=\"searchLocation\"\n            (keyup.enter)=\"onSearch()\" style=\"height: 32px; border-radius: 4px;\" />\n        </nb-form-field>\n      </div>\n      <div class=\"col-md-2\">\n        <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onReset()\" nbButton ghost>\n          <nb-icon icon=\"refresh-outline\"></nb-icon>\n        </button>\n        <button class=\"btn btn-sm btn-secondary\" (click)=\"onSearch()\" nbButton>\n          <nb-icon icon=\"search-outline\"></nb-icon>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 空間列表區域 -->\n  <div class=\"space-list-section border rounded p-3\" style=\"background-color: #f8f9fa;\">\n    <!-- 列表標題和統計 -->\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\n      <div class=\"d-flex align-items-center\" *ngIf=\"multiple\">\n        <input type=\"checkbox\" id=\"selectAllSpaces\" [checked]=\"allSelected\" (change)=\"toggleAllSpaces()\" class=\"me-2\">\n        <label for=\"selectAllSpaces\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\n      </div>\n      <div *ngIf=\"!multiple\" class=\"font-weight-bold\">\n        選擇空間\n      </div>\n      <small class=\"text-muted\">\n        共 {{ totalRecords }} 筆，第 {{ pageIndex }} / {{ totalPages }} 頁\n      </small>\n    </div>\n\n    <!-- 空間項目網格 -->\n    <div class=\"space-grid\">\n      <div class=\"space-item\" *ngFor=\"let space of availableSpaces\" [class.selected]=\"space.selected\"\n        (click)=\"toggleSpaceSelection(space)\">\n        <div class=\"space-card\">\n          <div class=\"space-name\">{{ space.CPart }}</div>\n          <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 空間列表為空時的提示 -->\n    <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\n      <nb-icon icon=\"info-outline\" class=\"me-2\"></nb-icon>\n      沒有符合條件的空間\n    </div>\n\n    <!-- 分頁控制 -->\n    <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"totalRecords > pageSize\">\n      <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\n        (PageChange)=\"onPageChange($event)\">\n      </ngx-pagination>\n    </div>\n  </div>\n\n  <!-- 已選空間摘要 -->\n  <div *ngIf=\"selectedItems.length > 0\" class=\"selected-summary mt-3 p-3 bg-light border rounded\">\n    <h6 class=\"mb-2\">\n      <nb-icon icon=\"checkmark-circle-outline\" class=\"text-success me-2\"></nb-icon>\n      已選空間 ({{ selectedItems.length }} 項)\n    </h6>\n    <div class=\"selected-spaces-list\">\n      <span *ngFor=\"let space of selectedItems\" class=\"badge badge-primary me-1 mb-1\">\n        {{ space.CPart }}\n        <button type=\"button\" class=\"btn-close ms-1\" (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"\n          aria-label=\"移除\">\n        </button>\n      </span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC/F,SAASC,mBAAmB,QAAQ,0DAA0D;AAG9F,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;IC0B5BC,EADF,CAAAC,cAAA,cAAwD,gBACwD;IAA1CD,EAAA,CAAAE,UAAA,oBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAAhGT,EAAA,CAAAU,YAAA,EAA8G;IAC9GV,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAW,MAAA,2CAAM;IACnEX,EADmE,CAAAU,YAAA,EAAQ,EACrE;;;;IAFwCV,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAAa,UAAA,YAAAP,MAAA,CAAAQ,WAAA,CAAuB;;;;;IAGrEd,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAW,MAAA,iCACF;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;;;IAQNV,EAAA,CAAAC,cAAA,cACwC;IAAtCD,EAAA,CAAAE,UAAA,mBAAAa,0DAAA;MAAA,MAAAC,QAAA,GAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,oBAAA,CAAAH,QAAA,CAA2B;IAAA,EAAC;IAEnChB,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAW,MAAA,GAAiB;IAAAX,EAAA,CAAAU,YAAA,EAAM;IAC/CV,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAW,MAAA,GAA4B;IAE5DX,EAF4D,CAAAU,YAAA,EAAM,EAC1D,EACF;;;;IANwDV,EAAA,CAAAoB,WAAA,aAAAJ,QAAA,CAAAK,QAAA,CAAiC;IAGnErB,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAsB,iBAAA,CAAAN,QAAA,CAAAO,KAAA,CAAiB;IACbvB,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAAsB,iBAAA,CAAAN,QAAA,CAAAQ,SAAA,QAA4B;;;;;IAM9DxB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAyB,SAAA,kBAAoD;IACpDzB,EAAA,CAAAW,MAAA,+DACF;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;;;IAIJV,EADF,CAAAC,cAAA,cAAgF,yBAExC;IADtBD,EAAA,CAAA0B,gBAAA,wBAAAC,0EAAAC,MAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8B,kBAAA,CAAAxB,MAAA,CAAAyB,SAAA,EAAAH,MAAA,MAAAtB,MAAA,CAAAyB,SAAA,GAAAH,MAAA;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAAoB,MAAA;IAAA,EAAoB;IAClC5B,EAAA,CAAAE,UAAA,wBAAAyB,0EAAAC,MAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAA0B,YAAA,CAAAJ,MAAA,CAAoB;IAAA,EAAC;IAEvC5B,EADE,CAAAU,YAAA,EAAiB,EACb;;;;IAHYV,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAAiC,gBAAA,SAAA3B,MAAA,CAAAyB,SAAA,CAAoB;IAAuB/B,EAAtB,CAAAa,UAAA,aAAAP,MAAA,CAAA4B,QAAA,CAAqB,mBAAA5B,MAAA,CAAA6B,YAAA,CAAgC;;;;;;IAa1FnC,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAW,MAAA,GACA;IAAAX,EAAA,CAAAC,cAAA,iBACkB;IAD2BD,EAAA,CAAAE,UAAA,mBAAAkC,oEAAA;MAAA,MAAAC,QAAA,GAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA,EAAApB,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiC,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAGnFrC,EADE,CAAAU,YAAA,EAAS,EACJ;;;;IAJLV,EAAA,CAAAY,SAAA,EACA;IADAZ,EAAA,CAAAwC,kBAAA,MAAAH,QAAA,CAAAd,KAAA,MACA;;;;;IAPJvB,EADF,CAAAC,cAAA,cAAgG,aAC7E;IACfD,EAAA,CAAAyB,SAAA,kBAA6E;IAC7EzB,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAyC,UAAA,IAAAC,2CAAA,mBAAgF;IAOpF1C,EADE,CAAAU,YAAA,EAAM,EACF;;;;IAVFV,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAwC,kBAAA,gCAAAlC,MAAA,CAAAqC,aAAA,CAAAC,MAAA,cACF;IAE0B5C,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAa,UAAA,YAAAP,MAAA,CAAAqC,aAAA,CAAgB;;;AD7C9C,OAAM,MAAOE,oBAAoB;EAmB/BC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAlBvB,KAAAJ,aAAa,GAAsB,EAAE;IACrC,KAAAK,QAAQ,GAAY,IAAI;IACxB,KAAAC,WAAW,GAAW,OAAO;IAC5B,KAAAC,eAAe,GAAG,IAAI3D,YAAY,EAAqB;IAEjE;IACA,KAAA4D,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAE3B;IACA,KAAArB,SAAS,GAAG,CAAC;IACb,KAAAG,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,CAAC;IAEhB;IACA,KAAAkB,eAAe,GAAsB,EAAE;IACvC,KAAAvC,WAAW,GAAG,KAAK;EAE+B;EAElDwC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACdjC,KAAK,EAAE,IAAI,CAAC4B,aAAa,IAAI,IAAI;MACjC3B,SAAS,EAAE,IAAI,CAAC4B,cAAc,IAAI,IAAI;MACtCK,OAAO,EAAE,CAAC;MAAE;MACZC,SAAS,EAAE,IAAI,CAAC3B,SAAS;MACzB4B,QAAQ,EAAE,IAAI,CAACzB;KAChB;IAED,IAAI,CAACa,YAAY,CAACa,6BAA6B,CAAC;MAAEC,IAAI,EAAEL;IAAO,CAAE,CAAC,CAACM,IAAI,CACrE/D,GAAG,CAACgE,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACX,eAAe,GAAGU,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDC,QAAQ,EAAED,IAAI,CAACC,QAAS;UACxB7C,KAAK,EAAE4C,IAAI,CAAC5C,KAAM;UAClBC,SAAS,EAAE2C,IAAI,CAAC3C,SAAS;UACzBH,QAAQ,EAAE,IAAI,CAACsB,aAAa,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKD,IAAI,CAACC,QAAQ;SACpE,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACjC,YAAY,GAAG4B,QAAQ,CAACQ,UAAU,IAAI,CAAC;QAC5C,IAAI,CAACC,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,CAAC3C,SAAS,GAAG,CAAC;IAClB,IAAI,CAACwB,mBAAmB,EAAE;EAC5B;EAEA;EACAoB,OAAOA,CAAA;IACL,IAAI,CAACxB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACrB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACwB,mBAAmB,EAAE;EAC5B;EAEA;EACApC,oBAAoBA,CAACyD,KAAsB;IACzCA,KAAK,CAACvD,QAAQ,GAAG,CAACuD,KAAK,CAACvD,QAAQ;IAEhC,IAAIuD,KAAK,CAACvD,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACsB,aAAa,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC,EAAE;QAChE,IAAI,IAAI,CAACpB,QAAQ,EAAE;UACjB,IAAI,CAACL,aAAa,CAACkC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAACjC,aAAa,GAAG,CAAC;YAAE,GAAGiC;UAAK,CAAE,CAAC;UACnC;UACA,IAAI,CAACvB,eAAe,CAACyB,OAAO,CAACR,CAAC,IAAG;YAC/B,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,EAAE;cACjCE,CAAC,CAACjD,QAAQ,GAAG,KAAK;YACpB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAACsB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoC,MAAM,CAACT,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;IACpF;IAEA,IAAI,CAACI,sBAAsB,EAAE;IAC7B,IAAI,CAACtB,eAAe,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAlC,eAAeA,CAAA;IACb,IAAI,CAACK,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACuC,eAAe,CAACyB,OAAO,CAACF,KAAK,IAAG;MACnC,IAAI,IAAI,CAAC9D,WAAW,IAAI,CAAC8D,KAAK,CAACvD,QAAQ,EAAE;QACvCuD,KAAK,CAACvD,QAAQ,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,CAACsB,aAAa,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC,EAAE;UAChE,IAAI,CAACzB,aAAa,CAACkC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC9D,WAAW,IAAI8D,KAAK,CAACvD,QAAQ,EAAE;QAC9CuD,KAAK,CAACvD,QAAQ,GAAG,KAAK;QACtB,IAAI,CAACsB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoC,MAAM,CAACT,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;MACpF;IACF,CAAC,CAAC;IAEF,IAAI,CAAClB,eAAe,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAJ,mBAAmBA,CAACqC,KAAsB;IACxC,IAAI,CAACjC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoC,MAAM,CAACT,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;IAElF;IACA,MAAMa,cAAc,GAAG,IAAI,CAAC5B,eAAe,CAAC6B,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKQ,KAAK,CAACR,QAAQ,CAAC;IACpF,IAAIa,cAAc,EAAE;MAClBA,cAAc,CAAC5D,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACmD,sBAAsB,EAAE;IAC7B,IAAI,CAACtB,eAAe,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC;EACpD;EAEA;EACA6B,sBAAsBA,CAAA;IACpB,IAAI,CAAC1D,WAAW,GAAG,IAAI,CAACuC,eAAe,CAACT,MAAM,GAAG,CAAC,IAChD,IAAI,CAACS,eAAe,CAAC8B,KAAK,CAACP,KAAK,IAAIA,KAAK,CAACvD,QAAQ,CAAC;EACvD;EAEA;EACAW,YAAYA,CAACoD,IAAY;IACvB,IAAI,CAACrD,SAAS,GAAGqD,IAAI;IACrB,IAAI,CAAC7B,mBAAmB,EAAE;EAC5B;EAEA;EACA,IAAI8B,UAAUA,CAAA;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACpD,YAAY,GAAG,IAAI,CAACD,QAAQ,CAAC;EACrD;;;uCA3IWW,oBAAoB,EAAA7C,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApB7C,oBAAoB;MAAA8C,SAAA;MAAAC,MAAA;QAAAjD,aAAA;QAAAK,QAAA;QAAAC,WAAA;MAAA;MAAA4C,OAAA;QAAA3C,eAAA;MAAA;MAAA4C,UAAA;MAAAC,QAAA,GAAA/F,EAAA,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBvBtG,EANV,CAAAC,cAAA,aAAoC,aAED,aACd,aACO,oBACL,eAE4D;UADED,EAAA,CAAA0B,gBAAA,2BAAA8E,6DAAA5E,MAAA;YAAA5B,EAAA,CAAA8B,kBAAA,CAAAyE,GAAA,CAAApD,aAAA,EAAAvB,MAAA,MAAA2E,GAAA,CAAApD,aAAA,GAAAvB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACpG5B,EAAA,CAAAE,UAAA,yBAAAuG,2DAAA;YAAA,OAAeF,GAAA,CAAA7B,QAAA,EAAU;UAAA,EAAC;UAEhC1E,EAHI,CAAAU,YAAA,EACyE,EAC3D,EACZ;UAGFV,EAFJ,CAAAC,cAAA,aAAsB,oBACL,eAE4D;UADED,EAAA,CAAA0B,gBAAA,2BAAAgF,6DAAA9E,MAAA;YAAA5B,EAAA,CAAA8B,kBAAA,CAAAyE,GAAA,CAAAnD,cAAA,EAAAxB,MAAA,MAAA2E,GAAA,CAAAnD,cAAA,GAAAxB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UACrG5B,EAAA,CAAAE,UAAA,yBAAAyG,2DAAA;YAAA,OAAeJ,GAAA,CAAA7B,QAAA,EAAU;UAAA,EAAC;UAEhC1E,EAHI,CAAAU,YAAA,EACyE,EAC3D,EACZ;UAEJV,EADF,CAAAC,cAAA,aAAsB,iBACqE;UAAnCD,EAAA,CAAAE,UAAA,mBAAA0G,uDAAA;YAAA,OAASL,GAAA,CAAA5B,OAAA,EAAS;UAAA,EAAC;UACvE3E,EAAA,CAAAyB,SAAA,kBAA0C;UAC5CzB,EAAA,CAAAU,YAAA,EAAS;UACTV,EAAA,CAAAC,cAAA,iBAAuE;UAA9BD,EAAA,CAAAE,UAAA,mBAAA2G,uDAAA;YAAA,OAASN,GAAA,CAAA7B,QAAA,EAAU;UAAA,EAAC;UAC3D1E,EAAA,CAAAyB,SAAA,mBAAyC;UAIjDzB,EAHM,CAAAU,YAAA,EAAS,EACL,EACF,EACF;UAKJV,EAFF,CAAAC,cAAA,eAAsF,eAEhB;UAKlED,EAJA,CAAAyC,UAAA,KAAAqE,oCAAA,kBAAwD,KAAAC,oCAAA,kBAIR;UAGhD/G,EAAA,CAAAC,cAAA,iBAA0B;UACxBD,EAAA,CAAAW,MAAA,IACF;UACFX,EADE,CAAAU,YAAA,EAAQ,EACJ;UAGNV,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAyC,UAAA,KAAAuE,oCAAA,kBACwC;UAM1ChH,EAAA,CAAAU,YAAA,EAAM;UASNV,EANA,CAAAyC,UAAA,KAAAwE,oCAAA,kBAA8E,KAAAC,oCAAA,kBAME;UAKlFlH,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAyC,UAAA,KAAA0E,oCAAA,kBAAgG;UAclGnH,EAAA,CAAAU,YAAA,EAAM;;;UA7E+EV,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAiC,gBAAA,YAAAsE,GAAA,CAAApD,aAAA,CAA2B;UAM3BnD,EAAA,CAAAY,SAAA,GAA4B;UAA5BZ,EAAA,CAAAiC,gBAAA,YAAAsE,GAAA,CAAAnD,cAAA,CAA4B;UAmBnEpD,EAAA,CAAAY,SAAA,GAAc;UAAdZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAAvD,QAAA,CAAc;UAIhDhD,EAAA,CAAAY,SAAA,EAAe;UAAfZ,EAAA,CAAAa,UAAA,UAAA0F,GAAA,CAAAvD,QAAA,CAAe;UAInBhD,EAAA,CAAAY,SAAA,GACF;UADEZ,EAAA,CAAAoH,kBAAA,aAAAb,GAAA,CAAApE,YAAA,0BAAAoE,GAAA,CAAAxE,SAAA,SAAAwE,GAAA,CAAAlB,UAAA,aACF;UAK0CrF,EAAA,CAAAY,SAAA,GAAkB;UAAlBZ,EAAA,CAAAa,UAAA,YAAA0F,GAAA,CAAAlD,eAAA,CAAkB;UAUxDrD,EAAA,CAAAY,SAAA,EAAkC;UAAlCZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAAlD,eAAA,CAAAT,MAAA,OAAkC;UAMS5C,EAAA,CAAAY,SAAA,EAA6B;UAA7BZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAApE,YAAA,GAAAoE,GAAA,CAAArE,QAAA,CAA6B;UAQ1ElC,EAAA,CAAAY,SAAA,EAA8B;UAA9BZ,EAAA,CAAAa,UAAA,SAAA0F,GAAA,CAAA5D,aAAA,CAAAC,MAAA,KAA8B;;;qBDlDlCpD,YAAY,EAAA6H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9H,WAAW,EAAA+H,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXjI,iBAAiB,EAAAkI,EAAA,CAAAC,oBAAA,EACjBlI,aAAa,EAAAiI,EAAA,CAAAE,gBAAA,EACblI,cAAc,EAAAgI,EAAA,CAAAG,iBAAA,EACdlI,YAAY,EAAA+H,EAAA,CAAAI,eAAA,EACZlI,mBAAmB;MAAAmI,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}