{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule } from '@nebular/theme';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nlet SpaceTemplateSelectorComponent = class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.CTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n    // 新增：模板類型選擇相關屬性\n    this.selectedTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\n  }\n  ngOnInit() {\n    // 初始化選擇的模板類型\n    this.selectedTemplateType = this.CTemplateType;\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數，使用當前選擇的模板類型\n    const getTemplateListArgs = {\n      CTemplateType: this.selectedTemplateType,\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  // 新增：當模板類型選擇變更時的處理\n  onTemplateTypeChange() {\n    // 清空當前選擇\n    this.resetSelections();\n    // 重新載入對應類型的模板\n    this.loadTemplatesFromAPI();\n  }\n  // 新增：獲取當前選擇的模板類型名稱\n  getCurrentTemplateTypeName() {\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const progressTexts = {\n      1: `請選擇要套用的${templateTypeName}項目`,\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: templateTypeName,\n      selectedItems: this.getSelectedItems(),\n      templateDetails: new Map(this.selectedTemplateDetails),\n      // 傳遞已載入的模板明細\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n};\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"CTemplateType\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"templateApplied\", void 0);\nSpaceTemplateSelectorComponent = __decorate([Component({\n  selector: 'app-space-template-selector',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule],\n  templateUrl: './space-template-selector.component.html',\n  styleUrls: ['./space-template-selector.component.scss']\n})], SpaceTemplateSelectorComponent);\nexport { SpaceTemplateSelectorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "EnumTemplateType", "EnumTemplateTypeHelper", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "CTemplateType", "SpaceTemplate", "templateApplied", "currentStep", "templates", "selectedTemplateDetails", "Map", "selectedTemplateType", "templateTypeOptions", "getTemplateTypeList", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "selected", "error", "onTemplateItemChange", "onTemplateTypeChange", "resetSelections", "getCurrentTemplateTypeName", "getDisplayName", "getSelectedItems", "filter", "getSelectedTotalPrice", "canProceed", "length", "nextStep", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "CTemplateId", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "getTemplateDetails", "get", "previousStep", "getProgressText", "templateTypeName", "progressTexts", "hasConflicts", "getConflictCount", "applyTemplate", "config", "spaceId", "spaceName", "templateDetails", "totalPrice", "emit", "close", "reset", "template", "clear", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef,\r\n  NbSelectModule\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  // 新增：模板類型選擇相關屬性\r\n  selectedTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 初始化選擇的模板類型\r\n    this.selectedTemplateType = this.CTemplateType;\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數，使用當前選擇的模板類型\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.selectedTemplateType,\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  // 新增：當模板類型選擇變更時的處理\r\n  onTemplateTypeChange() {\r\n    // 清空當前選擇\r\n    this.resetSelections();\r\n    // 重新載入對應類型的模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  // 新增：獲取當前選擇的模板類型名稱\r\n  getCurrentTemplateTypeName(): string {\r\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const progressTexts = {\r\n      1: `請選擇要套用的${templateTypeName}項目`,\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: templateTypeName,\r\n      selectedItems: this.getSelectedItems(),\r\n      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,cAAc,QACT,gBAAgB;AAGvB,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;AA8BxF,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EAazCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAdV,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAWP,gBAAgB,CAACQ,aAAa;IACrD,KAAAC,eAAe,GAAG,IAAInB,YAAY,EAAuB;IAEnE,KAAAoB,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;IAExE;IACA,KAAAC,oBAAoB,GAAWd,gBAAgB,CAACQ,aAAa;IAC7D,KAAAO,mBAAmB,GAAGd,sBAAsB,CAACe,mBAAmB,EAAE;EAK9D;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACP,aAAa;IAC9C;IACA,IAAI,CAACW,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CZ,aAAa,EAAE,IAAI,CAACO,oBAAoB;MACxCM,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAClB,eAAe,CAACmB,4CAA4C,CAAC;MAChEC,IAAI,EAAEL;KACP,CAAC,CAACM,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAAClB,SAAS,GAAGgB,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACrB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACtB,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAuB,oBAAoBA,CAAA;IAClB;EAAA;EAGF;EACAC,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACC,eAAe,EAAE;IACtB;IACA,IAAI,CAAClB,oBAAoB,EAAE;EAC7B;EAEA;EACAmB,0BAA0BA,CAAA;IACxB,OAAOpC,sBAAsB,CAACqC,cAAc,CAAC,IAAI,CAACxB,oBAAoB,CAAC;EACzE;EAEAyB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5B,SAAS,CAAC6B,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;EACrD;EAEAS,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAAChC,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC6B,gBAAgB,EAAE,CAACI,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACF,UAAU,EAAE,IAAI,IAAI,CAAChC,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACmC,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACnC,WAAW,EAAE;IACpB;EACF;EAEAmC,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAACP,gBAAgB,EAAE;IAE7CO,aAAa,CAACC,OAAO,CAAChB,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAACiB,WAAW,IAAI,CAAC,IAAI,CAACpC,uBAAuB,CAACqC,GAAG,CAAClB,IAAI,CAACiB,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACE,sBAAsB,CAACnB,IAAI,CAACiB,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAC/C,eAAe,CAACiD,yCAAyC,CAAC;MAC7D7B,IAAI,EAAE4B;KACP,CAAC,CAAC3B,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACjB,uBAAuB,CAAC0C,GAAG,CAACH,UAAU,EAAExB,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDI,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACrB,uBAAuB,CAAC0C,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAI,kBAAkBA,CAACJ,UAAkB;IACnC,OAAO,IAAI,CAACvC,uBAAuB,CAAC4C,GAAG,CAACL,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEAM,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/C,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAgD,eAAeA,CAAA;IACb,MAAMC,gBAAgB,GAAG1D,sBAAsB,CAACqC,cAAc,CAAC,IAAI,CAACxB,oBAAoB,CAAC;IACzF,MAAM8C,aAAa,GAAG;MACpB,CAAC,EAAE,UAAUD,gBAAgB,IAAI;MACjC,CAAC,EAAE;KACJ;IACD,OAAOC,aAAa,CAAC,IAAI,CAAClD,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAmD,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACtB,gBAAgB,EAAE,CAACI,MAAM,GAAG,CAAC;EAC3C;EAEAmB,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACD,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAE,aAAaA,CAAA;IACX,MAAMJ,gBAAgB,GAAG1D,sBAAsB,CAACqC,cAAc,CAAC,IAAI,CAACxB,oBAAoB,CAAC;IACzF,MAAMkD,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAEP,gBAAgB;MAC3Bb,aAAa,EAAE,IAAI,CAACP,gBAAgB,EAAE;MACtC4B,eAAe,EAAE,IAAItD,GAAG,CAAC,IAAI,CAACD,uBAAuB,CAAC;MAAE;MACxDwD,UAAU,EAAE,IAAI,CAAC3B,qBAAqB;KACvC;IAED,IAAI,CAAChC,eAAe,CAAC4D,IAAI,CAACL,MAAM,CAAC;IACjC,IAAI,CAACM,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAClC,eAAe,EAAE;IACtB,IAAI,CAAC/B,SAAS,CAACiE,KAAK,EAAE;EACxB;EAEA;EACA;EAEQC,KAAKA,CAAA;IACX,IAAI,CAAC7D,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;EAEQyB,eAAeA,CAAA;IACrB,IAAI,CAAC1B,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,SAAS,CAACoC,OAAO,CAACyB,QAAQ,IAAG;MAChCA,QAAQ,CAACxC,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAACpB,uBAAuB,CAAC6D,KAAK,EAAE;EACtC;CACD;AAvMUC,UAAA,EAARnF,KAAK,EAAE,C,kEAA0B;AACzBmF,UAAA,EAARnF,KAAK,EAAE,C,oEAAwD;AACtDmF,UAAA,EAATlF,MAAM,EAAE,C,sEAA2D;AAHzDU,8BAA8B,GAAAwE,UAAA,EAf1CrF,SAAS,CAAC;EACTsF,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPpF,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,CACf;EACD+E,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EACW7E,8BAA8B,CAwM1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}