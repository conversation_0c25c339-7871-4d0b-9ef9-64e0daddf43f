{"ast": null, "code": "export var EnumTemplateType;\n(function (EnumTemplateType) {\n  EnumTemplateType[EnumTemplateType[\"CustomerChangeRequirement\"] = 1] = \"CustomerChangeRequirement\";\n  EnumTemplateType[EnumTemplateType[\"ItemTemplate\"] = 2] = \"ItemTemplate\";\n  EnumTemplateType[EnumTemplateType[\"SpaceTemplate\"] = 3] = \"SpaceTemplate\"; // 空間模板\n})(EnumTemplateType || (EnumTemplateType = {}));\nexport class EnumTemplateTypeHelper {\n  static getDisplayName(templateType) {\n    switch (templateType) {\n      case EnumTemplateType.CustomerChangeRequirement:\n        return '客變需求';\n      case EnumTemplateType.ItemTemplate:\n        return '項目模板';\n      case EnumTemplateType.SpaceTemplate:\n        return '空間模板';\n      default:\n        return '未知';\n    }\n  }\n  static getTemplateTypeList() {\n    return [{\n      value: EnumTemplateType.CustomerChangeRequirement,\n      label: '客變需求'\n    }, {\n      value: EnumTemplateType.ItemTemplate,\n      label: '項目模板'\n    }, {\n      value: EnumTemplateType.SpaceTemplate,\n      label: '空間模板'\n    }];\n  }\n}", "map": {"version": 3, "names": ["EnumTemplateType", "EnumTemplateTypeHelper", "getDisplayName", "templateType", "CustomerChangeRequirement", "ItemTemplate", "SpaceTemplate", "getTemplateTypeList", "value", "label"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumTemplateType.ts"], "sourcesContent": ["export enum EnumTemplateType {\n  CustomerChangeRequirement = 1, // 客變需求\n  ItemTemplate = 2,              // 項目模板\n  SpaceTemplate = 3              // 空間模板\n}\n\nexport class EnumTemplateTypeHelper {\n  static getDisplayName(templateType: EnumTemplateType): string {\n    switch (templateType) {\n      case EnumTemplateType.CustomerChangeRequirement:\n        return '客變需求';\n      case EnumTemplateType.ItemTemplate:\n        return '項目模板';\n      case EnumTemplateType.SpaceTemplate:\n        return '空間模板';\n      default:\n        return '未知';\n    }\n  }\n\n  static getTemplateTypeList(): Array<{ value: EnumTemplateType; label: string }> {\n    return [\n      { value: EnumTemplateType.CustomerChangeRequirement, label: '客變需求' },\n      { value: EnumTemplateType.ItemTemplate, label: '項目模板' },\n      { value: EnumTemplateType.SpaceTemplate, label: '空間模板' }\n    ];\n  }\n}\n"], "mappings": "AAAA,WAAYA,gBAIX;AAJD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,gEAA6B;EAC7BA,gBAAA,CAAAA,gBAAA,sCAAgB;EAChBA,gBAAA,CAAAA,gBAAA,wCAAiB,EAAc;AACjC,CAAC,EAJWA,gBAAgB,KAAhBA,gBAAgB;AAM5B,OAAM,MAAOC,sBAAsB;EACjC,OAAOC,cAAcA,CAACC,YAA8B;IAClD,QAAQA,YAAY;MAClB,KAAKH,gBAAgB,CAACI,yBAAyB;QAC7C,OAAO,MAAM;MACf,KAAKJ,gBAAgB,CAACK,YAAY;QAChC,OAAO,MAAM;MACf,KAAKL,gBAAgB,CAACM,aAAa;QACjC,OAAO,MAAM;MACf;QACE,OAAO,IAAI;IACf;EACF;EAEA,OAAOC,mBAAmBA,CAAA;IACxB,OAAO,CACL;MAAEC,KAAK,EAAER,gBAAgB,CAACI,yBAAyB;MAAEK,KAAK,EAAE;IAAM,CAAE,EACpE;MAAED,KAAK,EAAER,gBAAgB,CAACK,YAAY;MAAEI,KAAK,EAAE;IAAM,CAAE,EACvD;MAAED,KAAK,EAAER,gBAAgB,CAACM,aAAa;MAAEG,KAAK,EAAE;IAAM,CAAE,CACzD;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}