{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiSpaceDeleteSpacePost$Json } from '../fn/space/api-space-delete-space-post-json';\nimport { apiSpaceDeleteSpacePost$Plain } from '../fn/space/api-space-delete-space-post-plain';\nimport { apiSpaceGetSpaceByIdPost$Json } from '../fn/space/api-space-get-space-by-id-post-json';\nimport { apiSpaceGetSpaceByIdPost$Plain } from '../fn/space/api-space-get-space-by-id-post-plain';\nimport { apiSpaceGetSpaceListForTemplatePost$Json } from '../fn/space/api-space-get-space-list-for-template-post-json';\nimport { apiSpaceGetSpaceListForTemplatePost$Plain } from '../fn/space/api-space-get-space-list-for-template-post-plain';\nimport { apiSpaceGetSpaceListPost$Json } from '../fn/space/api-space-get-space-list-post-json';\nimport { apiSpaceGetSpaceListPost$Plain } from '../fn/space/api-space-get-space-list-post-plain';\nimport { apiSpaceSaveSpacePost$Json } from '../fn/space/api-space-save-space-post-json';\nimport { apiSpaceSaveSpacePost$Plain } from '../fn/space/api-space-save-space-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let SpaceService = /*#__PURE__*/(() => {\n  class SpaceService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiSpaceGetSpaceListPost()` */\n    static {\n      this.ApiSpaceGetSpaceListPostPath = '/api/Space/GetSpaceList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceGetSpaceListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListPost$Plain$Response(params, context) {\n      return apiSpaceGetSpaceListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListPost$Plain(params, context) {\n      return this.apiSpaceGetSpaceListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceGetSpaceListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListPost$Json$Response(params, context) {\n      return apiSpaceGetSpaceListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListPost$Json(params, context) {\n      return this.apiSpaceGetSpaceListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpaceGetSpaceByIdPost()` */\n    static {\n      this.ApiSpaceGetSpaceByIdPostPath = '/api/Space/GetSpaceById';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceGetSpaceByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceByIdPost$Plain$Response(params, context) {\n      return apiSpaceGetSpaceByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceByIdPost$Plain(params, context) {\n      return this.apiSpaceGetSpaceByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceGetSpaceByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceByIdPost$Json$Response(params, context) {\n      return apiSpaceGetSpaceByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceByIdPost$Json(params, context) {\n      return this.apiSpaceGetSpaceByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpaceSaveSpacePost()` */\n    static {\n      this.ApiSpaceSaveSpacePostPath = '/api/Space/SaveSpace';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceSaveSpacePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceSaveSpacePost$Plain$Response(params, context) {\n      return apiSpaceSaveSpacePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceSaveSpacePost$Plain(params, context) {\n      return this.apiSpaceSaveSpacePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceSaveSpacePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceSaveSpacePost$Json$Response(params, context) {\n      return apiSpaceSaveSpacePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceSaveSpacePost$Json(params, context) {\n      return this.apiSpaceSaveSpacePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpaceDeleteSpacePost()` */\n    static {\n      this.ApiSpaceDeleteSpacePostPath = '/api/Space/DeleteSpace';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceDeleteSpacePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceDeleteSpacePost$Plain$Response(params, context) {\n      return apiSpaceDeleteSpacePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceDeleteSpacePost$Plain(params, context) {\n      return this.apiSpaceDeleteSpacePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceDeleteSpacePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceDeleteSpacePost$Json$Response(params, context) {\n      return apiSpaceDeleteSpacePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceDeleteSpacePost$Json(params, context) {\n      return this.apiSpaceDeleteSpacePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpaceGetSpaceListForTemplatePost()` */\n    static {\n      this.ApiSpaceGetSpaceListForTemplatePostPath = '/api/Space/GetSpaceListForTemplate';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListForTemplatePost$Plain$Response(params, context) {\n      return apiSpaceGetSpaceListForTemplatePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListForTemplatePost$Plain(params, context) {\n      return this.apiSpaceGetSpaceListForTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListForTemplatePost$Json$Response(params, context) {\n      return apiSpaceGetSpaceListForTemplatePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpaceGetSpaceListForTemplatePost$Json(params, context) {\n      return this.apiSpaceGetSpaceListForTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function SpaceService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpaceService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SpaceService,\n        factory: SpaceService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SpaceService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}