{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/space.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction SpacePickerComponent_nb_icon_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-icon\", 34);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_nb_icon_15_Template_nb_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.searchKeyword = \"\";\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_nb_icon_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-icon\", 34);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_nb_icon_22_Template_nb_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.searchLocation = \"\";\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_nb_icon_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 35);\n  }\n}\nfunction SpacePickerComponent_nb_icon_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 36);\n  }\n}\nfunction SpacePickerComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"input\", 38);\n    i0.ɵɵlistener(\"change\", function SpacePickerComponent_div_38_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 39);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.allSelected);\n  }\n}\nfunction SpacePickerComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_43_Template_div_click_0_listener() {\n      const space_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSpaceSelection(space_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 42)(2, \"div\", 43);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r6.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r6.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r6.CLocation || \"-\");\n  }\n}\nfunction SpacePickerComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"nb-icon\", 46);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"ngx-pagination\", 48);\n    i0.ɵɵtwoWayListener(\"PageChange\", function SpacePickerComponent_div_45_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pageIndex, $event) || (ctx_r1.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function SpacePickerComponent_div_45_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r1.pageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r1.pageSize)(\"CollectionSize\", ctx_r1.totalRecords);\n  }\n}\nfunction SpacePickerComponent_div_46_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_46_span_5_Template_button_click_2_listener() {\n      const space_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeSelectedSpace(space_r9));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r9.CPart, \" \");\n  }\n}\nfunction SpacePickerComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"h6\", 50);\n    i0.ɵɵelement(2, \"nb-icon\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtemplate(5, SpacePickerComponent_div_46_span_5_Template, 3, 1, \"span\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u7A7A\\u9593 (\", ctx_r1.selectedItems.length, \" \\u9805) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedItems);\n  }\n}\nexport class SpacePickerComponent {\n  constructor(spaceService) {\n    this.spaceService = spaceService;\n    this.selectedItems = [];\n    this.multiple = true;\n    this.placeholder = '請選擇空間';\n    this.selectionChange = new EventEmitter();\n    // 搜尋相關屬性\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    // 分頁相關屬性\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.totalRecords = 0;\n    // 資料相關屬性\n    this.availableSpaces = [];\n    this.allSelected = false;\n  }\n  ngOnInit() {\n    this.loadAvailableSpaces();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this.spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n        this.updateAllSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 切換空間選擇\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({\n            ...space\n          });\n        } else {\n          this.selectedItems = [{\n            ...space\n          }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 全選/取消全選\n  toggleAllSpaces() {\n    this.allSelected = !this.allSelected;\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({\n            ...space\n          });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 移除已選空間\n  removeSelectedSpace(space) {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 更新全選狀態\n  updateAllSelectedState() {\n    this.allSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page) {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 計算總頁數\n  get totalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  static {\n    this.ɵfac = function SpacePickerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpacePickerComponent)(i0.ɵɵdirectiveInject(i1.SpaceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpacePickerComponent,\n      selectors: [[\"app-space-picker\"]],\n      inputs: {\n        selectedItems: \"selectedItems\",\n        multiple: \"multiple\",\n        placeholder: \"placeholder\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 47,\n      vars: 20,\n      consts: [[1, \"space-picker-container\"], [1, \"search-section\"], [\"size\", \"medium\"], [1, \"pb-2\"], [1, \"mb-0\", \"text-muted\"], [\"icon\", \"search-outline\", 1, \"me-2\"], [1, \"pt-2\"], [1, \"row\", \"g-3\"], [1, \"col-lg-5\", \"col-md-6\"], [1, \"form-label\", \"text-muted\", \"mb-2\"], [\"nbPrefix\", \"\", \"icon\", \"cube-outline\", 1, \"text-muted\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [\"nbSuffix\", \"\", \"icon\", \"close-outline\", \"class\", \"cursor-pointer text-muted\", \"title\", \"\\u6E05\\u9664\", 3, \"click\", 4, \"ngIf\"], [\"nbPrefix\", \"\", \"icon\", \"pin-outline\", 1, \"text-muted\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [1, \"col-lg-2\", \"col-md-12\"], [1, \"form-label\", \"text-muted\", \"mb-2\", \"d-none\", \"d-md-block\"], [1, \"search-actions\", \"d-flex\", \"gap-2\"], [\"nbButton\", \"\", \"ghost\", \"\", \"status\", \"basic\", \"size\", \"medium\", \"title\", \"\\u91CD\\u7F6E\", 1, \"flex-fill\", 3, \"click\", \"disabled\"], [\"icon\", \"refresh-outline\"], [1, \"d-none\", \"d-lg-inline\", \"ms-1\"], [\"nbButton\", \"\", \"status\", \"primary\", \"size\", \"medium\", \"title\", \"\\u641C\\u5C0B\", 1, \"flex-fill\", 3, \"click\", \"disabled\"], [\"icon\", \"search-outline\", 4, \"ngIf\"], [\"icon\", \"loader-outline\", \"class\", \"spinning\", 4, \"ngIf\"], [1, \"space-list-section\", \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"font-weight-bold\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"selected-summary mt-3 p-3 bg-light border rounded\", 4, \"ngIf\"], [\"nbSuffix\", \"\", \"icon\", \"close-outline\", \"title\", \"\\u6E05\\u9664\", 1, \"cursor-pointer\", \"text-muted\", 3, \"click\"], [\"icon\", \"search-outline\"], [\"icon\", \"loader-outline\", 1, \"spinning\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", \"id\", \"selectAllSpaces\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllSpaces\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"font-weight-bold\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"selected-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"mb-2\"], [\"icon\", \"checkmark-circle-outline\", 1, \"text-success\", \"me-2\"], [1, \"selected-spaces-list\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", \"aria-label\", \"\\u79FB\\u9664\", 1, \"btn-close\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"]],\n      template: function SpacePickerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nb-card\", 2)(3, \"nb-card-header\", 3)(4, \"h6\", 4);\n          i0.ɵɵelement(5, \"nb-icon\", 5);\n          i0.ɵɵtext(6, \" \\u641C\\u5C0B\\u689D\\u4EF6 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"nb-card-body\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"label\", 9);\n          i0.ɵɵtext(11, \"\\u9805\\u76EE\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"nb-form-field\");\n          i0.ɵɵelement(13, \"nb-icon\", 10);\n          i0.ɵɵelementStart(14, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_14_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, SpacePickerComponent_nb_icon_15_Template, 1, 0, \"nb-icon\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 8)(17, \"label\", 9);\n          i0.ɵɵtext(18, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"nb-form-field\");\n          i0.ɵɵelement(20, \"nb-icon\", 13);\n          i0.ɵɵelementStart(21, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_21_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SpacePickerComponent_nb_icon_22_Template, 1, 0, \"nb-icon\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"label\", 16);\n          i0.ɵɵtext(25, \"\\u00A0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 17)(27, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_27_listener() {\n            return ctx.onReset();\n          });\n          i0.ɵɵelement(28, \"nb-icon\", 19);\n          i0.ɵɵelementStart(29, \"span\", 20);\n          i0.ɵɵtext(30, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_31_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵtemplate(32, SpacePickerComponent_nb_icon_32_Template, 1, 0, \"nb-icon\", 22)(33, SpacePickerComponent_nb_icon_33_Template, 1, 0, \"nb-icon\", 23);\n          i0.ɵɵelementStart(34, \"span\", 20);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelementStart(36, \"div\", 24)(37, \"div\", 25);\n          i0.ɵɵtemplate(38, SpacePickerComponent_div_38_Template, 4, 1, \"div\", 26)(39, SpacePickerComponent_div_39_Template, 2, 0, \"div\", 27);\n          i0.ɵɵelementStart(40, \"small\", 28);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 29);\n          i0.ɵɵtemplate(43, SpacePickerComponent_div_43_Template, 6, 4, \"div\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(44, SpacePickerComponent_div_44_Template, 3, 0, \"div\", 31)(45, SpacePickerComponent_div_45_Template, 2, 3, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, SpacePickerComponent_div_46_Template, 6, 2, \"div\", 33);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchLocation);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isLoading ? \"\\u641C\\u5C0B\\u4E2D...\" : \"\\u641C\\u5C0B\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx.totalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx.pageIndex, \" / \", ctx.totalPages, \" \\u9801 \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableSpaces);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableSpaces.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbFormFieldModule, i4.NbFormFieldComponent, i4.NbPrefixDirective, i4.NbSuffixDirective, NbInputModule, i4.NbInputDirective, NbButtonModule, i4.NbButtonComponent, NbIconModule, i4.NbIconComponent, PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #edf1f7;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #edf1f7;\\n  background-color: #f8f9fa;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #6c757d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  color: #6c757d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  height: 40px;\\n  border-radius: 6px;\\n  border: 1px solid #e4e7ea;\\n  background-color: #ffffff;\\n  transition: all 0.2s ease;\\n  font-size: 0.875rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);\\n  background-color: #ffffff;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled {\\n  background-color: #f8f9fa;\\n  border-color: #e9ecef;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #adb5bd;\\n  font-size: 0.875rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbPrefix][_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #6c757d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbSuffix][_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbSuffix][_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 40px;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=basic][_ngcontent-%COMP%] {\\n  border: 1px solid #e4e7ea;\\n  background-color: #ffffff;\\n  color: #6c757d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=basic][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  border-color: #007bff;\\n  color: #007bff;\\n  background-color: #f8f9ff;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=primary][_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=primary][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n  transform: translateY(-1px);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none !important;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   nb-icon.spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 12px;\\n  margin-bottom: 1rem;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: 2px solid #e4e7ea;\\n  border-radius: 8px;\\n  background-color: #ffffff;\\n  text-align: center;\\n  transition: all 0.2s ease;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #2c3e50;\\n  margin-bottom: 4px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #7f8c8d;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #4a90e2;\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);\\n  transform: translateY(-1px);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #28a745;\\n  background-color: #f8fff9;\\n  box-shadow: 0 2px 12px rgba(40, 167, 69, 0.2);\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.8rem;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 20px;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: white;\\n  opacity: 0.8;\\n  cursor: pointer;\\n  padding: 0;\\n  margin: 0;\\n  width: 12px;\\n  height: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u00D7\\\";\\n  font-size: 12px;\\n  line-height: 1;\\n}\\n@media (max-width: 1200px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-5[_ngcontent-%COMP%], \\n   .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n    justify-content: stretch;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 0;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem !important;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: inline !important;\\n  }\\n  .space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbFormFieldModule", "NbInputModule", "NbButtonModule", "NbIconModule", "PaginationComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpacePickerComponent_nb_icon_15_Template_nb_icon_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "searchKeyword", "ɵɵresetView", "onSearch", "ɵɵelementEnd", "SpacePickerComponent_nb_icon_22_Template_nb_icon_click_0_listener", "_r3", "searchLocation", "ɵɵelement", "SpacePickerComponent_div_38_Template_input_change_1_listener", "_r4", "toggleAllSpaces", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "allSelected", "SpacePickerComponent_div_43_Template_div_click_0_listener", "space_r6", "_r5", "$implicit", "toggleSpaceSelection", "ɵɵclassProp", "selected", "ɵɵtextInterpolate", "<PERSON>art", "CLocation", "ɵɵtwoWayListener", "SpacePickerComponent_div_45_Template_ngx_pagination_PageChange_1_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "pageIndex", "onPageChange", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "SpacePickerComponent_div_46_span_5_Template_button_click_2_listener", "space_r9", "_r8", "removeSelectedSpace", "ɵɵtextInterpolate1", "ɵɵtemplate", "SpacePickerComponent_div_46_span_5_Template", "selectedItems", "length", "SpacePickerComponent", "constructor", "spaceService", "multiple", "placeholder", "selectionChange", "availableSpaces", "ngOnInit", "loadAvailableSpaces", "request", "CStatus", "PageIndex", "PageSize", "apiSpaceGetSpaceListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CSpaceID", "some", "s", "TotalItems", "updateAllSelectedState", "subscribe", "onReset", "space", "push", "for<PERSON>ach", "filter", "emit", "availableSpace", "find", "every", "page", "totalPages", "Math", "ceil", "ɵɵdirectiveInject", "i1", "SpaceService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpacePickerComponent_Template", "rf", "ctx", "SpacePickerComponent_Template_input_ngModelChange_14_listener", "SpacePickerComponent_Template_input_keyup_enter_14_listener", "SpacePickerComponent_nb_icon_15_Template", "SpacePickerComponent_Template_input_ngModelChange_21_listener", "SpacePickerComponent_Template_input_keyup_enter_21_listener", "SpacePickerComponent_nb_icon_22_Template", "SpacePickerComponent_Template_button_click_27_listener", "SpacePickerComponent_Template_button_click_31_listener", "SpacePickerComponent_nb_icon_32_Template", "SpacePickerComponent_nb_icon_33_Template", "SpacePickerComponent_div_38_Template", "SpacePickerComponent_div_39_Template", "SpacePickerComponent_div_43_Template", "SpacePickerComponent_div_44_Template", "SpacePickerComponent_div_45_Template", "SpacePickerComponent_div_46_Template", "isLoading", "ɵɵtextInterpolate3", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbFormFieldComponent", "NbPrefixDirective", "NbSuffixDirective", "NbInputDirective", "NbButtonComponent", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { SpaceService } from 'src/services/api/services/space.service';\nimport { GetSpaceListResponse } from 'src/services/api/models';\nimport { tap } from 'rxjs/operators';\n\nexport interface SpacePickerItem {\n  CSpaceID: number;\n  CPart: string;\n  CLocation?: string | null;\n  selected?: boolean;\n}\n\n@Component({\n  selector: 'app-space-picker',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbFormFieldModule,\n    NbInputModule,\n    NbButtonModule,\n    NbIconModule,\n    PaginationComponent\n  ],\n  templateUrl: './space-picker.component.html',\n  styleUrls: ['./space-picker.component.scss']\n})\nexport class SpacePickerComponent implements OnInit {\n  @Input() selectedItems: SpacePickerItem[] = [];\n  @Input() multiple: boolean = true;\n  @Input() placeholder: string = '請選擇空間';\n  @Output() selectionChange = new EventEmitter<SpacePickerItem[]>();\n\n  // 搜尋相關屬性\n  searchKeyword: string = '';\n  searchLocation: string = '';\n\n  // 分頁相關屬性\n  pageIndex = 1;\n  pageSize = 10;\n  totalRecords = 0;\n\n  // 資料相關屬性\n  availableSpaces: SpacePickerItem[] = [];\n  allSelected = false;\n\n  constructor(private spaceService: SpaceService) { }\n\n  ngOnInit(): void {\n    this.loadAvailableSpaces();\n  }\n\n  // 載入可用空間列表\n  loadAvailableSpaces(): void {\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1, // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n\n    this.spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\n      tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID!,\n            CPart: item.CPart!,\n            CLocation: item.CLocation,\n            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n          this.updateAllSelectedState();\n        }\n      })\n    ).subscribe();\n  }\n\n  // 搜尋功能\n  onSearch(): void {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 重置搜尋\n  onReset(): void {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 切換空間選擇\n  toggleSpaceSelection(space: SpacePickerItem): void {\n    space.selected = !space.selected;\n\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({ ...space });\n        } else {\n          this.selectedItems = [{ ...space }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 全選/取消全選\n  toggleAllSpaces(): void {\n    this.allSelected = !this.allSelected;\n\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({ ...space });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 移除已選空間\n  removeSelectedSpace(space: SpacePickerItem): void {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 更新全選狀態\n  updateAllSelectedState(): void {\n    this.allSelected = this.availableSpaces.length > 0 &&\n      this.availableSpaces.every(space => space.selected);\n  }\n\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page: number): void {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n\n  // 計算總頁數\n  get totalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n}\n", "<!-- 空間選擇器共用組件 -->\n<div class=\"space-picker-container\">\n  <!-- 搜尋區域 -->\n  <div class=\"search-section\">\n    <nb-card size=\"medium\">\n      <nb-card-header class=\"pb-2\">\n        <h6 class=\"mb-0 text-muted\">\n          <nb-icon icon=\"search-outline\" class=\"me-2\"></nb-icon>\n          搜尋條件\n        </h6>\n      </nb-card-header>\n      <nb-card-body class=\"pt-2\">\n        <div class=\"row g-3\">\n          <div class=\"col-lg-5 col-md-6\">\n            <label class=\"form-label text-muted mb-2\">項目名稱</label>\n            <nb-form-field>\n              <nb-icon nbPrefix icon=\"cube-outline\" class=\"text-muted\"></nb-icon>\n              <input type=\"text\" nbInput placeholder=\"請輸入項目名稱...\" [(ngModel)]=\"searchKeyword\"\n                (keyup.enter)=\"onSearch()\" [disabled]=\"isLoading\" />\n              <nb-icon *ngIf=\"searchKeyword\" nbSuffix icon=\"close-outline\" class=\"cursor-pointer text-muted\"\n                (click)=\"searchKeyword = ''; onSearch()\" title=\"清除\"></nb-icon>\n            </nb-form-field>\n          </div>\n          <div class=\"col-lg-5 col-md-6\">\n            <label class=\"form-label text-muted mb-2\">所屬區域</label>\n            <nb-form-field>\n              <nb-icon nbPrefix icon=\"pin-outline\" class=\"text-muted\"></nb-icon>\n              <input type=\"text\" nbInput placeholder=\"請輸入所屬區域...\" [(ngModel)]=\"searchLocation\"\n                (keyup.enter)=\"onSearch()\" [disabled]=\"isLoading\" />\n              <nb-icon *ngIf=\"searchLocation\" nbSuffix icon=\"close-outline\" class=\"cursor-pointer text-muted\"\n                (click)=\"searchLocation = ''; onSearch()\" title=\"清除\"></nb-icon>\n            </nb-form-field>\n          </div>\n          <div class=\"col-lg-2 col-md-12\">\n            <label class=\"form-label text-muted mb-2 d-none d-md-block\">&nbsp;</label>\n            <div class=\"search-actions d-flex gap-2\">\n              <button nbButton ghost status=\"basic\" size=\"medium\" (click)=\"onReset()\" [disabled]=\"isLoading\"\n                class=\"flex-fill\" title=\"重置\">\n                <nb-icon icon=\"refresh-outline\"></nb-icon>\n                <span class=\"d-none d-lg-inline ms-1\">重置</span>\n              </button>\n              <button nbButton status=\"primary\" size=\"medium\" (click)=\"onSearch()\" [disabled]=\"isLoading\"\n                class=\"flex-fill\" title=\"搜尋\">\n                <nb-icon *ngIf=\"!isLoading\" icon=\"search-outline\"></nb-icon>\n                <nb-icon *ngIf=\"isLoading\" icon=\"loader-outline\" class=\"spinning\"></nb-icon>\n                <span class=\"d-none d-lg-inline ms-1\">{{ isLoading ? '搜尋中...' : '搜尋' }}</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </nb-card-body>\n    </nb-card>\n  </div>\n\n  <!-- 空間列表區域 -->\n  <div class=\"space-list-section border rounded p-3\" style=\"background-color: #f8f9fa;\">\n    <!-- 列表標題和統計 -->\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\n      <div class=\"d-flex align-items-center\" *ngIf=\"multiple\">\n        <input type=\"checkbox\" id=\"selectAllSpaces\" [checked]=\"allSelected\" (change)=\"toggleAllSpaces()\" class=\"me-2\">\n        <label for=\"selectAllSpaces\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\n      </div>\n      <div *ngIf=\"!multiple\" class=\"font-weight-bold\">\n        選擇空間\n      </div>\n      <small class=\"text-muted\">\n        共 {{ totalRecords }} 筆，第 {{ pageIndex }} / {{ totalPages }} 頁\n      </small>\n    </div>\n\n    <!-- 空間項目網格 -->\n    <div class=\"space-grid\">\n      <div class=\"space-item\" *ngFor=\"let space of availableSpaces\" [class.selected]=\"space.selected\"\n        (click)=\"toggleSpaceSelection(space)\">\n        <div class=\"space-card\">\n          <div class=\"space-name\">{{ space.CPart }}</div>\n          <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 空間列表為空時的提示 -->\n    <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\n      <nb-icon icon=\"info-outline\" class=\"me-2\"></nb-icon>\n      沒有符合條件的空間\n    </div>\n\n    <!-- 分頁控制 -->\n    <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"totalRecords > pageSize\">\n      <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\n        (PageChange)=\"onPageChange($event)\">\n      </ngx-pagination>\n    </div>\n  </div>\n\n  <!-- 已選空間摘要 -->\n  <div *ngIf=\"selectedItems.length > 0\" class=\"selected-summary mt-3 p-3 bg-light border rounded\">\n    <h6 class=\"mb-2\">\n      <nb-icon icon=\"checkmark-circle-outline\" class=\"text-success me-2\"></nb-icon>\n      已選空間 ({{ selectedItems.length }} 項)\n    </h6>\n    <div class=\"selected-spaces-list\">\n      <span *ngFor=\"let space of selectedItems\" class=\"badge badge-primary me-1 mb-1\">\n        {{ space.CPart }}\n        <button type=\"button\" class=\"btn-close ms-1\" (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"\n          aria-label=\"移除\">\n        </button>\n      </span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC/F,SAASC,mBAAmB,QAAQ,0DAA0D;AAG9F,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;ICYtBC,EAAA,CAAAC,cAAA,kBACsD;IAApDD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAD,MAAA,CAAAE,aAAA,GAAyB,EAAE;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAEH,MAAA,CAAAI,QAAA,EAAU;IAAA,EAAC;IAAYV,EAAA,CAAAW,YAAA,EAAU;;;;;;IAShEX,EAAA,CAAAC,cAAA,kBACuD;IAArDD,EAAA,CAAAE,UAAA,mBAAAU,kEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAD,MAAA,CAAAQ,cAAA,GAA0B,EAAE;MAAA,OAAAd,EAAA,CAAAS,WAAA,CAAEH,MAAA,CAAAI,QAAA,EAAU;IAAA,EAAC;IAAYV,EAAA,CAAAW,YAAA,EAAU;;;;;IAa/DX,EAAA,CAAAe,SAAA,kBAA4D;;;;;IAC5Df,EAAA,CAAAe,SAAA,kBAA4E;;;;;;IAepFf,EADF,CAAAC,cAAA,cAAwD,gBACwD;IAA1CD,EAAA,CAAAE,UAAA,oBAAAc,6DAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAAUH,MAAA,CAAAY,eAAA,EAAiB;IAAA,EAAC;IAAhGlB,EAAA,CAAAW,YAAA,EAA8G;IAC9GX,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAmB,MAAA,2CAAM;IACnEnB,EADmE,CAAAW,YAAA,EAAQ,EACrE;;;;IAFwCX,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAgB,WAAA,CAAuB;;;;;IAGrEtB,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAmB,MAAA,iCACF;IAAAnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IAQNX,EAAA,CAAAC,cAAA,cACwC;IAAtCD,EAAA,CAAAE,UAAA,mBAAAqB,0DAAA;MAAA,MAAAC,QAAA,GAAAxB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAASH,MAAA,CAAAqB,oBAAA,CAAAH,QAAA,CAA2B;IAAA,EAAC;IAEnCxB,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAmB,MAAA,GAAiB;IAAAnB,EAAA,CAAAW,YAAA,EAAM;IAC/CX,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAmB,MAAA,GAA4B;IAE5DnB,EAF4D,CAAAW,YAAA,EAAM,EAC1D,EACF;;;;IANwDX,EAAA,CAAA4B,WAAA,aAAAJ,QAAA,CAAAK,QAAA,CAAiC;IAGnE7B,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAA8B,iBAAA,CAAAN,QAAA,CAAAO,KAAA,CAAiB;IACb/B,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA8B,iBAAA,CAAAN,QAAA,CAAAQ,SAAA,QAA4B;;;;;IAM9DhC,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAe,SAAA,kBAAoD;IACpDf,EAAA,CAAAmB,MAAA,+DACF;IAAAnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IAIJX,EADF,CAAAC,cAAA,cAAgF,yBAExC;IADtBD,EAAA,CAAAiC,gBAAA,wBAAAC,0EAAAC,MAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAqC,kBAAA,CAAA/B,MAAA,CAAAgC,SAAA,EAAAH,MAAA,MAAA7B,MAAA,CAAAgC,SAAA,GAAAH,MAAA;MAAA,OAAAnC,EAAA,CAAAS,WAAA,CAAA0B,MAAA;IAAA,EAAoB;IAClCnC,EAAA,CAAAE,UAAA,wBAAAgC,0EAAAC,MAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAAcH,MAAA,CAAAiC,YAAA,CAAAJ,MAAA,CAAoB;IAAA,EAAC;IAEvCnC,EADE,CAAAW,YAAA,EAAiB,EACb;;;;IAHYX,EAAA,CAAAoB,SAAA,EAAoB;IAApBpB,EAAA,CAAAwC,gBAAA,SAAAlC,MAAA,CAAAgC,SAAA,CAAoB;IAAuBtC,EAAtB,CAAAqB,UAAA,aAAAf,MAAA,CAAAmC,QAAA,CAAqB,mBAAAnC,MAAA,CAAAoC,YAAA,CAAgC;;;;;;IAa1F1C,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAmB,MAAA,GACA;IAAAnB,EAAA,CAAAC,cAAA,iBACkB;IAD2BD,EAAA,CAAAE,UAAA,mBAAAyC,oEAAA;MAAA,MAAAC,QAAA,GAAA5C,EAAA,CAAAI,aAAA,CAAAyC,GAAA,EAAAnB,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAS,WAAA,CAASH,MAAA,CAAAwC,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAGnF5C,EADE,CAAAW,YAAA,EAAS,EACJ;;;;IAJLX,EAAA,CAAAoB,SAAA,EACA;IADApB,EAAA,CAAA+C,kBAAA,MAAAH,QAAA,CAAAb,KAAA,MACA;;;;;IAPJ/B,EADF,CAAAC,cAAA,cAAgG,aAC7E;IACfD,EAAA,CAAAe,SAAA,kBAA6E;IAC7Ef,EAAA,CAAAmB,MAAA,GACF;IAAAnB,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAgD,UAAA,IAAAC,2CAAA,mBAAgF;IAOpFjD,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAVFX,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA+C,kBAAA,gCAAAzC,MAAA,CAAA4C,aAAA,CAAAC,MAAA,cACF;IAE0BnD,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAA4C,aAAA,CAAgB;;;ADvE9C,OAAM,MAAOE,oBAAoB;EAmB/BC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAlBvB,KAAAJ,aAAa,GAAsB,EAAE;IACrC,KAAAK,QAAQ,GAAY,IAAI;IACxB,KAAAC,WAAW,GAAW,OAAO;IAC5B,KAAAC,eAAe,GAAG,IAAIlE,YAAY,EAAqB;IAEjE;IACA,KAAAiB,aAAa,GAAW,EAAE;IAC1B,KAAAM,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAwB,SAAS,GAAG,CAAC;IACb,KAAAG,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,CAAC;IAEhB;IACA,KAAAgB,eAAe,GAAsB,EAAE;IACvC,KAAApC,WAAW,GAAG,KAAK;EAE+B;EAElDqC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACd9B,KAAK,EAAE,IAAI,CAACvB,aAAa,IAAI,IAAI;MACjCwB,SAAS,EAAE,IAAI,CAAClB,cAAc,IAAI,IAAI;MACtCgD,OAAO,EAAE,CAAC;MAAE;MACZC,SAAS,EAAE,IAAI,CAACzB,SAAS;MACzB0B,QAAQ,EAAE,IAAI,CAACvB;KAChB;IAED,IAAI,CAACa,YAAY,CAACW,6BAA6B,CAAC;MAAEC,IAAI,EAAEL;IAAO,CAAE,CAAC,CAACM,IAAI,CACrEpE,GAAG,CAACqE,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACX,eAAe,GAAGU,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDC,QAAQ,EAAED,IAAI,CAACC,QAAS;UACxB1C,KAAK,EAAEyC,IAAI,CAACzC,KAAM;UAClBC,SAAS,EAAEwC,IAAI,CAACxC,SAAS;UACzBH,QAAQ,EAAE,IAAI,CAACqB,aAAa,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKD,IAAI,CAACC,QAAQ;SACpE,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC/B,YAAY,GAAG0B,QAAQ,CAACQ,UAAU,IAAI,CAAC;QAC5C,IAAI,CAACC,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACApE,QAAQA,CAAA;IACN,IAAI,CAAC4B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACsB,mBAAmB,EAAE;EAC5B;EAEA;EACAmB,OAAOA,CAAA;IACL,IAAI,CAACvE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACM,cAAc,GAAG,EAAE;IACxB,IAAI,CAACwB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACsB,mBAAmB,EAAE;EAC5B;EAEA;EACAjC,oBAAoBA,CAACqD,KAAsB;IACzCA,KAAK,CAACnD,QAAQ,GAAG,CAACmD,KAAK,CAACnD,QAAQ;IAEhC,IAAImD,KAAK,CAACnD,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACqB,aAAa,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,CAAC,EAAE;QAChE,IAAI,IAAI,CAAClB,QAAQ,EAAE;UACjB,IAAI,CAACL,aAAa,CAAC+B,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAAC9B,aAAa,GAAG,CAAC;YAAE,GAAG8B;UAAK,CAAE,CAAC;UACnC;UACA,IAAI,CAACtB,eAAe,CAACwB,OAAO,CAACP,CAAC,IAAG;YAC/B,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,EAAE;cACjCE,CAAC,CAAC9C,QAAQ,GAAG,KAAK;YACpB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiC,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,CAAC;IACpF;IAEA,IAAI,CAACI,sBAAsB,EAAE;IAC7B,IAAI,CAACpB,eAAe,CAAC2B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAClC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAhC,eAAeA,CAAA;IACb,IAAI,CAACI,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACoC,eAAe,CAACwB,OAAO,CAACF,KAAK,IAAG;MACnC,IAAI,IAAI,CAAC1D,WAAW,IAAI,CAAC0D,KAAK,CAACnD,QAAQ,EAAE;QACvCmD,KAAK,CAACnD,QAAQ,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,CAACqB,aAAa,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,CAAC,EAAE;UAChE,IAAI,CAACvB,aAAa,CAAC+B,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC1D,WAAW,IAAI0D,KAAK,CAACnD,QAAQ,EAAE;QAC9CmD,KAAK,CAACnD,QAAQ,GAAG,KAAK;QACtB,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiC,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,CAAC;MACpF;IACF,CAAC,CAAC;IAEF,IAAI,CAAChB,eAAe,CAAC2B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAClC,aAAa,CAAC,CAAC;EACpD;EAEA;EACAJ,mBAAmBA,CAACkC,KAAsB;IACxC,IAAI,CAAC9B,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiC,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,CAAC;IAElF;IACA,MAAMY,cAAc,GAAG,IAAI,CAAC3B,eAAe,CAAC4B,IAAI,CAACX,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKO,KAAK,CAACP,QAAQ,CAAC;IACpF,IAAIY,cAAc,EAAE;MAClBA,cAAc,CAACxD,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACgD,sBAAsB,EAAE;IAC7B,IAAI,CAACpB,eAAe,CAAC2B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAClC,aAAa,CAAC,CAAC;EACpD;EAEA;EACA2B,sBAAsBA,CAAA;IACpB,IAAI,CAACvD,WAAW,GAAG,IAAI,CAACoC,eAAe,CAACP,MAAM,GAAG,CAAC,IAChD,IAAI,CAACO,eAAe,CAAC6B,KAAK,CAACP,KAAK,IAAIA,KAAK,CAACnD,QAAQ,CAAC;EACvD;EAEA;EACAU,YAAYA,CAACiD,IAAY;IACvB,IAAI,CAAClD,SAAS,GAAGkD,IAAI;IACrB,IAAI,CAAC5B,mBAAmB,EAAE;EAC5B;EAEA;EACA,IAAI6B,UAAUA,CAAA;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACjD,YAAY,GAAG,IAAI,CAACD,QAAQ,CAAC;EACrD;;;uCA3IWW,oBAAoB,EAAApD,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApB1C,oBAAoB;MAAA2C,SAAA;MAAAC,MAAA;QAAA9C,aAAA;QAAAK,QAAA;QAAAC,WAAA;MAAA;MAAAyC,OAAA;QAAAxC,eAAA;MAAA;MAAAyC,UAAA;MAAAC,QAAA,GAAAnG,EAAA,CAAAoG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBzB1G,EALR,CAAAC,cAAA,aAAoC,aAEN,iBACH,wBACQ,YACC;UAC1BD,EAAA,CAAAe,SAAA,iBAAsD;UACtDf,EAAA,CAAAmB,MAAA,iCACF;UACFnB,EADE,CAAAW,YAAA,EAAK,EACU;UAIXX,EAHN,CAAAC,cAAA,sBAA2B,aACJ,aACY,gBACa;UAAAD,EAAA,CAAAmB,MAAA,gCAAI;UAAAnB,EAAA,CAAAW,YAAA,EAAQ;UACtDX,EAAA,CAAAC,cAAA,qBAAe;UACbD,EAAA,CAAAe,SAAA,mBAAmE;UACnEf,EAAA,CAAAC,cAAA,iBACsD;UADFD,EAAA,CAAAiC,gBAAA,2BAAA2E,8DAAAzE,MAAA;YAAAnC,EAAA,CAAAqC,kBAAA,CAAAsE,GAAA,CAAAnG,aAAA,EAAA2B,MAAA,MAAAwE,GAAA,CAAAnG,aAAA,GAAA2B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAC7EnC,EAAA,CAAAE,UAAA,yBAAA2G,4DAAA;YAAA,OAAeF,GAAA,CAAAjG,QAAA,EAAU;UAAA,EAAC;UAD5BV,EAAA,CAAAW,YAAA,EACsD;UACtDX,EAAA,CAAAgD,UAAA,KAAA8D,wCAAA,sBACsD;UAE1D9G,EADE,CAAAW,YAAA,EAAgB,EACZ;UAEJX,EADF,CAAAC,cAAA,cAA+B,gBACa;UAAAD,EAAA,CAAAmB,MAAA,gCAAI;UAAAnB,EAAA,CAAAW,YAAA,EAAQ;UACtDX,EAAA,CAAAC,cAAA,qBAAe;UACbD,EAAA,CAAAe,SAAA,mBAAkE;UAClEf,EAAA,CAAAC,cAAA,iBACsD;UADFD,EAAA,CAAAiC,gBAAA,2BAAA8E,8DAAA5E,MAAA;YAAAnC,EAAA,CAAAqC,kBAAA,CAAAsE,GAAA,CAAA7F,cAAA,EAAAqB,MAAA,MAAAwE,GAAA,CAAA7F,cAAA,GAAAqB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAC9EnC,EAAA,CAAAE,UAAA,yBAAA8G,4DAAA;YAAA,OAAeL,GAAA,CAAAjG,QAAA,EAAU;UAAA,EAAC;UAD5BV,EAAA,CAAAW,YAAA,EACsD;UACtDX,EAAA,CAAAgD,UAAA,KAAAiE,wCAAA,sBACuD;UAE3DjH,EADE,CAAAW,YAAA,EAAgB,EACZ;UAEJX,EADF,CAAAC,cAAA,eAAgC,iBAC8B;UAAAD,EAAA,CAAAmB,MAAA,cAAM;UAAAnB,EAAA,CAAAW,YAAA,EAAQ;UAExEX,EADF,CAAAC,cAAA,eAAyC,kBAER;UADqBD,EAAA,CAAAE,UAAA,mBAAAgH,uDAAA;YAAA,OAASP,GAAA,CAAA5B,OAAA,EAAS;UAAA,EAAC;UAErE/E,EAAA,CAAAe,SAAA,mBAA0C;UAC1Cf,EAAA,CAAAC,cAAA,gBAAsC;UAAAD,EAAA,CAAAmB,MAAA,oBAAE;UAC1CnB,EAD0C,CAAAW,YAAA,EAAO,EACxC;UACTX,EAAA,CAAAC,cAAA,kBAC+B;UADiBD,EAAA,CAAAE,UAAA,mBAAAiH,uDAAA;YAAA,OAASR,GAAA,CAAAjG,QAAA,EAAU;UAAA,EAAC;UAGlEV,EADA,CAAAgD,UAAA,KAAAoE,wCAAA,sBAAkD,KAAAC,wCAAA,sBACgB;UAClErH,EAAA,CAAAC,cAAA,gBAAsC;UAAAD,EAAA,CAAAmB,MAAA,IAAiC;UAOrFnB,EAPqF,CAAAW,YAAA,EAAO,EACvE,EACL,EACF,EACF,EACO,EACP,EACN;UAKJX,EAFF,CAAAC,cAAA,eAAsF,eAEhB;UAKlED,EAJA,CAAAgD,UAAA,KAAAsE,oCAAA,kBAAwD,KAAAC,oCAAA,kBAIR;UAGhDvH,EAAA,CAAAC,cAAA,iBAA0B;UACxBD,EAAA,CAAAmB,MAAA,IACF;UACFnB,EADE,CAAAW,YAAA,EAAQ,EACJ;UAGNX,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAgD,UAAA,KAAAwE,oCAAA,kBACwC;UAM1CxH,EAAA,CAAAW,YAAA,EAAM;UASNX,EANA,CAAAgD,UAAA,KAAAyE,oCAAA,kBAA8E,KAAAC,oCAAA,kBAME;UAKlF1H,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAgD,UAAA,KAAA2E,oCAAA,kBAAgG;UAclG3H,EAAA,CAAAW,YAAA,EAAM;;;UA7F4DX,EAAA,CAAAoB,SAAA,IAA2B;UAA3BpB,EAAA,CAAAwC,gBAAA,YAAAmE,GAAA,CAAAnG,aAAA,CAA2B;UAClDR,EAAA,CAAAqB,UAAA,aAAAsF,GAAA,CAAAiB,SAAA,CAAsB;UACzC5H,EAAA,CAAAoB,SAAA,EAAmB;UAAnBpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAAnG,aAAA,CAAmB;UAQuBR,EAAA,CAAAoB,SAAA,GAA4B;UAA5BpB,EAAA,CAAAwC,gBAAA,YAAAmE,GAAA,CAAA7F,cAAA,CAA4B;UACnDd,EAAA,CAAAqB,UAAA,aAAAsF,GAAA,CAAAiB,SAAA,CAAsB;UACzC5H,EAAA,CAAAoB,SAAA,EAAoB;UAApBpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAA7F,cAAA,CAAoB;UAO0Cd,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAqB,UAAA,aAAAsF,GAAA,CAAAiB,SAAA,CAAsB;UAKzB5H,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAqB,UAAA,aAAAsF,GAAA,CAAAiB,SAAA,CAAsB;UAE/E5H,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAAqB,UAAA,UAAAsF,GAAA,CAAAiB,SAAA,CAAgB;UAChB5H,EAAA,CAAAoB,SAAA,EAAe;UAAfpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAAiB,SAAA,CAAe;UACa5H,EAAA,CAAAoB,SAAA,GAAiC;UAAjCpB,EAAA,CAAA8B,iBAAA,CAAA6E,GAAA,CAAAiB,SAAA,4CAAiC;UAazC5H,EAAA,CAAAoB,SAAA,GAAc;UAAdpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAApD,QAAA,CAAc;UAIhDvD,EAAA,CAAAoB,SAAA,EAAe;UAAfpB,EAAA,CAAAqB,UAAA,UAAAsF,GAAA,CAAApD,QAAA,CAAe;UAInBvD,EAAA,CAAAoB,SAAA,GACF;UADEpB,EAAA,CAAA6H,kBAAA,aAAAlB,GAAA,CAAAjE,YAAA,0BAAAiE,GAAA,CAAArE,SAAA,SAAAqE,GAAA,CAAAlB,UAAA,aACF;UAK0CzF,EAAA,CAAAoB,SAAA,GAAkB;UAAlBpB,EAAA,CAAAqB,UAAA,YAAAsF,GAAA,CAAAjD,eAAA,CAAkB;UAUxD1D,EAAA,CAAAoB,SAAA,EAAkC;UAAlCpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAAjD,eAAA,CAAAP,MAAA,OAAkC;UAMSnD,EAAA,CAAAoB,SAAA,EAA6B;UAA7BpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAAjE,YAAA,GAAAiE,GAAA,CAAAlE,QAAA,CAA6B;UAQ1EzC,EAAA,CAAAoB,SAAA,EAA8B;UAA9BpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAAzD,aAAA,CAAAC,MAAA,KAA8B;;;qBD5ElC3D,YAAY,EAAAsI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZvI,WAAW,EAAAwI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX1I,iBAAiB,EAAA2I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,iBAAA,EACjB7I,aAAa,EAAA0I,EAAA,CAAAI,gBAAA,EACb7I,cAAc,EAAAyI,EAAA,CAAAK,iBAAA,EACd7I,YAAY,EAAAwI,EAAA,CAAAM,eAAA,EACZ7I,mBAAmB;MAAA8I,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}