{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule } from '@nebular/theme';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_12_nb_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"nb-checkbox\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const template_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r5.selected, $event) || (template_r5.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTemplateItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 35);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const template_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r5.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r5.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r5.CTemplateId, \"\");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template, 7, 3, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"nb-icon\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u66AB\\u7121\\u53EF\\u7528\\u7684\", ctx_r1.getCurrentTemplateTypeName(), \"\\u9805\\u76EE\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20)(3, \"div\", 21);\n    i0.ɵɵelement(4, \"nb-icon\", 22);\n    i0.ɵɵtext(5, \"\\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-select\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_Template_nb_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedTemplateType, $event) || (ctx_r1.selectedTemplateType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_Template_nb_select_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTemplateTypeChange());\n    });\n    i0.ɵɵtemplate(7, SpaceTemplateSelectorComponent_div_12_nb_option_7_Template, 2, 2, \"nb-option\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 25);\n    i0.ɵɵelement(9, \"nb-icon\", 26);\n    i0.ɵɵtext(10, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 27);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_div_12_Template, 2, 1, \"div\", 28)(13, SpaceTemplateSelectorComponent_div_12_ng_template_13_Template, 3, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noTemplates_r6 = i0.ɵɵreference(14);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedTemplateType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.templateTypeOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templates.length > 0)(\"ngIfElse\", noTemplates_r6);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r7.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60)(4, \"div\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template, 2, 1, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r8 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r7.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r7.CLocation);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"span\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 56);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template, 7, 3, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r1.getTemplateDetails(item_r9.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplateDetails(item_r9.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"nb-icon\", 37);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"h5\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"span\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 51);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template, 6, 2, \"div\", 52)(11, SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const noDetails_r10 = i0.ɵɵreference(12);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r9.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r9.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getTemplateDetails(item_r9.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r10);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"strong\");\n    i0.ɵɵelement(3, \"nb-icon\", 67);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r1.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 38)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"nb-icon\", 39);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"div\", 41);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 42);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_13_div_12_Template, 13, 5, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_div_13_Template, 6, 1, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentTemplateTypeName());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r1.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.CTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n    // 新增：模板類型選擇相關屬性\n    this.selectedTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\n  }\n  ngOnInit() {\n    // 初始化選擇的模板類型\n    this.selectedTemplateType = this.CTemplateType;\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數，使用當前選擇的模板類型\n    const getTemplateListArgs = {\n      CTemplateType: this.selectedTemplateType,\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  // 新增：當模板類型選擇變更時的處理\n  onTemplateTypeChange() {\n    // 清空當前選擇\n    this.resetSelections();\n    // 重新載入對應類型的模板\n    this.loadTemplatesFromAPI();\n  }\n  // 新增：獲取當前選擇的模板類型名稱\n  getCurrentTemplateTypeName() {\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const progressTexts = {\n      1: `請選擇要套用的${templateTypeName}項目`,\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: templateTypeName,\n      selectedItems: this.getSelectedItems(),\n      templateDetails: new Map(this.selectedTemplateDetails),\n      // 傳遞已載入的模板明細\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        CTemplateType: \"CTemplateType\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 16,\n      consts: [[\"noTemplates\", \"\"], [\"noDetails\", \"\"], [1, \"space-template-dialog\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"template-type-selector\"], [1, \"selector-label\"], [\"icon\", \"options-2-outline\", 1, \"mr-2\"], [\"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B\", 1, \"template-type-select\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"section-title\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [1, \"template-list\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"value\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"no-templates\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"no-details\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [\"icon\", \"alert-triangle-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 7)(7, \"div\", 8)(8, \"div\", 9);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_Template, 15, 4, \"div\", 10)(13, SpaceTemplateSelectorComponent_div_13_Template, 14, 4, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 11)(15, \"div\", 12)(16, \"span\");\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_19_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SpaceTemplateSelectorComponent_button_21_Template, 2, 0, \"button\", 15)(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 1, \"button\", 16)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n.space-template-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  background-color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: #ADB5BD;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #2C3E50;\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-radius: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: #FFFFFF;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  margin: 0 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: 0.3s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  color: #ADB5BD;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #AE9B66;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%] {\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  padding: 1rem;\\n  background-color: #F8F9FA;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #AE9B66;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .template-type-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 300px;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .template-type-select[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  transition: 0.3s ease;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  border-color: #AE9B66;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  background-color: rgba(184, 166, 118, 0.03);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #A69660;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-id[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #28A745;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%]   .detail-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  border-radius: 50%;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%] {\\n  background-color: #FFF3CD;\\n  border: 1px solid #FFC107;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-top: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%] {\\n  color: #E0A800;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  color: #FFC107;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #E9ECEF;\\n  background-color: #F8F9FA;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #ADB5BD;\\n  font-size: 0.875rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #AE9B66;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  border-color: #28A745;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1E7E34;\\n  border-color: #1E7E34;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-color: #CDCDCD;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-color: #5A5A5A;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .space-template-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-type-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-top-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "TemplateGetListResponse", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r3", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "template_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r1", "ɵɵnextContext", "onTemplateItemChange", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "CTemplateId", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_12_div_12_div_1_Template", "templates", "ɵɵelement", "getCurrentTemplateTypeName", "SpaceTemplateSelectorComponent_div_12_Template_nb_select_ngModelChange_6_listener", "_r1", "selectedTemplateType", "onTemplateTypeChange", "SpaceTemplateSelectorComponent_div_12_nb_option_7_Template", "SpaceTemplateSelectorComponent_div_12_div_12_Template", "SpaceTemplateSelectorComponent_div_12_ng_template_13_Template", "ɵɵtemplateRefExtractor", "templateTypeOptions", "length", "noTemplates_r6", "detail_r7", "CLocation", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template", "i_r8", "<PERSON>art", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template", "getTemplateDetails", "item_r9", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template", "SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template", "CStatus", "noDetails_r10", "getConflictCount", "SpaceTemplateSelectorComponent_div_13_div_12_Template", "SpaceTemplateSelectorComponent_div_13_div_13_Template", "getSelectedItems", "hasConflicts", "SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener", "_r11", "previousStep", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r12", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r13", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "CTemplateType", "SpaceTemplate", "templateApplied", "currentStep", "selectedTemplateDetails", "Map", "getTemplateTypeList", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "resetSelections", "getDisplayName", "filter", "getSelectedTotalPrice", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "templateTypeName", "progressTexts", "config", "spaceId", "spaceName", "templateDetails", "totalPrice", "emit", "close", "reset", "template", "clear", "ɵɵdirectiveInject", "i1", "TemplateService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_button_click_4_listener", "SpaceTemplateSelectorComponent_div_12_Template", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_Template_button_click_19_listener", "SpaceTemplateSelectorComponent_button_21_Template", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef,\r\n  NbSelectModule\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  // 新增：模板類型選擇相關屬性\r\n  selectedTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  templateTypeOptions = EnumTemplateTypeHelper.getTemplateTypeList();\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 初始化選擇的模板類型\r\n    this.selectedTemplateType = this.CTemplateType;\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數，使用當前選擇的模板類型\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.selectedTemplateType,\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  // 新增：當模板類型選擇變更時的處理\r\n  onTemplateTypeChange() {\r\n    // 清空當前選擇\r\n    this.resetSelections();\r\n    // 重新載入對應類型的模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  // 新增：獲取當前選擇的模板類型名稱\r\n  getCurrentTemplateTypeName(): string {\r\n    return EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const progressTexts = {\r\n      1: `請選擇要套用的${templateTypeName}項目`,\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const templateTypeName = EnumTemplateTypeHelper.getDisplayName(this.selectedTemplateType);\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: templateTypeName,\r\n      selectedItems: this.getSelectedItems(),\r\n      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->\r\n<nb-card class=\"space-template-dialog\">\r\n  <nb-card-header class=\"space-template-header\">\r\n    <div class=\"space-template-title\">空間模板選擇器</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"space-template-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇模板</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認套用</div>\r\n    </div>\r\n\r\n    <!-- 步驟1: 選擇模板 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <div class=\"template-selection\">\r\n        <!-- 模板類型選擇器 -->\r\n        <div class=\"template-type-selector\">\r\n          <div class=\"selector-label\">\r\n            <nb-icon icon=\"options-2-outline\" class=\"mr-2\"></nb-icon>模板類型\r\n          </div>\r\n          <nb-select [(ngModel)]=\"selectedTemplateType\" (ngModelChange)=\"onTemplateTypeChange()\" placeholder=\"選擇模板類型\"\r\n            class=\"template-type-select\">\r\n            <nb-option *ngFor=\"let option of templateTypeOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇模板項目\r\n        </div>\r\n        <div class=\"template-list\">\r\n          <div *ngIf=\"templates.length > 0; else noTemplates\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <nb-checkbox [(ngModel)]=\"template.selected\" (ngModelChange)=\"onTemplateItemChange()\">\r\n                <div class=\"template-info\">\r\n                  <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                  <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n                  \r\n                  \r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noTemplates>\r\n            <div class=\"no-templates\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              暫無可用的{{ getCurrentTemplateTypeName() }}項目，請稍後再試或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認套用 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認套用詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將套用 <strong>{{ getCurrentTemplateTypeName() }}</strong>：{{ getSelectedItems().length }}個模板項目\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選中的模板詳情展開 -->\r\n        <div class=\"selected-templates-details\">\r\n          <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n            <div class=\"template-detail-header\">\r\n              <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n              <div class=\"template-meta\">\r\n                <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"template-detail-content\">\r\n              <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                <div class=\"detail-items-header\">\r\n                  <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                </div>\r\n                <div class=\"detail-items-list\">\r\n                  <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\" class=\"detail-item\">\r\n                    <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                    <div class=\"detail-info\">\r\n                      <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                      <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                        位置: {{ detail.CLocation }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ng-template #noDetails>\r\n                <div class=\"no-details\">\r\n                  <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n                  此模板暫無明細項目\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n          <div class=\"warning-text\">\r\n            <strong><nb-icon icon=\"alert-triangle-outline\" class=\"mr-1\"></nb-icon>衝突檢測：</strong>\r\n            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"space-template-footer\">\r\n    <div class=\"progress-info\">\r\n      <span>{{ getProgressText() }}</span>\r\n    </div>\r\n    <div class=\"step-buttons\">\r\n      <button nbButton status=\"basic\" (click)=\"close()\">取消</button>\r\n      <button *ngIf=\"currentStep > 1\" nbButton status=\"basic\" (click)=\"previousStep()\">上一步</button>\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\"\r\n        (click)=\"nextStep()\">下一步</button>\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" [disabled]=\"getSelectedItems().length === 0\"\r\n        (click)=\"applyTemplate()\">確認套用</button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,cAAc,QACT,gBAAgB;AAEvB,SAA8BC,uBAAuB,QAAuD,yBAAyB;AACrI,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;ICqBnFC,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF0CH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAC1EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;;IAUET,EADF,CAAAC,cAAA,cAA8D,sBAC0B;IAAzED,EAAA,CAAAU,gBAAA,2BAAAC,iGAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAZ,EAAA,CAAAmB,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACZ,EAAA,CAAAoB,UAAA,2BAAAT,iGAAA;MAAAX,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAAiBE,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEjFvB,EADF,CAAAC,cAAA,cAA2B,cACF;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAK3DF,EAL2D,CAAAG,YAAA,EAAM,EAGvD,EACM,EACV;;;;IARSH,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAwB,gBAAA,YAAAX,WAAA,CAAAK,QAAA,CAA+B;IAEjBlB,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAyB,iBAAA,CAAAZ,WAAA,CAAAa,aAAA,CAA4B;IAC5B1B,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,kBAAA,SAAAK,WAAA,CAAAc,WAAA,KAA8B;;;;;IAL7D3B,EAAA,CAAAC,cAAA,UAAoD;IAClDD,EAAA,CAAA4B,UAAA,IAAAC,2DAAA,kBAA8D;IAUhE7B,EAAA,CAAAG,YAAA,EAAM;;;;IAVsBH,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAS,SAAA,CAAY;;;;;IAYtC9B,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA+B,SAAA,kBAAoD;IACpD/B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oCAAAa,MAAA,CAAAW,0BAAA,4FACF;;;;;;IA/BFhC,EAJN,CAAAC,cAAA,cAAoD,cAClB,cAEM,cACN;IAC1BD,EAAA,CAAA+B,SAAA,kBAAyD;IAAA/B,EAAA,CAAAE,MAAA,gCAC3D;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,oBAC+B;IADpBD,EAAA,CAAAU,gBAAA,2BAAAuB,kFAAArB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAoB,GAAA;MAAA,MAAAb,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAiB,kBAAA,CAAAI,MAAA,CAAAc,oBAAA,EAAAvB,MAAA,MAAAS,MAAA,CAAAc,oBAAA,GAAAvB,MAAA;MAAA,OAAAZ,EAAA,CAAAmB,WAAA,CAAAP,MAAA;IAAA,EAAkC;IAACZ,EAAA,CAAAoB,UAAA,2BAAAa,kFAAA;MAAAjC,EAAA,CAAAc,aAAA,CAAAoB,GAAA;MAAA,MAAAb,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAAiBE,MAAA,CAAAe,oBAAA,EAAsB;IAAA,EAAC;IAEpFpC,EAAA,CAAA4B,UAAA,IAAAS,0DAAA,wBAA6E;IAIjFrC,EADE,CAAAG,YAAA,EAAY,EACR;IAENH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA+B,SAAA,kBAAsD;IAAA/B,EAAA,CAAAE,MAAA,6CACxD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAazBD,EAZA,CAAA4B,UAAA,KAAAU,qDAAA,kBAAoD,KAAAC,6DAAA,gCAAAvC,EAAA,CAAAwC,sBAAA,CAY1B;IAQhCxC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAhCWH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwB,gBAAA,YAAAH,MAAA,CAAAc,oBAAA,CAAkC;IAEbnC,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAoB,mBAAA,CAAsB;IAUhDzC,EAAA,CAAAO,SAAA,GAA4B;IAAAP,EAA5B,CAAAI,UAAA,SAAAiB,MAAA,CAAAS,SAAA,CAAAY,MAAA,KAA4B,aAAAC,cAAA,CAAgB;;;;;IAwDtC3C,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAoC,SAAA,CAAAC,SAAA,MACF;;;;;IALF7C,EADF,CAAAC,cAAA,cAAqG,cACzE;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEzCH,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAA4B,UAAA,IAAAkB,wEAAA,kBAAsD;IAI1D9C,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPsBH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAyB,iBAAA,CAAAsB,IAAA,KAAW;IAEV/C,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAyB,iBAAA,CAAAmB,SAAA,CAAAI,KAAA,CAAkB;IACbhD,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAwC,SAAA,CAAAC,SAAA,CAAsB;;;;;IAPxD7C,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IACzFF,EADyF,CAAAG,YAAA,EAAO,EAC1F;IACNH,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA4B,UAAA,IAAAqB,kEAAA,kBAAqG;IAUzGjD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAbyBH,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAQ,kBAAA,kBAAAa,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAAxB,WAAA,EAAAe,MAAA,0CAA4D;IAG/D1C,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAAxB,WAAA,EAA0C;;;;;IAapE3B,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA+B,SAAA,kBAAoD;IACpD/B,EAAA,CAAAE,MAAA,+DACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA7BRH,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErDH,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAExEF,EAFwE,CAAAG,YAAA,EAAO,EACvE,EACF;IAENH,EAAA,CAAAC,cAAA,cAAqC;IAkBnCD,EAjBA,CAAA4B,UAAA,KAAAwB,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAArD,EAAA,CAAAwC,sBAAA,CAiB3E;IAO5BxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IAhCwBH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAyB,iBAAA,CAAA0B,OAAA,CAAAzB,aAAA,CAAwB;IAEtB1B,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,kBAAA,SAAA2C,OAAA,CAAAxB,WAAA,KAA0B;IACtB3B,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAyB,iBAAA,CAAA0B,OAAA,CAAAG,OAAA,yCAAsC;IAKhEtD,EAAA,CAAAO,SAAA,GAAwD;IAAAP,EAAxD,CAAAI,UAAA,SAAAiB,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAAxB,WAAA,EAAAe,MAAA,KAAwD,aAAAa,aAAA,CAAc;;;;;IA6B9EvD,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA+B,SAAA,kBAA8D;IAAA/B,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpFH,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,yBAAAa,MAAA,CAAAmC,gBAAA,+JACF;;;;;IArDFxD,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA+B,SAAA,kBAAgE;IAAA/B,EAAA,CAAAE,MAAA,4CAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAAAH,EAAA,CAAAE,MAAA,IACzD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA4B,UAAA,KAAA6B,qDAAA,mBAA6E;IAmC/EzD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA4B,UAAA,KAAA8B,qDAAA,kBAAqD;IAOzD1D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlDcH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAyB,iBAAA,CAAAJ,MAAA,CAAAW,0BAAA,GAAkC;IAAShC,EAAA,CAAAO,SAAA,EACzD;IADyDP,EAAA,CAAAQ,kBAAA,WAAAa,MAAA,CAAAsC,gBAAA,GAAAjB,MAAA,oCACzD;IAKsB1C,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,YAAAiB,MAAA,CAAAsC,gBAAA,GAAqB;IAqCvC3D,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAiB,MAAA,CAAAuC,YAAA,GAAoB;;;;;;IAgB5B5D,EAAA,CAAAC,cAAA,iBAAiF;IAAzBD,EAAA,CAAAoB,UAAA,mBAAAyC,0EAAA;MAAA7D,EAAA,CAAAc,aAAA,CAAAgD,IAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAASE,MAAA,CAAA0C,YAAA,EAAc;IAAA,EAAC;IAAC/D,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC7FH,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAoB,UAAA,mBAAA4C,0EAAA;MAAAhE,EAAA,CAAAc,aAAA,CAAAmD,IAAA;MAAA,MAAA5C,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAASE,MAAA,CAAA6C,QAAA,EAAU;IAAA,EAAC;IAAClE,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADuBH,EAAA,CAAAI,UAAA,cAAAiB,MAAA,CAAA8C,UAAA,GAA0B;;;;;;IAEpFnE,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAoB,UAAA,mBAAAgD,0EAAA;MAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA;MAAA,MAAAhD,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAmB,WAAA,CAASE,MAAA,CAAAiD,aAAA,EAAe;IAAA,EAAC;IAACtE,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADmBH,EAAA,CAAAI,UAAA,aAAAiB,MAAA,CAAAsC,gBAAA,GAAAjB,MAAA,OAA4C;;;AD9F9G,OAAM,MAAO6B,8BAA8B;EAazCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAdV,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAW9E,gBAAgB,CAAC+E,aAAa;IACrD,KAAAC,eAAe,GAAG,IAAIzF,YAAY,EAAuB;IAEnE,KAAA0F,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAjD,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAkD,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;IAExE;IACA,KAAA9C,oBAAoB,GAAWrC,gBAAgB,CAAC+E,aAAa;IAC7D,KAAApC,mBAAmB,GAAG1C,sBAAsB,CAACmF,mBAAmB,EAAE;EAK9D;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAAChD,oBAAoB,GAAG,IAAI,CAACyC,aAAa;IAC9C;IACA,IAAI,CAACQ,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CT,aAAa,EAAE,IAAI,CAACzC,oBAAoB;MACxCmD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACf7D,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC+C,eAAe,CAACe,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAAChE,SAAS,GAAG8D,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACP9E,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACY,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDmE,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACnE,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAP,oBAAoBA,CAAA;IAClB;EAAA;EAGF;EACAa,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAC8D,eAAe,EAAE;IACtB;IACA,IAAI,CAACd,oBAAoB,EAAE;EAC7B;EAEA;EACApD,0BAA0BA,CAAA;IACxB,OAAOjC,sBAAsB,CAACoG,cAAc,CAAC,IAAI,CAAChE,oBAAoB,CAAC;EACzE;EAEAwB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC7B,SAAS,CAACsE,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAAC9E,QAAQ,CAAC;EACrD;EAEAmF,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAlC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACY,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACpB,gBAAgB,EAAE,CAACjB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAwB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACY,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACuB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACvB,WAAW,EAAE;IACpB;EACF;EAEAuB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAAC5C,gBAAgB,EAAE;IAE7C4C,aAAa,CAACC,OAAO,CAACR,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAACrE,WAAW,IAAI,CAAC,IAAI,CAACqD,uBAAuB,CAACyB,GAAG,CAACT,IAAI,CAACrE,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAAC+E,sBAAsB,CAACV,IAAI,CAACrE,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEA+E,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAClC,eAAe,CAACoC,yCAAyC,CAAC;MAC7DpB,IAAI,EAAEmB;KACP,CAAC,CAAClB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACd,uBAAuB,CAAC8B,GAAG,CAACH,UAAU,EAAEf,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACjB,uBAAuB,CAAC8B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAzD,kBAAkBA,CAACyD,UAAkB;IACnC,OAAO,IAAI,CAAC3B,uBAAuB,CAAC+B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEA5C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAiC,eAAeA,CAAA;IACb,MAAMC,gBAAgB,GAAGlH,sBAAsB,CAACoG,cAAc,CAAC,IAAI,CAAChE,oBAAoB,CAAC;IACzF,MAAM+E,aAAa,GAAG;MACpB,CAAC,EAAE,UAAUD,gBAAgB,IAAI;MACjC,CAAC,EAAE;KACJ;IACD,OAAOC,aAAa,CAAC,IAAI,CAACnC,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAnB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACjB,MAAM,GAAG,CAAC;EAC3C;EAEAc,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAM2C,gBAAgB,GAAGlH,sBAAsB,CAACoG,cAAc,CAAC,IAAI,CAAChE,oBAAoB,CAAC;IACzF,MAAMgF,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAEJ,gBAAgB;MAC3BV,aAAa,EAAE,IAAI,CAAC5C,gBAAgB,EAAE;MACtC2D,eAAe,EAAE,IAAIrC,GAAG,CAAC,IAAI,CAACD,uBAAuB,CAAC;MAAE;MACxDuC,UAAU,EAAE,IAAI,CAAClB,qBAAqB;KACvC;IAED,IAAI,CAACvB,eAAe,CAAC0C,IAAI,CAACL,MAAM,CAAC;IACjC,IAAI,CAACM,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACvB,eAAe,EAAE;IACtB,IAAI,CAACxB,SAAS,CAAC+C,KAAK,EAAE;EACxB;EAEA;EACA;EAEQC,KAAKA,CAAA;IACX,IAAI,CAAC3C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACjD,SAAS,GAAG,EAAE;EACrB;EAEQoE,eAAeA,CAAA;IACrB,IAAI,CAACnB,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACjD,SAAS,CAAC0E,OAAO,CAACmB,QAAQ,IAAG;MAChCA,QAAQ,CAACzG,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAAC8D,uBAAuB,CAAC4C,KAAK,EAAE;EACtC;;;uCAvMWrD,8BAA8B,EAAAvE,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9B1D,8BAA8B;MAAA2D,SAAA;MAAAC,MAAA;QAAAxD,WAAA;QAAAC,aAAA;MAAA;MAAAwD,OAAA;QAAAtD,eAAA;MAAA;MAAAuD,UAAA;MAAAC,QAAA,GAAAtI,EAAA,CAAAuI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAf,QAAA,WAAAgB,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxCvC5I,EAFJ,CAAAC,cAAA,iBAAuC,wBACS,aACV;UAAAD,EAAA,CAAAE,MAAA,iDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC/CH,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAoB,UAAA,mBAAA0H,gEAAA;YAAA,OAASD,GAAA,CAAApB,KAAA,EAAO;UAAA,EAAC;UACxDzH,EAAA,CAAA+B,SAAA,iBAAwC;UAE5C/B,EADE,CAAAG,YAAA,EAAS,EACM;UAKbH,EAHJ,CAAAC,cAAA,sBAA0C,aAElB,aAKjB;UAAAD,EAAA,CAAAE,MAAA,kCAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChBH,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAE,MAAA,mCAAO;UACZF,EADY,CAAAG,YAAA,EAAM,EACZ;UA6CNH,EA1CA,CAAA4B,UAAA,KAAAmH,8CAAA,mBAAoD,KAAAC,8CAAA,mBA0CA;UA2DtDhJ,EAAA,CAAAG,YAAA,EAAe;UAIXH,EAFJ,CAAAC,cAAA,0BAA8C,eACjB,YACnB;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAChC;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBAC0B;UAAlBD,EAAA,CAAAoB,UAAA,mBAAA6H,iEAAA;YAAA,OAASJ,GAAA,CAAApB,KAAA,EAAO;UAAA,EAAC;UAACzH,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI7DH,EAHA,CAAA4B,UAAA,KAAAsH,iDAAA,qBAAiF,KAAAC,iDAAA,qBAE1D,KAAAC,iDAAA,qBAEK;UAGlCpJ,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;UAjImBH,EAAA,CAAAO,SAAA,GAIrB;UAJqBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAqJ,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAA9D,WAAA,QAAA8D,GAAA,CAAA9D,WAAA,MAAA8D,GAAA,CAAA9D,WAAA,MAIrB;UACqB/E,EAAA,CAAAO,SAAA,GAIrB;UAJqBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAqJ,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAA9D,WAAA,QAAA8D,GAAA,CAAA9D,WAAA,MAAA8D,GAAA,CAAA9D,WAAA,MAIrB;UAIE/E,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAyI,GAAA,CAAA9D,WAAA,OAAuB;UA0CvB/E,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAyI,GAAA,CAAA9D,WAAA,OAAuB;UA+DrB/E,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAyB,iBAAA,CAAAoH,GAAA,CAAA7B,eAAA,GAAuB;UAIpBhH,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAAyI,GAAA,CAAA9D,WAAA,KAAqB;UACrB/E,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAAyI,GAAA,CAAA9D,WAAA,KAAqB;UAErB/E,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAyI,GAAA,CAAA9D,WAAA,OAAuB;;;qBDzGlCzF,YAAY,EAAAiK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZnK,WAAW,EAAAoK,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACXrK,YAAY,EAAAwI,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,mBAAA,EAAA/B,EAAA,CAAAgC,qBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EACZxK,cAAc,EAAAuI,EAAA,CAAAkC,iBAAA,EACdxK,YAAY,EAAAsI,EAAA,CAAAmC,eAAA,EACZxK,gBAAgB,EAAAqI,EAAA,CAAAoC,mBAAA,EAChBxK,cAAc,EAAAoI,EAAA,CAAAqC,iBAAA,EAAArC,EAAA,CAAAsC,iBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}