{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/space.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction SpacePickerComponent_nb_icon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-icon\", 27);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_nb_icon_9_Template_nb_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.searchKeyword = \"\";\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_nb_icon_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-icon\", 27);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_nb_icon_15_Template_nb_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.searchLocation = \"\";\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_nb_icon_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 28);\n  }\n}\nfunction SpacePickerComponent_nb_icon_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 29);\n  }\n}\nfunction SpacePickerComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"input\", 31);\n    i0.ɵɵlistener(\"change\", function SpacePickerComponent_div_27_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 32);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.allSelected);\n  }\n}\nfunction SpacePickerComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_32_Template_div_click_0_listener() {\n      const space_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSpaceSelection(space_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r6.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r6.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r6.CLocation || \"-\");\n  }\n}\nfunction SpacePickerComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"nb-icon\", 39);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpacePickerComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"ngx-pagination\", 41);\n    i0.ɵɵtwoWayListener(\"PageChange\", function SpacePickerComponent_div_34_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pageIndex, $event) || (ctx_r1.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function SpacePickerComponent_div_34_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r1.pageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r1.pageSize)(\"CollectionSize\", ctx_r1.totalRecords);\n  }\n}\nfunction SpacePickerComponent_div_35_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SpacePickerComponent_div_35_span_5_Template_button_click_2_listener() {\n      const space_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeSelectedSpace(space_r9));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r9.CPart, \" \");\n  }\n}\nfunction SpacePickerComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"h6\", 43);\n    i0.ɵɵelement(2, \"nb-icon\", 44);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtemplate(5, SpacePickerComponent_div_35_span_5_Template, 3, 1, \"span\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u7A7A\\u9593 (\", ctx_r1.selectedItems.length, \" \\u9805) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedItems);\n  }\n}\nexport let SpacePickerComponent = /*#__PURE__*/(() => {\n  class SpacePickerComponent {\n    constructor(spaceService) {\n      this.spaceService = spaceService;\n      this.selectedItems = [];\n      this.multiple = true;\n      this.placeholder = '請選擇空間';\n      this.selectionChange = new EventEmitter();\n      // 搜尋相關屬性\n      this.searchKeyword = '';\n      this.searchLocation = '';\n      this.isLoading = false;\n      // 分頁相關屬性\n      this.pageIndex = 1;\n      this.pageSize = 10;\n      this.totalRecords = 0;\n      // 資料相關屬性\n      this.availableSpaces = [];\n      this.allSelected = false;\n    }\n    ngOnInit() {\n      this.loadAvailableSpaces();\n    }\n    // 載入可用空間列表\n    loadAvailableSpaces() {\n      this.isLoading = true;\n      const request = {\n        CPart: this.searchKeyword || null,\n        CLocation: this.searchLocation || null,\n        CStatus: 1,\n        // 只顯示啟用的空間\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      this.spaceService.apiSpaceGetSpaceListPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID,\n            CPart: item.CPart,\n            CLocation: item.CLocation,\n            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n          this.updateAllSelectedState();\n        }\n        this.isLoading = false;\n      })).subscribe({\n        next: () => {},\n        error: () => {\n          this.isLoading = false;\n        }\n      });\n    }\n    // 搜尋功能\n    onSearch() {\n      this.pageIndex = 1;\n      this.loadAvailableSpaces();\n    }\n    // 重置搜尋\n    onReset() {\n      this.searchKeyword = '';\n      this.searchLocation = '';\n      this.pageIndex = 1;\n      this.loadAvailableSpaces();\n    }\n    // 切換空間選擇\n    toggleSpaceSelection(space) {\n      space.selected = !space.selected;\n      if (space.selected) {\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          if (this.multiple) {\n            this.selectedItems.push({\n              ...space\n            });\n          } else {\n            this.selectedItems = [{\n              ...space\n            }];\n            // 單選模式下，取消其他選項\n            this.availableSpaces.forEach(s => {\n              if (s.CSpaceID !== space.CSpaceID) {\n                s.selected = false;\n              }\n            });\n          }\n        }\n      } else {\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n      this.updateAllSelectedState();\n      this.selectionChange.emit([...this.selectedItems]);\n    }\n    // 全選/取消全選\n    toggleAllSpaces() {\n      this.allSelected = !this.allSelected;\n      this.availableSpaces.forEach(space => {\n        if (this.allSelected && !space.selected) {\n          space.selected = true;\n          if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n            this.selectedItems.push({\n              ...space\n            });\n          }\n        } else if (!this.allSelected && space.selected) {\n          space.selected = false;\n          this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n        }\n      });\n      this.selectionChange.emit([...this.selectedItems]);\n    }\n    // 移除已選空間\n    removeSelectedSpace(space) {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      // 更新可用列表中的選中狀態\n      const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n      if (availableSpace) {\n        availableSpace.selected = false;\n      }\n      this.updateAllSelectedState();\n      this.selectionChange.emit([...this.selectedItems]);\n    }\n    // 更新全選狀態\n    updateAllSelectedState() {\n      this.allSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n    }\n    // 分頁變更 - 與 ngx-pagination 組件兼容\n    onPageChange(page) {\n      this.pageIndex = page;\n      this.loadAvailableSpaces();\n    }\n    // 計算總頁數\n    get totalPages() {\n      return Math.ceil(this.totalRecords / this.pageSize);\n    }\n    static {\n      this.ɵfac = function SpacePickerComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpacePickerComponent)(i0.ɵɵdirectiveInject(i1.SpaceService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SpacePickerComponent,\n        selectors: [[\"app-space-picker\"]],\n        inputs: {\n          selectedItems: \"selectedItems\",\n          multiple: \"multiple\",\n          placeholder: \"placeholder\"\n        },\n        outputs: {\n          selectionChange: \"selectionChange\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 36,\n        vars: 20,\n        consts: [[1, \"space-picker-container\"], [1, \"search-section\"], [1, \"search-form\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"row\", \"g-2\", \"align-items-end\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"form-label\", \"mb-1\", \"text-muted\", \"small\"], [\"type\", \"text\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [\"nbSuffix\", \"\", \"icon\", \"close-outline\", \"class\", \"cursor-pointer text-muted\", \"title\", \"\\u6E05\\u9664\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"text\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"disabled\"], [1, \"col-lg-4\", \"col-md-2\"], [1, \"search-actions\", \"d-flex\", \"gap-1\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", \"title\", \"\\u91CD\\u7F6E\", 3, \"click\", \"disabled\"], [\"icon\", \"refresh-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", \"size\", \"small\", \"title\", \"\\u641C\\u5C0B\", 3, \"click\", \"disabled\"], [\"icon\", \"search-outline\", 4, \"ngIf\"], [\"icon\", \"loader-outline\", \"class\", \"spinning\", 4, \"ngIf\"], [1, \"d-none\", \"d-lg-inline\", \"ms-1\"], [1, \"space-list-section\", \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"font-weight-bold\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"selected-summary mt-3 p-3 bg-light border rounded\", 4, \"ngIf\"], [\"nbSuffix\", \"\", \"icon\", \"close-outline\", \"title\", \"\\u6E05\\u9664\", 1, \"cursor-pointer\", \"text-muted\", 3, \"click\"], [\"icon\", \"search-outline\"], [\"icon\", \"loader-outline\", 1, \"spinning\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", \"id\", \"selectAllSpaces\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllSpaces\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"font-weight-bold\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"selected-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"mb-2\"], [\"icon\", \"checkmark-circle-outline\", 1, \"text-success\", \"me-2\"], [1, \"selected-spaces-list\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", \"aria-label\", \"\\u79FB\\u9664\", 1, \"btn-close\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"]],\n        template: function SpacePickerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n            i0.ɵɵtext(6, \"\\u9805\\u76EE\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"nb-form-field\")(8, \"input\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_8_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(9, SpacePickerComponent_nb_icon_9_Template, 1, 0, \"nb-icon\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 4)(11, \"label\", 5);\n            i0.ɵɵtext(12, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"nb-form-field\")(14, \"input\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SpacePickerComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function SpacePickerComponent_Template_input_keyup_enter_14_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(15, SpacePickerComponent_nb_icon_15_Template, 1, 0, \"nb-icon\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 9)(17, \"div\", 10)(18, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_18_listener() {\n              return ctx.onReset();\n            });\n            i0.ɵɵelement(19, \"nb-icon\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function SpacePickerComponent_Template_button_click_20_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵtemplate(21, SpacePickerComponent_nb_icon_21_Template, 1, 0, \"nb-icon\", 14)(22, SpacePickerComponent_nb_icon_22_Template, 1, 0, \"nb-icon\", 15);\n            i0.ɵɵelementStart(23, \"span\", 16);\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(25, \"div\", 17)(26, \"div\", 18);\n            i0.ɵɵtemplate(27, SpacePickerComponent_div_27_Template, 4, 1, \"div\", 19)(28, SpacePickerComponent_div_28_Template, 2, 0, \"div\", 20);\n            i0.ɵɵelementStart(29, \"small\", 21);\n            i0.ɵɵtext(30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 22);\n            i0.ɵɵtemplate(32, SpacePickerComponent_div_32_Template, 6, 4, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(33, SpacePickerComponent_div_33_Template, 3, 0, \"div\", 24)(34, SpacePickerComponent_div_34_Template, 2, 3, \"div\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(35, SpacePickerComponent_div_35_Template, 6, 2, \"div\", 26);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.searchLocation);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.isLoading ? \"\\u641C\\u5C0B...\" : \"\\u641C\\u5C0B\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx.totalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx.pageIndex, \" / \", ctx.totalPages, \" \\u9801 \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.availableSpaces);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.availableSpaces.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbFormFieldModule, i4.NbFormFieldComponent, i4.NbSuffixDirective, NbInputModule, i4.NbInputDirective, NbButtonModule, i4.NbButtonComponent, NbIconModule, i4.NbIconComponent, PaginationComponent],\n        styles: [\"@charset \\\"UTF-8\\\";.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]{margin-bottom:1rem}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]{border:1px solid #e4e7ea;border-radius:6px;background-color:#f8f9fa}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500;color:#6c757d;margin-bottom:.25rem}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:32px;border-radius:4px;border:1px solid #dee2e6;background-color:#fff;font-size:.8rem;padding:.375rem .75rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .1rem #007bff1a}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled{background-color:#f8f9fa;border-color:#e9ecef;color:#6c757d;cursor:not-allowed}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#adb5bd;font-size:.8rem}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbSuffix][_ngcontent-%COMP%]{font-size:.9rem;opacity:.6;transition:opacity .2s ease}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   nb-icon[nbSuffix][_ngcontent-%COMP%]:hover{opacity:1}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:32px;border-radius:4px;font-size:.8rem;padding:.375rem .75rem;transition:all .15s ease}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[ghost][_ngcontent-%COMP%]{border:1px solid #dee2e6;background-color:#fff;color:#6c757d}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[ghost][_ngcontent-%COMP%]:hover:not(:disabled){border-color:#007bff;color:#007bff}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=primary][_ngcontent-%COMP%]{background-color:#007bff;border-color:#007bff}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[status=primary][_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0056b3;border-color:#0056b3}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:.9rem}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   nb-icon.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]{min-height:200px}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(5,1fr);gap:12px;margin-bottom:1rem}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]{cursor:pointer;transition:all .2s ease}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]{padding:12px;border:2px solid #e4e7ea;border-radius:8px;background-color:#fff;text-align:center;transition:all .2s ease}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%]{font-weight:600;font-size:.9rem;color:#2c3e50;margin-bottom:4px}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%]{font-size:.8rem;color:#7f8c8d}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%]{border-color:#4a90e2;box-shadow:0 2px 8px #4a90e226;transform:translateY(-1px)}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]{border-color:#28a745;background-color:#f8fff9;box-shadow:0 2px 12px #28a74533}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%]{color:#28a745}.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.5rem .75rem;font-size:.8rem;background-color:#007bff;color:#fff;border-radius:20px}.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]{background:none;border:none;color:#fff;opacity:.8;cursor:pointer;padding:0;margin:0;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover{opacity:1}.space-picker-container[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .selected-spaces-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:before{content:\\\"\\\\d7\\\";font-size:12px;line-height:1}@media (max-width: 1200px){.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(4,1fr)}}@media (max-width: 992px){.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(3,1fr)}}@media (max-width: 768px){.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%], .space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-5[_ngcontent-%COMP%]{margin-bottom:.5rem}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-2[_ngcontent-%COMP%]{margin-bottom:0}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{flex:1}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:8px}}@media (max-width: 576px){.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem!important}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.space-picker-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline!important}.space-picker-container[_ngcontent-%COMP%]   .space-list-section[_ngcontent-%COMP%]   .space-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}[_nghost-%COMP%]{display:block;width:100%}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer!important}\"]\n      });\n    }\n  }\n  return SpacePickerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}