import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbButtonModule, NbIconModule } from '@nebular/theme';
import { SpaceTemplateSelectorService, SpaceTemplateSelectorConfig } from './space-template-selector.service';
import { SpaceTemplateConfig } from './space-template-selector.component';
import { EnumTemplateType } from 'src/app/shared/enum/enumTemplateType';

@Component({
  selector: 'app-space-template-selector-button',
  standalone: true,
  imports: [CommonModule, NbButtonModule, NbIconModule],
  template: `
    <button
      type="button"
      [class]="buttonClass"
      (click)="openSelector()"
      [disabled]="disabled">
      <i *ngIf="icon" [class]="icon + ' mr-1'"></i>
      {{ text }}
    </button>
  `,
  styles: [`
    .mr-1 {
      margin-right: 0.25rem;
    }
  `]
})
export class SpaceTemplateSelectorButtonComponent {
  @Input() buildCaseId: string = '';
  @Input() CTemplateType: number = EnumTemplateType.CustomerChangeRequirement;
  @Input() text: string = '模板新增';
  @Input() icon: string = 'fas fa-layer-group';
  @Input() buttonClass: string = 'btn btn-warning mr-2';
  @Input() disabled: boolean = false;
  @Input() config: Partial<SpaceTemplateSelectorConfig> = {};

  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();
  @Output() beforeOpen = new EventEmitter<void>();
  @Output() error = new EventEmitter<string>();

  constructor(private spaceTemplateSelectorService: SpaceTemplateSelectorService) { }

  openSelector() {
    this.beforeOpen.emit();

    // 建立完整的配置
    const fullConfig: SpaceTemplateSelectorConfig = {
      buildCaseId: this.buildCaseId,
      buttonText: this.text,
      buttonIcon: this.icon,
      buttonClass: this.buttonClass,
      CTemplateType: this.CTemplateType,
      ...this.config
    };

    this.spaceTemplateSelectorService.openSelector(fullConfig)
      .subscribe({
        next: (result) => {
          if (result) {
            this.templateApplied.emit(result);
          }
        },
        error: (error) => {
          this.error.emit(error.message || '開啟模板選擇器時發生錯誤');
        }
      });
  }
}
